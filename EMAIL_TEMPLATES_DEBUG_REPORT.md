# Email Templates Page - Debug Report

## Vấn đề được báo cáo

1. **API được gọi 3 lần** cho endpoint `/marketing/template-emails?page=1&limit=10`
2. **Dữ liệu không hiển thị** trên bảng mặc dù API trả về thành công với 10 items

## Phân tích nguyên nhân

### 1. API được gọi nhiều lần

**Nguyên nhân chính:**
- **Query key không stable**: Mỗi lần `dataTable.queryParams` thay đổi, React Query coi đó là query key mới
- **useEffect thừa**: Code có useEffect không cần thiết gây re-render
- **Object reference thay đổi**: Query params object được tạo mới mỗi lần render

**Vị trí vấn đề:**
```typescript
// File: useEmailTemplatesAdapter.ts - Dòng 16 (trướ<PERSON> khi sửa)
list: (query: EmailTemplateQueryDto) => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists(), query] as const,
```

### 2. Dữ liệu không hiển thị

**Nguyên nhân chính:**
- **Conflict giữa Table component và useDataTable**:
  - Table nhận `data={templatesData?.items || []}`
  - useDataTable cũng cố gắng quản lý data qua `updateTableData`
- **useEffect thừa** gây confusion trong data flow

**Vị trí vấn đề:**
```typescript
// File: EmailTemplatesPage.tsx - Dòng 283-285 (đã xóa)
useEffect(() => {
  updateTableDataRef.current(templatesData, isLoading);
}, [templatesData, isLoading]);
```

## Giải pháp đã áp dụng

### 1. Sửa Query Key Structure

**Trước:**
```typescript
list: (query: EmailTemplateQueryDto) => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists(), query] as const,
```

**Sau:**
```typescript
list: (query: EmailTemplateQueryDto) => {
  // Tạo stable query key bằng cách serialize các giá trị quan trọng
  const stableQuery = {
    page: query.page || 1,
    limit: query.limit || 10,
    search: query.search || '',
    sortBy: query.sortBy || '',
    sortDirection: query.sortDirection || '',
    status: query.status || '',
  };
  return [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists(), stableQuery] as const;
},
```

### 2. Xóa useEffect thừa

**Đã xóa:**
```typescript
// Lưu trữ tham chiếu đến hàm updateTableData
const updateTableDataRef = React.useRef(dataTable.updateTableData);

// Cập nhật tham chiếu khi dataTable thay đổi
useEffect(() => {
  updateTableDataRef.current = dataTable.updateTableData;
}, [dataTable]);

// Cập nhật dữ liệu bảng với API data
useEffect(() => {
  updateTableDataRef.current(templatesData, isLoading);
}, [templatesData, isLoading]);
```

### 3. Sửa Pagination Props

**Trước:**
```typescript
pagination={{
  current: templatesData?.meta.currentPage || 1,
  pageSize: dataTable.tableData.pageSize,
  total: templatesData?.meta.totalItems || 0,
  onChange: dataTable.tableData.handlePageChange,
  // Missing onShowSizeChange
}}
```

**Sau:**
```typescript
pagination={{
  current: templatesData?.meta.currentPage || 1,
  pageSize: templatesData?.meta.itemsPerPage || 10,
  total: templatesData?.meta.totalItems || 0,
  onChange: dataTable.tableData.handlePageChange,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100],
  showFirstLastButtons: true,
  showPageInfo: true,
}}
```

### 4. Thêm Debug Logging

**Trong useEmailTemplatesAdapter:**
```typescript
console.log('🔍 useEmailTemplatesAdapter query key:', queryKey);
console.log('🔍 useEmailTemplatesAdapter query params:', query);
console.log('🚀 API call: EmailTemplateAdapterService.getEmailTemplates', query);
```

**Trong EmailTemplateAdapterService:**
```typescript
console.log('📥 EmailTemplateAdapterService.getEmailTemplates called with query:', query);
console.log('🔄 Converted to backend query:', backendQuery);
console.log('📤 Backend result:', { itemsCount, meta, firstItem });
console.log('✅ Final adapter result:', { itemsCount, meta, firstConvertedItem });
```

## Kết quả mong đợi

1. **API chỉ được gọi 1 lần** khi trang load
2. **Dữ liệu hiển thị đúng** trên bảng với 10 items
3. **Pagination hoạt động** bình thường
4. **Search và filter** hoạt động không gây gọi API thừa

## Cách kiểm tra

1. Mở Developer Tools > Console
2. Truy cập `/marketing/email/templates`
3. Kiểm tra:
   - Số lần API được gọi (chỉ 1 lần)
   - Dữ liệu hiển thị trên bảng
   - Pagination hoạt động
   - Search/filter không gây API call thừa

## Files đã thay đổi

1. `src/modules/marketing/pages/email/EmailTemplatesPage.tsx`
   - Xóa useEffect thừa
   - Sửa pagination props

2. `src/modules/marketing/hooks/email/useEmailTemplatesAdapter.ts`
   - Sửa query key structure
   - Thêm debug logging

3. `src/modules/marketing/services/email-template-adapter.service.ts`
   - Thêm debug logging

## Trạng thái

🔄 **ĐANG KIỂM TRA** - Các thay đổi đã được áp dụng:

1. ✅ Query key structure đã được tối ưu với stable keys
2. ✅ Xóa useEffect thừa gây re-render
3. ✅ Thêm useMemo cho stableQueryParams
4. ✅ Sửa pagination props
5. 🔄 Đang kiểm tra xem API có còn được gọi 3 lần không
6. 🔄 Đang kiểm tra xem dữ liệu có hiển thị đúng không

## Cách kiểm tra

1. **Mở Developer Tools > Console**
2. **Truy cập** `http://localhost:5176/marketing/email/templates`
3. **Kiểm tra Console logs:**
   - `🔍 [useEmailTemplatesAdapter] Called with query:` - Xem hook được gọi bao nhiều lần
   - `🔍 [useEmailTemplatesAdapter] Generated queryKey:` - Xem query key có stable không
   - `🚀 [API CALL] EmailTemplateAdapterService.getEmailTemplates` - Xem API được gọi bao nhiều lần
   - `📊 [EmailTemplatesPage] stableQueryParams:` - Xem query params có stable không

4. **Kiểm tra Network tab:**
   - Xem có bao nhiều request đến `/api/v1/marketing/template-emails`
   - Nếu > 1 request thì vẫn còn vấn đề

5. **Kiểm tra UI:**
   - Dữ liệu có hiển thị trên bảng không
   - Pagination có hoạt động không
   - Search/filter có gây API call thừa không

## Nếu vẫn còn vấn đề

Nếu API vẫn được gọi nhiều lần, có thể do:
1. **React Strict Mode** (development mode) - gọi hook 2 lần
2. **Component re-mount** - do routing hoặc parent component
3. **Query key vẫn chưa stable** - cần kiểm tra lại dependency array

## Lưu ý

- Debug logs sẽ được xóa sau khi xác nhận vấn đề đã được giải quyết
- Cần test kỹ pagination, search, và filter để đảm bảo không có regression
- Có thể áp dụng pattern tương tự cho các trang khác sử dụng useDataTable
