import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  ConditionalField,
  Divider,
  Typography,
  Chip,
  Icon,
} from '@/shared/components/common';
import SearchInputWithLazyLoading from '@/shared/components/common/SearchInputWithLazyLoading/SearchInputWithLazyLoading';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { createProductSchema } from '../../schemas/product.schema';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto
} from '../../types/product.types';
import { useCreateProduct } from '../../hooks/useProductQuery';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { CustomFieldService } from '../../services/custom-field.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { ProductService } from '../../services/product.service';

interface ProductFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  value: Record<string, unknown>;
}



// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  variants?: ProductVariant[];
}

// Interface cho biến thể sản phẩm trong form
interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  customFields: SelectedCustomField[];
}

/**
 * Form tạo sản phẩm mới
 */
const ProductForm: React.FC<ProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createProduct, isPending } = useCreateProduct();



  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // State cho biến thể sản phẩm
  const [productVariants, setProductVariants] = useState<ProductVariant[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload file với TaskQueue
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload product media',
    autoAddToQueue: true,
  });



  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    try {
      const formValues = values as ProductFormValues;
      setIsUploading(true);

      // Không còn sử dụng customFields riêng, chỉ sử dụng trong biến thể

      // Chuyển đổi giá trị form thành dữ liệu API
      const productData: CreateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: getPriceData(formValues),
        description: formValues.description,
        tags: tempTags, // Sử dụng tempTags thay vì formValues.tags
        shipmentConfig: formValues.shipmentConfig ? {
          lengthCm: Number(formValues.shipmentConfig.lengthCm) || undefined,
          widthCm: Number(formValues.shipmentConfig.widthCm) || undefined,
          heightCm: Number(formValues.shipmentConfig.heightCm) || undefined,
          weightGram: Number(formValues.shipmentConfig.weightGram) || undefined,
        } : undefined,
        // Không còn sử dụng customFields riêng, chỉ sử dụng trong biến thể
        classifications: productVariants.length > 0 ? productVariants.map(variant => ({
          name: variant.name,
          listPrice: Number(variant.listPrice),
          salePrice: Number(variant.salePrice),
          currency: variant.currency,
          customFields: variant.customFields.map(field => ({
            fieldId: field.fieldId,
            value: field.value,
          })),
        })) : undefined,
      };

      // Gọi API tạo sản phẩm
      await createProduct(productData as unknown as Record<string, unknown>);

      // Upload media nếu có
      if (mediaFiles.length > 0) {
        try {
          // Tạo danh sách promises cho việc upload từng file
          const uploadPromises = mediaFiles.map(async (fileData) => {
            // Lấy presigned URL từ API
            const presignedData = await ProductService.getPresignedUrl(fileData.file.type);

            // Upload file lên presigned URL với TaskQueue
            return fileUploadWithQueue.uploadToUrlWithQueue({
              file: fileData.file,
              presignedUrl: presignedData.result.uploadUrl,
              taskTitle: `Upload: ${fileData.file.name}`,
              taskDescription: `Kích thước: ${(fileData.file.size / 1024).toFixed(1)} KB`,
              onUploadProgress: (progress) => {
                setUploadProgress(progress);
              },
            });
          });

          // Chờ tất cả các file upload xong
          await Promise.all(uploadPromises);
        } catch (uploadError) {
          console.error('Error uploading media:', uploadError);
          NotificationUtil.warning({
            message: t('business:product.mediaUploadError'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.createSuccess'),
        duration: 3000,
      });

      // Gọi callback onSubmit
      onSubmit(values as Record<string, unknown>);
    } catch (error) {
      console.error('Error creating product:', error);
      setIsUploading(false);
      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE && values.listPrice && values.salePrice) {
      return {
        listPrice: Number(values.listPrice),
        salePrice: Number(values.salePrice),
        currency: values.currency || 'VND',
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE && values.priceDescription) {
      return {
        priceDescription: values.priceDescription,
      };
    }
    return null;
  };

  // Hàm load trường tùy chỉnh
  const loadCustomFields = async (search: string, pagination?: { page: number; limit: number }) => {
    try {
      const response = await CustomFieldService.getCustomFields({
        search,
        page: pagination?.page || 1,
        limit: pagination?.limit || 10,
      });

      return response.result.items.map(item => ({
        id: item.id,
        name: item.label,
        description: item.type,
        image: null,
      }));
    } catch (error) {
      console.error('Error loading custom fields:', error);
      return [];
    }
  };





  // Thêm biến thể mới
  const handleAddVariant = () => {
    const newVariant: ProductVariant = {
      id: Date.now(), // ID tạm thời cho UI
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      customFields: [],
    };

    setProductVariants([...productVariants, newVariant]);

    // Cập nhật giá trị vào form
    if (formRef.current) {
      formRef.current.getFormMethods().setValue('variants', [...productVariants, newVariant], { shouldValidate: true });
    }
  };

  // Xóa biến thể
  const handleRemoveVariant = (variantId: number) => {
    const updatedVariants = productVariants.filter(variant => variant.id !== variantId);
    setProductVariants(updatedVariants);

    // Cập nhật giá trị vào form
    if (formRef.current) {
      formRef.current.getFormMethods().setValue('variants', updatedVariants, { shouldValidate: true });
    }
  };

  // Cập nhật biến thể
  const handleUpdateVariant = (variantId: number, field: string, value: string | number) => {
    const updatedVariants = productVariants.map(variant => {
      if (variant.id === variantId) {
        return { ...variant, [field]: value };
      }
      return variant;
    });

    setProductVariants(updatedVariants);

    // Cập nhật giá trị vào form
    if (formRef.current) {
      formRef.current.getFormMethods().setValue('variants', updatedVariants, { shouldValidate: true });
    }
  };

  // Thêm trường tùy chỉnh vào biến thể
  const handleAddCustomFieldToVariant = (variantId: number, fieldId: number) => {
    const updatedVariants = productVariants.map(variant => {
      if (variant.id === variantId) {
        // Kiểm tra xem trường đã tồn tại trong biến thể chưa
        const existingField = variant.customFields.find(field => field.fieldId === fieldId);
        if (existingField) {
          return variant; // Nếu đã tồn tại, không thêm nữa
        }

        // Thêm trường mới vào biến thể
        return {
          ...variant,
          customFields: [
            ...variant.customFields,
            {
              id: Date.now(), // ID tạm thời
              fieldId,
              value: { value: '' }, // Giá trị mặc định
            }
          ]
        };
      }
      return variant;
    });

    setProductVariants(updatedVariants);

    // Cập nhật giá trị vào form
    if (formRef.current) {
      formRef.current.getFormMethods().setValue('variants', updatedVariants, { shouldValidate: true });
    }
  };

  // Xóa trường tùy chỉnh khỏi biến thể
  const handleRemoveCustomFieldFromVariant = (variantId: number, customFieldId: number) => {
    const updatedVariants = productVariants.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.filter(field => field.id !== customFieldId)
        };
      }
      return variant;
    });

    setProductVariants(updatedVariants);

    // Cập nhật giá trị vào form
    if (formRef.current) {
      formRef.current.getFormMethods().setValue('variants', updatedVariants, { shouldValidate: true });
    }
  };

  // Cập nhật giá trị trường tùy chỉnh trong biến thể
  const handleUpdateCustomFieldInVariant = (variantId: number, customFieldId: number, value: string) => {
    const updatedVariants = productVariants.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.map(field => {
            if (field.id === customFieldId) {
              return {
                ...field,
                value: { value }
              };
            }
            return field;
          })
        };
      }
      return variant;
    });

    setProductVariants(updatedVariants);

    // Cập nhật giá trị vào form
    if (formRef.current) {
      formRef.current.getFormMethods().setValue('variants', updatedVariants, { shouldValidate: true });
    }
  };



  // Giá trị mặc định cho form
  const defaultValues = {
    name: '',
    typePrice: PriceTypeEnum.HAS_PRICE,
    listPrice: '',
    salePrice: '',
    currency: 'VND',
    priceDescription: '',
    description: '',
    tags: [],
    shipmentConfig: {
      lengthCm: '',
      widthCm: '',
      heightCm: '',
      weightGram: '',
    },
    customFields: [],
    media: [],
    variants: [],
  };

  return (
    <Card title={t('business:product.form.createTitle')}>
      <Form
        ref={formRef}
        schema={createProductSchema(t)}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="p-4 space-y-4"
      >
        <FormItem
          name="name"
          label={t('business:product.name')}
          required
        >
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>

        <FormItem
          name="typePrice"
          label={t('business:product.priceType.title')}
          required
        >
          <Select
            fullWidth
            options={[
              { value: PriceTypeEnum.HAS_PRICE, label: t('business:product.priceType.hasPrice') },
              { value: PriceTypeEnum.STRING_PRICE, label: t('business:product.priceType.stringPrice') },
              { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
            ]}
          />
        </FormItem>

        {/* Hiển thị các trường giá dựa trên loại giá */}
        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.HAS_PRICE,
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="listPrice"
              label={t('business:product.listPrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="salePrice"
              label={t('business:product.salePrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="currency"
              label={t('business:product.currency')}
              required
            >
              <Select
                fullWidth
                options={[
                  { value: 'VND', label: 'VND' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' },
                ]}
              />
            </FormItem>
          </div>
        </ConditionalField>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.STRING_PRICE,
          }}
        >
          <FormItem
            name="priceDescription"
            label={t('business:product.priceDescription')}
            required
          >
            <Input fullWidth placeholder={t('business:product.form.priceDescriptionPlaceholder')} />
          </FormItem>
        </ConditionalField>

        <FormItem
          name="description"
          label={t('business:product.form.description')}
        >
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>

        {/* Upload media */}
        <FormItem
          name="media"
          label={t('business:product.form.media')}
        >
          <MultiFileUpload
            mediaOnly={true}
            accept="image/*,video/*"
            placeholder={t('business:product.form.mediaPlaceholder', 'Kéo thả hoặc click để tải lên ảnh/video')}
            onChange={(files) => {
              setMediaFiles(files);
              if (formRef.current) {
                formRef.current.getFormMethods().setValue('media', files, { shouldValidate: true });
              }
            }}
            value={mediaFiles}
          />
        </FormItem>

        <FormItem
          name="tags"
          label={t('business:product.tags')}
        >
          <div className="space-y-2">
            <Input
              fullWidth
              placeholder={t('business:product.form.tagsPlaceholder')}
              onKeyDown={e => {
                if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                  e.preventDefault();

                  // Lấy tag mới
                  const newTag = e.currentTarget.value.trim();

                  // Thêm tag mới nếu chưa tồn tại
                  if (!tempTags.includes(newTag)) {
                    setTempTags(prev => [...prev, newTag]);
                  }

                  e.currentTarget.value = '';
                }
              }}
            />
            <div className="flex flex-wrap gap-1 mt-2">
              {tempTags.map((tag, tagIndex) => (
                <Chip
                  key={`tag-${tagIndex}-${tag}`}
                  size="sm"
                  closable
                  onClose={() => {
                    setTempTags(prev => prev.filter(t => t !== tag));
                  }}
                >
                  {tag}
                </Chip>
              ))}
            </div>
          </div>
        </FormItem>

        {/* Cấu hình vận chuyển */}
        <Divider className="my-4" />
        <Typography variant="h6" className="mb-4">
          {t('business:product.form.shipmentConfig.title', 'Cấu hình vận chuyển')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="shipmentConfig.widthCm"
            label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.heightCm"
            label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.lengthCm"
            label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.weightGram"
            label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
        </div>

        {/* Phần biến thể sản phẩm */}
        <Divider className="my-4" />
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h6">
            {t('business:product.form.variants.title', 'Biến thể sản phẩm')}
          </Typography>
          <Button
            size="sm"
            variant="primary"
            onClick={handleAddVariant}
          >
            <Icon name="plus" className="mr-1" size="sm" />
            {t('business:product.form.variants.addVariant', 'Thêm biến thể')}
          </Button>
        </div>

        {/* Danh sách biến thể */}
        {productVariants.length > 0 ? (
          <div className="space-y-6">
            {productVariants.map((variant, index) => (
              <div key={variant.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="subtitle1" className="font-medium">
                    {t('business:product.form.variants.variant', 'Biến thể')} #{index + 1}
                  </Typography>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveVariant(variant.id)}
                  >
                    <Icon name="trash" size="sm" className="text-red-500" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormItem
                    label={t('business:product.form.variants.name', 'Tên biến thể')}
                  >
                    <Input
                      fullWidth
                      value={variant.name}
                      onChange={(e) => handleUpdateVariant(variant.id, 'name', e.target.value)}
                      placeholder={t('business:product.form.variants.namePlaceholder', 'Nhập tên biến thể')}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                  >
                    <Select
                      fullWidth
                      value={variant.currency}
                      onChange={(val) => handleUpdateVariant(variant.id, 'currency', val as string)}
                      options={[
                        { value: 'VND', label: 'VND' },
                        { value: 'USD', label: 'USD' },
                        { value: 'EUR', label: 'EUR' },
                      ]}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.listPrice}
                      onChange={(e) => handleUpdateVariant(variant.id, 'listPrice', e.target.value)}
                      placeholder="0"
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.salePrice', 'Giá bán')}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.salePrice}
                      onChange={(e) => handleUpdateVariant(variant.id, 'salePrice', e.target.value)}
                      placeholder="0"
                    />
                  </FormItem>
                </div>

                {/* Trường tùy chỉnh cho biến thể */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <Typography variant="subtitle2">
                      {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                    </Typography>
                  </div>

                  <div className="mb-4">
                    <SearchInputWithLazyLoading
                      loadOptions={loadCustomFields}
                      onChange={(id) => {
                        const fieldId = Number(id);
                        if (!fieldId) return;
                        handleAddCustomFieldToVariant(variant.id, fieldId);
                      }}
                      placeholder={t('business:product.form.variants.searchCustomField', 'Tìm thuộc tính')}
                      showSearchIcon={true}
                      loadInitialOptions={true}
                    />
                  </div>

                  {variant.customFields.length > 0 && (
                    <div className="space-y-3">
                      {variant.customFields.map((field) => (
                        <div key={field.id} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                          <div className="flex items-center justify-between mb-2">
                            <Typography variant="caption" className="font-medium">
                              {field.fieldId}
                            </Typography>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRemoveCustomFieldFromVariant(variant.id, field.id)}
                            >
                              <Icon name="trash" size="xs" className="text-red-500" />
                            </Button>
                          </div>
                          <Input
                            fullWidth
                            value={field.value.value as string || ''}
                            onChange={(e) => handleUpdateCustomFieldInVariant(variant.id, field.id, e.target.value)}
                            placeholder={t('business:product.form.variants.valuePlaceholder', 'Nhập giá trị')}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:product.form.variants.noVariants', 'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.')}
            </Typography>
          </div>
        )}



        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isPending || isUploading}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isPending || isUploading}
          >
            {isUploading ? t('business:product.uploading') : t('common:save')}
          </Button>
        </div>

        {/* Hiển thị tiến trình upload nếu đang upload */}
        {isUploading && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
              <div
                className="bg-primary-600 h-2.5 rounded-full"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <Typography variant="caption" className="mt-1 text-center block">
              {t('business:product.uploadingMedia', 'Đang tải lên media...')} {uploadProgress.toFixed(0)}%
            </Typography>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default ProductForm;
