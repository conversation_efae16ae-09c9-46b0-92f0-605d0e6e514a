import React, { useState } from 'react';
import { Card, Table, ActionMenu, Loading } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useFolders } from '../hooks/useFolderQuery';
import { FolderResponseDto, FolderStatus } from '../types/folder.types';
import { formatTimestamp } from '@/shared/utils/date';

// Define ActionMenuItem interface locally since import is not available
interface ActionMenuItem {
  id: string;
  label: string;
  icon: string;
  onClick: () => void;
}

/**
 * Trang quản lý thư mục cho Admin
 */
const FolderPage: React.FC = () => {

  // State cho filter và pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  // Sử dụng hook để lấy dữ liệu thư mục
  const { data: foldersResponse, isLoading, error } = useFolders({
    page,
    limit,
    search: searchTerm || undefined,
    status: filter !== 'all' ? (filter as FolderStatus) : undefined,
  });

  // Lấy dữ liệu từ API response
  const folders = foldersResponse?.result?.items || [];
  const totalItems = foldersResponse?.result?.meta?.totalItems || 0;

  // Định nghĩa cột cho bảng
  const columns: TableColumn<FolderResponseDto>[] = [
    {
      key: 'name',
      title: 'Tên thư mục',
      dataIndex: 'name',
      sortable: true,
    },
    {
      key: 'description',
      title: 'Mô tả',
      dataIndex: 'description',
      render: (value: unknown) => {
        const description = value as string | undefined;
        return description || 'Không có mô tả';
      },
    },
    {
      key: 'parent',
      title: 'Thư mục cha',
      dataIndex: 'parent',
      render: (value: unknown) => {
        const parent = value as { name: string } | undefined;
        return parent?.name || 'Thư mục gốc';
      },
    },
    {
      key: 'fileCount',
      title: 'Số file',
      dataIndex: 'fileCount',
      render: (value: unknown) => {
        const count = value as number | undefined;
        return count || 0;
      },
    },
    {
      key: 'subFolderCount',
      title: 'Số thư mục con',
      dataIndex: 'subFolderCount',
      render: (value: unknown) => {
        const count = value as number | undefined;
        return count || 0;
      },
    },
    {
      key: 'status',
      title: 'Trạng thái',
      dataIndex: 'status',
      render: (value: unknown) => {
        const status = value as FolderStatus;
        const statusMap = {
          [FolderStatus.ACTIVE]: { text: 'Hoạt động', color: 'success' },
          [FolderStatus.INACTIVE]: { text: 'Không hoạt động', color: 'secondary' },
        };

        return <span className={`text-${statusMap[status]?.color || 'secondary'}`}>{statusMap[status]?.text || status}</span>;
      },
    },
    {
      key: 'owner',
      title: 'Người sở hữu',
      dataIndex: 'owner',
      render: (value: unknown) => {
        const owner = value as { name: string; email?: string } | undefined;
        return owner ? (
          <div>
            <div>{owner.name}</div>
            {owner.email && <div className="text-sm text-gray-500">{owner.email}</div>}
          </div>
        ) : 'Không xác định';
      },
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      render: (value: unknown) => {
        return formatTimestamp(value);
      },
    },
    {
      key: 'actions',
      title: 'Thao tác',
      render: (_, record) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'view',
            label: 'Xem chi tiết',
            icon: 'eye',
            onClick: () => console.log('View folder:', record.id),
          },
          {
            id: 'open',
            label: 'Mở thư mục',
            icon: 'folder-open',
            onClick: () => console.log('Open folder:', record.id),
          },
          {
            id: 'delete',
            label: 'Xóa',
            icon: 'trash-2',
            onClick: () => console.log('Delete folder:', record.id),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip="Thao tác"
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ];

  // Xử lý thay đổi trang
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        items={[
          {
            id: 'all',
            label: 'Tất cả',
            icon: 'list',
            onClick: () => setFilter('all'),
          },
          {
            id: 'active',
            label: 'Hoạt động',
            icon: 'check-circle',
            onClick: () => setFilter(FolderStatus.ACTIVE),
          },
          {
            id: 'inactive',
            label: 'Không hoạt động',
            icon: 'eye-off',
            onClick: () => setFilter(FolderStatus.INACTIVE),
          },
        ]}
      />

      {isLoading ? (
        <Loading />
      ) : error ? (
        <Card>
          <div className="p-4 text-center text-red-500">
            Có lỗi xảy ra khi tải dữ liệu thư mục
          </div>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <Table
            columns={columns}
            data={folders}
            rowKey="id"
            pagination={{
              current: page,
              pageSize: limit,
              total: totalItems,
              onChange: handlePageChange,
            }}
          />
        </Card>
      )}
    </div>
  );
};

export default FolderPage;
