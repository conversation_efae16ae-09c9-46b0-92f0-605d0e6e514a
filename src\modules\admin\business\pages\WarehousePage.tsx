import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, Loading } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { formatTimestamp } from '@/shared/utils/date';
import { usePhysicalWarehousesAdmin } from '../hooks/usePhysicalWarehouseAdminQuery';
import {
  PhysicalWarehouseAdminDto,
  QueryPhysicalWarehouseAdminDto,
} from '../types/physical-warehouse.types';

// Define ActionMenuItem interface locally since import is not available
interface ActionMenuItem {
  id: string;
  label: string;
  icon: string;
  onClick: () => void;
}

/**
 * Trang quản lý kho vật lý cho Admin
 */
const WarehousePage: React.FC = () => {
  const { t } = useTranslation(['common', 'business']);

  // State cho pagination và sorting
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho filter và search
  const [searchTerm, setSearchTerm] = useState('');
  const [addressFilter, setAddressFilter] = useState('');
  const [capacityFilter, setCapacityFilter] = useState<{
    min?: number;
    max?: number;
  }>({});

  // State cho active filters
  const [, setActiveFilters] = useState<Array<{ key: string; label: string; value: string }>>([]);

  // Clear filters function
  const clearFilters = useCallback(() => {
    setActiveFilters([]);
  }, []);

  // Tạo query parameters
  const queryParams: QueryPhysicalWarehouseAdminDto = useMemo(() => {
    const params: QueryPhysicalWarehouseAdminDto = {
      page,
      limit,
      sortBy: sortBy as 'warehouseId' | 'address' | 'capacity' | 'createdAt',
      sortDirection,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (addressFilter) {
      params.address = addressFilter;
    }

    if (capacityFilter.min !== undefined) {
      params.minCapacity = capacityFilter.min;
    }

    if (capacityFilter.max !== undefined) {
      params.maxCapacity = capacityFilter.max;
    }

    return params;
  }, [page, limit, sortBy, sortDirection, searchTerm, addressFilter, capacityFilter]);

  // Fetch data
  const { data: warehousesResponse, isLoading, error } = usePhysicalWarehousesAdmin(queryParams);

  // Lấy dữ liệu từ API response
  const warehouses = warehousesResponse?.result?.items || [];
  const totalItems = warehousesResponse?.result?.meta?.totalItems || 0;

  // Xử lý actions
  const handleView = useCallback((record: PhysicalWarehouseAdminDto) => {
    console.log('View warehouse:', record.warehouseId);
    // TODO: Navigate to detail page
  }, []);

  const handleEdit = useCallback((record: PhysicalWarehouseAdminDto) => {
    console.log('Edit warehouse:', record.warehouseId);
    // TODO: Open edit form
  }, []);

  const handleDelete = useCallback((record: PhysicalWarehouseAdminDto) => {
    console.log('Delete warehouse:', record.warehouseId);
    // TODO: Show delete confirmation
  }, []);

  // Định nghĩa cột cho bảng
  const columns: TableColumn<PhysicalWarehouseAdminDto>[] = useMemo(() => [
    {
      key: 'warehouseId',
      title: t('business:warehouse.id'),
      dataIndex: 'warehouseId',
      sortable: true,
      width: '10%',
    },
    {
      key: 'name',
      title: t('business:warehouse.name'),
      sortable: true,
      width: '20%',
      render: (_, record) => record.warehouse?.name || t('common:notSet'),
    },
    {
      key: 'description',
      title: t('business:warehouse.description'),
      width: '25%',
      render: (_, record) => {
        const description = record.warehouse?.description;
        return description || t('common:noDescription');
      },
    },
    {
      key: 'address',
      title: t('business:warehouse.address'),
      dataIndex: 'address',
      width: '25%',
    },
    {
      key: 'capacity',
      title: t('business:warehouse.capacity'),
      dataIndex: 'capacity',
      width: '10%',
      render: (value: unknown) => {
        const capacity = value as number | null;
        return capacity ? capacity.toLocaleString() : t('common:notSet');
      },
    },
    {
      key: 'createdAt',
      title: t('common:createdAt'),
      sortable: true,
      width: '15%',
      render: (_, record) => formatTimestamp(record.warehouse?.createdAt),
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '15%',
      render: (_, record) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'view',
            label: t('common:view'),
            icon: 'eye',
            onClick: () => handleView(record),
          },
          {
            id: 'edit',
            label: t('common:edit'),
            icon: 'edit',
            onClick: () => handleEdit(record),
          },
          {
            id: 'delete',
            label: t('common:delete'),
            icon: 'trash-2',
            onClick: () => handleDelete(record),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('common:moreActions')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ], [t, handleView, handleEdit, handleDelete]);

  // Create query params function
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): QueryPhysicalWarehouseAdminDto => {
    const queryParams: QueryPhysicalWarehouseAdminDto = {
      page: params.page,
      limit: params.pageSize,
      sortBy: params.sortBy as 'warehouseId' | 'address' | 'capacity' | 'createdAt' || 'createdAt',
      sortDirection: params.sortDirection || SortDirection.DESC,
    };

    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }

    return queryParams;
  }, []);

  // Data table configuration
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
    showDateFilter: false,
  }));

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Xử lý thay đổi sorting
  const handleSortChange = useCallback((column: string | null, order: 'asc' | 'desc' | null) => {
    if (column && order) {
      setSortBy(column);
      setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
      setPage(1); // Reset về trang đầu khi sort
    }
  }, []);

  // Xử lý search
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setPage(1); // Reset về trang đầu khi search
  }, []);

  // Menu items cho filter
  const menuItems = useMemo(() => [
    {
      id: 'all',
      label: t('common:all'),
      icon: 'list',
      onClick: () => {
        setAddressFilter('');
        setCapacityFilter({});
        clearFilters();
        setPage(1);
      },
    },
    {
      id: 'high-capacity',
      label: t('business:warehouse.highCapacity'),
      icon: 'trending-up',
      onClick: () => {
        setCapacityFilter({ min: 1000 });
        setActiveFilters([{ key: 'capacity', label: t('business:warehouse.highCapacity'), value: '≥1000' }]);
        setPage(1);
      },
    },
    {
      id: 'low-capacity',
      label: t('business:warehouse.lowCapacity'),
      icon: 'trending-down',
      onClick: () => {
        setCapacityFilter({ max: 1000 });
        setActiveFilters([{ key: 'capacity', label: t('business:warehouse.lowCapacity'), value: '≤1000' }]);
        setPage(1);
      },
    },
  ], [t, clearFilters, setActiveFilters]);

  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar
        onSearch={handleSearch}
        items={menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {isLoading ? (
        <Loading />
      ) : error ? (
        <Card>
          <div className="p-4 text-center text-red-500">
            {t('common:errorLoadingData')}
          </div>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <Table
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={warehouses}
            rowKey="warehouseId"
            sortable
            onSortChange={handleSortChange}
            pagination={{
              current: page,
              pageSize: limit,
              total: totalItems,
              onChange: handlePageChange,
            }}
          />
        </Card>
      )}
    </div>
  );
};

export default WarehousePage;
