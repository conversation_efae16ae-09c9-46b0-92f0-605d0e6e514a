import { Suspense, lazy } from 'react';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import { RouteObject } from 'react-router-dom';

// Lazy load pages
const BusinessPage = lazy(() => import('../pages/BusinessPage'));
const ProductPage = lazy(() => import('../pages/ProductPage'));
const ConversionPage = lazy(() => import('../pages/ConversionPage'));
const OrderPage = lazy(() => import('../pages/OrderPage'));
const WarehousePage = lazy(() => import('../pages/WarehousePage'));
const CustomFieldPage = lazy(() => import('../pages/CustomFieldPage'));
const FilePage = lazy(() => import('../pages/FilePage'));
const FolderPage = lazy(() => import('../pages/FolderPage'));
const WarehouseCustomFieldPage = lazy(() => import('../pages/WarehouseCustomFieldPage'));

/**
 * Business module routes for Admin
 */
export const businessAdminRoutes: RouteObject[] = [
  // Trang tổng quan
  {
    path: '/admin/business',
    element: (
      <MainLayout title="Quản lý kinh doanh - Admin">
        <Suspense fallback={<Loading />}>
          <BusinessPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang sản phẩm
  {
    path: '/admin/business/product',
    element: (
      <MainLayout title="Quản lý sản phẩm - Admin">
        <Suspense fallback={<Loading />}>
          <ProductPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chuyển đổi
  {
    path: '/admin/business/conversion',
    element: (
      <MainLayout title="Quản lý chuyển đổi - Admin">
        <Suspense fallback={<Loading />}>
          <ConversionPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang đơn hàng
  {
    path: '/admin/business/order',
    element: (
      <MainLayout title="Quản lý đơn hàng - Admin">
        <Suspense fallback={<Loading />}>
          <OrderPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang kho vật lý
  {
    path: '/admin/business/warehouse',
    element: (
      <MainLayout title="Quản lý kho - Admin">
        <Suspense fallback={<Loading />}>
          <WarehousePage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang trường tùy chỉnh
  {
    path: '/admin/business/custom-field',
    element: (
      <MainLayout title="Quản lý trường tùy chỉnh - Admin">
        <Suspense fallback={<Loading />}>
          <CustomFieldPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang trường tùy chỉnh kho
  {
    path: '/admin/business/warehouse-custom-field',
    element: (
      <MainLayout title="Quản lý trường tùy chỉnh kho - Admin">
        <Suspense fallback={<Loading />}>
          <WarehouseCustomFieldPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý file
  {
    path: '/admin/business/file',
    element: (
      <MainLayout title="Quản lý file - Admin">
        <Suspense fallback={<Loading />}>
          <FilePage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý thư mục
  {
    path: '/admin/business/folder',
    element: (
      <MainLayout title="Quản lý thư mục - Admin">
        <Suspense fallback={<Loading />}>
          <FolderPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default businessAdminRoutes;
