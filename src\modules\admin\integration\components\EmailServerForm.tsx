import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Card, Typography, Button, Input, Textarea, Form, FormItem } from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { emailServerConfigurationSchema } from '../email/schemas';
import { EmailServerConfiguration, TestEmailServerDto } from '../email/types';
import { useTestEmailServer } from '../email/hooks';

// Định nghĩa kiểu dữ liệu cho form
export type EmailServerFormValues = z.infer<typeof emailServerConfigurationSchema>;

interface EmailServerFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: EmailServerConfiguration | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Form tạo/chỉnh sửa Email Server Configuration
 */
const EmailServerForm: React.FC<EmailServerFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [showTestForm, setShowTestForm] = useState(false);
  const [testData, setTestData] = useState<TestEmailServerDto>({
    recipientEmail: '',
    subject: 'Test Email Connection',
  });

  const testEmailMutation = useTestEmailServer();
  const isEditMode = !!initialData;

  // Chuẩn bị giá trị mặc định cho form
  const defaultValues = initialData
    ? {
        serverName: initialData.serverName,
        host: initialData.host,
        port: initialData.port,
        username: initialData.username,
        password: '', // Không hiển thị password cũ
        useSsl: initialData.useSsl,
        useStartTls: initialData.useStartTls,
        additionalSettings: initialData.additionalSettings
          ? JSON.stringify(initialData.additionalSettings, null, 2)
          : '',
        isActive: initialData.isActive,
      }
    : {
        serverName: '',
        host: '',
        port: 587,
        username: '',
        password: '',
        useSsl: false,
        useStartTls: true,
        additionalSettings: '',
        isActive: true,
      };

  // Xử lý submit form
  const handleFormSubmit = (values: Record<string, unknown>) => {
    // Parse additionalSettings từ JSON string nếu có
    const processedValues = {
      ...values,
      additionalSettings: values.additionalSettings
        ? JSON.parse(values.additionalSettings as string)
        : undefined,
    };
    onSubmit(processedValues);
  };

  const handleTestConnection = async () => {
    if (!initialData?.id) {
      return;
    }

    await testEmailMutation.mutateAsync({
      id: initialData.id,
      data: testData,
    });
  };

  return (
    <Card
      title={readOnly
        ? t('admin:integration.email.form.view', 'Xem Email Server')
        : isEditMode
        ? t('admin:integration.email.form.edit', 'Chỉnh sửa Email Server')
        : t('admin:integration.email.form.create', 'Tạo Email Server')}
     
    >
      <Form
        schema={emailServerConfigurationSchema}
        onSubmit={handleFormSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
      >

   
          <FormItem
            name="serverName"
            label={t('admin:integration.email.form.fields.serverName')}
            required
          >
            <Input
              placeholder={t('admin:integration.email.form.placeholders.serverName')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="host"
            label={t('admin:integration.email.form.fields.host')}
            required
          >
            <Input
              placeholder={t('admin:integration.email.form.placeholders.host')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="port"
            label={t('admin:integration.email.form.fields.port')}
            required
          >
            <Input
              type="number"
              placeholder={t('admin:integration.email.form.placeholders.port')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="username"
            label={t('admin:integration.email.form.fields.username')}
            required
          >
            <Input
              type="email"
              placeholder={t('admin:integration.email.form.placeholders.username')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="password"
            label={t('admin:integration.email.form.fields.password')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder={t('admin:integration.email.form.placeholders.password')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* SSL/TLS Settings */}
          <div className="flex space-x-8">
            <FormItem
              name="useSsl"
              label={t('admin:integration.email.form.fields.useSsl')}
            >
              <Toggle
                disabled={readOnly || isSubmitting}
                label=""
              />
            </FormItem>

            <FormItem
              name="useStartTls"
              label={t('admin:integration.email.form.fields.useStartTls')}
            >
              <Toggle
                disabled={readOnly || isSubmitting}
                label=""
              />
            </FormItem>

            <FormItem
              name="isActive"
              label={t('admin:integration.email.form.fields.isActive')}
            >
              <Toggle
                disabled={readOnly || isSubmitting}
                label=""
              />
            </FormItem>
          </div>
   

        {/* Additional Settings */}
        <FormItem
          name="additionalSettings"
          label={t('admin:integration.email.form.fields.additionalSettings')}
        >
          <Textarea
            placeholder={t('admin:integration.email.form.placeholders.additionalSettings')}
            disabled={readOnly || isSubmitting}
            rows={4}
            fullWidth
          />
        </FormItem>

        {/* Test Connection Section */}
        {isEditMode && !readOnly && (
          <Card className="p-4 bg-gray-50 dark:bg-gray-700">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h6">
                {t('admin:integration.email.form.test', 'Kiểm tra kết nối')}
              </Typography>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowTestForm(!showTestForm)}
              >
                {showTestForm ? t('common:hide') : t('admin:integration.email.actions.test')}
              </Button>
            </div>

            {showTestForm && (
              <div className="space-y-4">
                <Input
                  type="email"
                  label={t('admin:integration.email.form.fields.recipientEmail')}
                  placeholder={t('admin:integration.email.form.placeholders.recipientEmail')}
                  value={testData.recipientEmail}
                  onChange={(e) => setTestData({ ...testData, recipientEmail: e.target.value })}
                  fullWidth
                />
                <Input
                  label={t('admin:integration.email.form.fields.subject')}
                  placeholder={t('admin:integration.email.form.placeholders.subject')}
                  value={testData.subject}
                  onChange={(e) => setTestData({ ...testData, subject: e.target.value })}
                  fullWidth
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleTestConnection}
                  isLoading={testEmailMutation.isPending}
                  disabled={!testData.recipientEmail}
                >
                  {t('admin:integration.email.actions.test')}
                </Button>
              </div>
            )}
          </Card>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('admin:integration.email.actions.cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              {t('admin:integration.email.actions.save')}
            </Button>
          )}
        </div>
      </Form>
    </Card>
  );
};

export default EmailServerForm;
