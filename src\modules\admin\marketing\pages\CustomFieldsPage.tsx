import React, { useMemo, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Table,
  Card,
  Loading,
  Modal,
  Button,
  Typography,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import CustomFieldForm from '../components/forms/CustomFieldForm';
import EditCustomFieldForm from '../components/forms/EditCustomFieldForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  CustomField,
  CustomFieldType,
  CustomFieldQueryParams,
  CreateCustomFieldRequest,
} from '../types/custom-field.types';
import { CustomFieldService } from '../services/custom-field.service';
import { useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useCreateCustomField, useCustomFields, useDeleteCustomField } from '../hooks';

/**
 * Custom Fields Management Page
 * Allows administrators to manage custom data fields
 */
const CustomFieldsPage: React.FC = () => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  // Khởi tạo các mutation hooks
  const createCustomFieldMutation = useCreateCustomField();
  const deleteCustomFieldMutation = useDeleteCustomField();

  // State quản lý fieldKey của trường đang chỉnh sửa
  const [editingFieldId, setEditingFieldId] = useState<string | null>(null);



  // Không sử dụng hook useUpdateCustomField nữa vì chúng ta gọi API trực tiếp
  // Tạo một đối tượng giả để tương thích với useEffect
  const updateCustomFieldMutation = useMemo(
    () => ({
      isSuccess: false,
      reset: () => {},
    }),
    []
  );

  // State quản lý trạng thái hiển thị Modal xác nhận xóa
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    visible: boolean;
    fieldId: string | null;
    fieldName: string | null;
  }>({
    visible: false,
    fieldId: null,
    fieldName: null,
  });

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (fieldKey: string) => {
      console.log('Edit custom field', fieldKey);
      // Lưu fieldKey của trường đang chỉnh sửa
      setEditingFieldId(fieldKey);
      // Hiển thị form chỉnh sửa
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý hiển thị xác nhận xóa
  const handleDelete = useCallback((fieldKey: string, name: string) => {
    // Hiển thị Modal xác nhận
    setDeleteConfirmation({
      visible: true,
      fieldId: fieldKey,
      fieldName: name,
    });
  }, []);

  // Xử lý xác nhận xóa
  const confirmDelete = useCallback(() => {
    if (deleteConfirmation.fieldId !== null) {
      deleteCustomFieldMutation.mutate(deleteConfirmation.fieldId);
      // Ẩn Modal sau khi xóa
      setDeleteConfirmation({
        visible: false,
        fieldId: null,
        fieldName: null,
      });
    }
  }, [deleteConfirmation.fieldId, deleteCustomFieldMutation]);

  // Xử lý hủy xóa
  const cancelDelete = useCallback(() => {
    setDeleteConfirmation({
      visible: false,
      fieldId: null,
      fieldName: null,
    });
  }, []);

  // Schema đã được chuyển vào component CustomFieldForm

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<CustomField>[]>(
    () => [
      { key: 'createdBy', title: t('common:id', 'ID'), dataIndex: 'createdBy', width: '10%', sortable: true },
      {
        key: 'displayName',
        title: t('marketingAdmin:customFields.displayName', 'Display Name'),
        dataIndex: 'displayName',
        width: '20%',
        sortable: true,
      },
      {
        key: 'fieldKey',
        title: t('marketingAdmin:customFields.fieldKey', 'Field Key'),
        dataIndex: 'fieldKey',
        width: '15%',
        sortable: true,
      },
      {
        key: 'dataType',
        title: t('common:dataType', 'Kiểu dữ liệu'),
        dataIndex: 'dataType',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const dataType = value as string;
          let displayText = dataType;

          // Display friendly name for data type
          switch (dataType) {
            case CustomFieldType.TEXT:
              displayText = t('marketingAdmin:customFields.types.text');
              break;
            case CustomFieldType.NUMBER:
              displayText = t('marketingAdmin:customFields.types.number');
              break;
            case CustomFieldType.DATE:
              displayText = t('marketingAdmin:customFields.types.date');
              break;
            case CustomFieldType.BOOLEAN:
              displayText = t('marketingAdmin:customFields.types.boolean');
              break;
            case 'integer':
              displayText = t('marketingAdmin:customFields.types.integer');
              break;
          }

          return <span>{displayText}</span>;
        },
      },
      {
        key: 'description',
        title: t('common:description', 'Mô tả'),
        dataIndex: 'description',
        width: '20%',
        sortable: true,
      },

      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: CustomField) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record.fieldKey),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDelete(record.fieldKey, record.displayName),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit, handleDelete]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [{ id: 'all', label: t('common:all'), icon: 'list', value: 'all' }],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): CustomFieldQueryParams => {
    const queryParams: CustomFieldQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Log tìm kiếm để debug
    if (params.searchTerm) {
      console.log('Search term applied:', params.searchTerm);
    }

    // Status filter removed

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<CustomField, CustomFieldQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Sử dụng hooks từ API với queryParams từ dataTable
  const { data: customFieldData, isLoading } = useCustomFields(dataTable.queryParams);

  // Theo dõi kết quả mutation để refetch dữ liệu
  useEffect(() => {
    if (
      createCustomFieldMutation.isSuccess ||
      updateCustomFieldMutation.isSuccess ||
      deleteCustomFieldMutation.isSuccess
    ) {
      // Reset các trạng thái mutation để tránh refetch liên tục
      if (createCustomFieldMutation.isSuccess) createCustomFieldMutation.reset();
      if (updateCustomFieldMutation.isSuccess) updateCustomFieldMutation.reset();
      if (deleteCustomFieldMutation.isSuccess) deleteCustomFieldMutation.reset();
    }
  }, [createCustomFieldMutation, updateCustomFieldMutation, deleteCustomFieldMutation]);

  // Chỉ log khi cần debug
  // useEffect(() => {
  //   console.log('API Response - customFieldData:', customFieldData);
  // }, [customFieldData]);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,

    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form thêm mới
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateCustomFieldRequest theo cấu trúc API mới
    const customFieldData = {
      fieldKey: values.fieldKey as string,
      displayName: values.displayName as string,
      dataType: values.dataType as CustomFieldType,
      description: values.description as string | undefined,
    };

    console.log('Submitting custom field data:', customFieldData);

    // Gọi API để tạo custom field mới
    createCustomFieldMutation.mutate(customFieldData as CreateCustomFieldRequest);
    hideAddForm();
  };

  // Lấy queryClient để invalidate queries
  const queryClient = useQueryClient();

  // Xử lý submit form chỉnh sửa
  const handleEditSubmit = (fieldKey: string, values: Record<string, unknown>) => {
    // Đảm bảo fieldKey hợp lệ
    if (!fieldKey) {
      console.error('Invalid fieldKey for update:', fieldKey);
      return;
    }

    // Chuyển đổi values thành UpdateCustomFieldRequest theo cấu trúc API
    const updateData = {
      displayName: values.displayName as string,
      dataType: values.dataType as CustomFieldType,
      description: values.description as string | undefined,
    };

    console.log(`Updating custom field ${fieldKey} with data:`, updateData);

    // Gọi API trực tiếp từ service thay vì dùng hook
    // Điều này đảm bảo chúng ta luôn sử dụng fieldKey chính xác từ tham số
    CustomFieldService.updateCustomField(fieldKey, updateData)
      .then(data => {
        // Hiển thị thông báo thành công
        NotificationUtil.success({
          message: data.message || 'Cập nhật trường tùy chỉnh thành công',
        });

        // Invalidate queries để refresh dữ liệu
        queryClient.invalidateQueries({
          queryKey: ['marketing', 'custom-fields'],
          exact: false,
          refetchType: 'active',
        });
      })
      .catch((error: AxiosError<{ message: string }>) => {
        // Hiển thị thông báo lỗi
        NotificationUtil.error({
          message: error.response?.data?.message || 'Cập nhật trường tùy chỉnh thất bại',
        });
        console.error('Error updating custom field:', error);
      });

    hideEditForm();
    setEditingFieldId(null);
  };

  // Xử lý hủy form thêm mới
  const handleCancel = () => {
    hideAddForm();
  };

  // Xử lý hủy form chỉnh sửa
  const handleEditCancel = () => {
    hideEditForm();
    setEditingFieldId(null);
  };

  return (
    <div>
      {/* Modal xác nhận xóa - Đã cập nhật để tránh hiển thị lỗi */}
      {deleteConfirmation.visible && (
        <Modal
          isOpen={true}
          onClose={cancelDelete}
          size="sm"
          title={t('common:confirmDelete')}
          footer={
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={cancelDelete}>
                {t('common:cancel')}
              </Button>
              <Button variant="primary" onClick={confirmDelete}>
                {t('common:delete')}
              </Button>
            </div>
          }
        >
          <div className="text-center py-4">
            <Typography variant="body1" className="mb-6">
              {t('marketingAdmin:customFields.deleteConfirmation', { name: deleteConfirmation.fieldName })}
            </Typography>
          </div>
        </Modal>
      )}

      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <CustomFieldForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      {/* Form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {editingFieldId && (
          <EditCustomFieldForm
            id={editingFieldId}
            onSubmit={handleEditSubmit}
            onCancel={handleEditCancel}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        {isLoading ? (
          <div className="p-4 min-h-[300px] flex items-center justify-center">
            <Loading className="rounded-lg" />
          </div>
        ) : (
          <>
            {/* Bỏ console.log để tránh re-render không cần thiết */}

            {/* Xử lý dữ liệu để đảm bảo cấu trúc phù hợp với Table */}
            {(() => {
              // Chuẩn bị dữ liệu cho bảng
              let tableData: CustomField[] = [];
              let meta = {
                page: dataTable.tableData.currentPage,
                total: 0,
                limit: dataTable.tableData.pageSize,
                totalPages: 1,
              };

              // Xử lý dữ liệu từ API
              if (customFieldData) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const data = customFieldData as any;

                // Xác định dữ liệu bảng
                if (data.items && Array.isArray(data.items)) {
                  tableData = data.items;
                  console.log('Using data.items:', tableData);
                } else if (data.data && Array.isArray(data.data)) {
                  tableData = data.data;
                  console.log('Using data.data:', tableData);
                } else if (Array.isArray(data)) {
                  tableData = data;
                  console.log('Using array data directly:', tableData);
                }

                // Đảm bảo mỗi item có trường id (sử dụng createdBy nếu không có id)
                tableData = tableData.map(item => {
                  // Sử dụng type assertion để xử lý các trường có thể thiếu
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const anyItem = item as any;
                  if (!anyItem.id) {
                    return { ...anyItem, id: anyItem.fieldKey };
                  }
                  return item;
                });

                // Xác định thông tin phân trang
                if (data.meta) {
                  meta = {
                    page: data.meta.page || dataTable.tableData.currentPage,
                    total: data.meta.total || 0,
                    limit: data.meta.limit || dataTable.tableData.pageSize,
                    totalPages: data.meta.totalPages || 1,
                  };
                  console.log('Using meta from data.meta:', meta);
                }

                // Bỏ log thông tin phân trang để tránh re-render
              }

              return (
                <Table
                  columns={dataTable.columnVisibility.visibleTableColumns}
                  data={tableData}
                  rowKey="fieldKey"
                  loading={false} // Tắt loading của Table để tránh giật
                  sortable={true}
                  onSortChange={dataTable.tableData.handleSortChange}
                  pagination={{
                    current: meta.page,
                    pageSize: meta.limit,
                    total: meta.total,
                    onChange: dataTable.tableData.handlePageChange,
                    showSizeChanger: true,
                    pageSizeOptions: [10, 20, 50, 100],
                    showFirstLastButtons: true,
                    showPageInfo: true,
                  }}
                />
              );
            })()}
          </>
        )}
      </Card>
    </div>
  );
};

export default CustomFieldsPage;
