import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Button, Select, Icon, Textarea, Typography, Checkbox } from '@/shared/components/common';
import { SubmitHandler, useFormContext } from 'react-hook-form';
import { ProductCategory, Product } from '../../types/product.types';

// Định nghĩa type cho form values
export interface EditProductFormValues {
  name: string;
  description?: string;
  image?: string;
  listedPrice?: number;
  discountedPrice?: number;
  category: ProductCategory;
  detail?: string;
  userManual?: string;
}

/**
 * Component con để handle textarea với form context
 */
interface FormTextareaProps {
  name: string;
  label: string;
  placeholder: string;
  originalValue: string;
  onChangeDetected: (hasChanged: boolean) => void;
}

const FormTextarea: React.FC<FormTextareaProps> = ({
  name,
  label,
  placeholder,
  originalValue,
  onChangeDetected
}) => {
  const { setValue } = useFormContext<EditProductFormValues>();
  const [displayValue, setDisplayValue] = useState<string>(''); // Giá trị hiển thị trong textarea
  const [originalContent, setOriginalContent] = useState<string>(''); // Nội dung gốc để so sánh
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const { t } = useTranslation(['admin', 'common']);

  // Fetch nội dung từ CDN URL chỉ một lần khi component mount
  useEffect(() => {
    if (isInitialized) return;

    const fetchContentFromUrl = async (url: string) => {
      if (!url) {
        console.log(`❌ [${name}] Empty URL provided`);
        return '';
      }

      console.log(`🔍 [${name}] ==================== FETCHING ====================`);
      console.log(`🔍 [${name}] Input URL:`, url);

      // Làm sạch URL - giữ nguyên query parameters
      const cleanUrl = url.trim();
      console.log(`🔍 [${name}] Clean URL:`, cleanUrl);

      try {
        setIsLoading(true);
        console.log(`🔍 [${name}] Starting fetch request...`);

        // Thêm timeout 10 giây
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(cleanUrl, {
          signal: controller.signal,
          method: 'GET',
          headers: {
            'Accept': 'text/plain, text/html, */*',
          },
        });

        clearTimeout(timeoutId);

        console.log(`🔍 [${name}] Response status:`, response.status);
        console.log(`🔍 [${name}] Response headers:`, Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          const content = await response.text();
          console.log(`✅ [${name}] ✅ FETCH SUCCESS!`);
          console.log(`✅ [${name}] Content length:`, content.length);
          console.log(`✅ [${name}] Content preview:`, content.substring(0, 200) + '...');
          return content;
        } else {
          console.error(`❌ [${name}] ❌ HTTP ERROR:`, response.status, response.statusText);
          const errorText = await response.text();
          console.error(`❌ [${name}] Error response:`, errorText.substring(0, 200));
          return '';
        }
      } catch (error) {
        console.error(`❌ [${name}] ❌ FETCH EXCEPTION:`, error);
        if (error instanceof Error) {
          console.error(`❌ [${name}] Error name:`, error.name);
          console.error(`❌ [${name}] Error message:`, error.message);
        }
        return '';
      } finally {
        setIsLoading(false);
        console.log(`🔍 [${name}] ==================== FETCH END ====================`);
      }
    };

    const initializeContent = async () => {
      let content = '';

      console.log(`🔍 [${name}] ==================== INITIALIZING ====================`);
      console.log(`🔍 [${name}] originalValue:`, originalValue);
      console.log(`🔍 [${name}] originalValue type:`, typeof originalValue);
      console.log(`🔍 [${name}] originalValue length:`, originalValue?.length);
      console.log(`🔍 [${name}] Is URL?:`, originalValue?.startsWith?.('https://cdn.redai.vn/'));
      console.log(`🔍 [${name}] Contains cdn.redai.vn?:`, originalValue?.includes?.('cdn.redai.vn'));

      // Kiểm tra nếu originalValue là URL CDN - LOGIC MẠNH HƠN
      const isUrl = originalValue && originalValue.length > 10 && (
        originalValue.startsWith('https://cdn.redai.vn/') ||
        originalValue.includes('cdn.redai.vn') ||
        (originalValue.startsWith('https://') && originalValue.includes('expires=')) // Presigned URL
      );

      console.log(`🔍 [${name}] isUrl decision:`, isUrl);

      if (isUrl) {
        console.log(`🔍 [${name}] ✅ DETECTED URL - FETCHING CONTENT...`);
        content = await fetchContentFromUrl(originalValue);

        // Nếu fetch thất bại, fallback về originalValue
        if (!content || content.trim() === '') {
          console.log(`⚠️ [${name}] ❌ FETCH FAILED - using originalValue as fallback`);
          content = originalValue || '';
        } else {
          console.log(`✅ [${name}] ✅ FETCH SUCCESS - content length:`, content.length);
        }
      } else {
        // Nếu không phải URL, sử dụng trực tiếp
        console.log(`🔍 [${name}] ❌ NOT URL - using originalValue directly`);
        content = originalValue || '';
      }

      console.log(`✅ [${name}] Final content:`, content.substring(0, 100) + '...');

      setOriginalContent(content);
      setDisplayValue(content);
      setValue(name as keyof EditProductFormValues, content as string);

      // Reset change detection khi khởi tạo
      console.log(`🔄 [${name}] RESET change detection to FALSE on initialization`);
      onChangeDetected(false);
      setIsInitialized(true);
    };

    initializeContent();
  }, [originalValue, name, setValue, isInitialized, onChangeDetected, t]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setDisplayValue(newValue); // Cập nhật giá trị hiển thị
    setValue(name as keyof EditProductFormValues, newValue as string); // Cập nhật form state

    // Check if changed from original content (not URL)
    const hasChanged = newValue.trim() !== originalContent.trim();
    console.log(`📝 [${name}] CHANGE DETECTION:`, {
      hasChanged,
      originalLength: originalContent.length,
      newLength: newValue.length,
      originalPreview: originalContent.substring(0, 50) + '...',
      newPreview: newValue.substring(0, 50) + '...',
      originalTrimmed: originalContent.trim().substring(0, 30),
      newTrimmed: newValue.trim().substring(0, 30)
    });

    onChangeDetected(hasChanged);
  };

  return (
    <FormItem name={name} label={label}>
      <Textarea
        rows={4}
        placeholder={isLoading ? t('common:loading', 'Đang tải nội dung...') : placeholder}
        fullWidth
        value={displayValue} // Sử dụng displayValue thay vì currentValue từ form
        onChange={handleChange}
        disabled={isLoading}
      />
    </FormItem>
  );
};

interface EditProductFormProps {
  product: Product;
  onSubmit: (values: EditProductFormValues & {
    imageFiles?: File[]; // Thay đổi để hỗ trợ nhiều ảnh
    oldImageKeys?: string[]; // Keys của ảnh cũ (để reference)
    deletedImageKeys?: string[]; // Keys của ảnh bị xóa (để gửi DELETE operations)
    hasImageChanged?: boolean; // Flag để biết ảnh có thay đổi không
    uploadedImageUrls?: string[]; // URLs của ảnh đã upload thành công
    hasDetailChanged?: boolean; // Flag để biết detail có thay đổi không
    hasUserManualChanged?: boolean; // Flag để biết userManual có thay đổi không
    publishAfterUpdate?: boolean; // Flag để biết có publish sau khi update không
  }) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form chỉnh sửa sản phẩm cho admin
 */
const EditProductForm: React.FC<EditProductFormProps> = ({
  product,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho image upload - hỗ trợ nhiều ảnh
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>(product?.images || []);
  const [hasImageChanged, setHasImageChanged] = useState<boolean>(false);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [deletedImageKeys, setDeletedImageKeys] = useState<string[]>([]); // Track ảnh bị xóa

  // Track mapping giữa preview và key để tránh lỗi index
  const [imageKeyMapping, setImageKeyMapping] = useState<Map<string, string>>(new Map());

  // Track thay đổi detail và userManual
  const [hasDetailChanged, setHasDetailChanged] = useState<boolean>(false);
  const [hasUserManualChanged, setHasUserManualChanged] = useState<boolean>(false);
  const originalDetail = product?.detail || '';
  const originalUserManual = product?.userManual || '';

  // State cho publish after update
  const [publishAfterUpdate, setPublishAfterUpdate] = useState<boolean>(false);

  // Debug state changes
  useEffect(() => {
    console.log(`🔍 [EDIT_FORM] hasDetailChanged changed to:`, hasDetailChanged);
  }, [hasDetailChanged]);

  useEffect(() => {
    console.log(`🔍 [EDIT_FORM] hasUserManualChanged changed to:`, hasUserManualChanged);
  }, [hasUserManualChanged]);

  // Khởi tạo mapping khi component mount
  useEffect(() => {
    if (product?.images) {
      const mapping = new Map<string, string>();
      product.images.forEach((imageUrl: string, index: number) => {
        // Tạo key giả từ URL để mapping
        const key = `image-${index}-${imageUrl.split('/').pop()}`;
        mapping.set(imageUrl, key);
      });
      setImageKeyMapping(mapping);
      console.log('🔍 [EDIT_FORM] Image key mapping:', mapping);
    }
  }, [product?.images]);

  // Xử lý khi thêm ảnh mới
  const handleAddImage = (file: File, dataUrl: string) => {
    setImageFiles(prev => [...prev, file]);
    setImagePreviews(prev => [...prev, dataUrl]);
    setHasImageChanged(true);
  };

  // Xử lý khi xóa ảnh
  const handleRemoveImage = (index: number) => {
    console.log('🔍 [REMOVE IMAGE] Starting removal for index:', index);
    console.log('🔍 [REMOVE IMAGE] Current imagePreviews:', imagePreviews);
    console.log('🔍 [REMOVE IMAGE] Current imageFiles:', imageFiles);

    // Lấy URL của ảnh đang xóa
    const imageUrl = imagePreviews[index];
    console.log('🔍 [REMOVE IMAGE] Image URL to remove:', imageUrl);

    // Kiểm tra xem có phải ảnh cũ không bằng cách tìm trong mapping
    const correspondingKey = imageKeyMapping.get(imageUrl);
    if (correspondingKey) {
      console.log('🔍 [REMOVE IMAGE] Removing OLD image with key:', correspondingKey);
      setDeletedImageKeys(prev => {
        const newDeleted = [...prev, correspondingKey];
        console.log('🗑️ [REMOVE IMAGE] Updated deletedImageKeys:', newDeleted);
        return newDeleted;
      });
      // Xóa khỏi mapping
      setImageKeyMapping(prev => {
        const newMapping = new Map(prev);
        newMapping.delete(imageUrl);
        return newMapping;
      });
    } else {
      console.log('🔍 [REMOVE IMAGE] Removing NEW image (no key found)');
    }

    setImageFiles(prev => {
      const newFiles = prev.filter((_, i) => i !== index);
      console.log('🔍 [REMOVE IMAGE] Updated imageFiles:', newFiles);
      return newFiles;
    });
    setImagePreviews(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      console.log('🔍 [REMOVE IMAGE] Updated imagePreviews:', newPreviews);
      return newPreviews;
    });
    setUploadedImageUrls(prev => prev.filter((_, i) => i !== index));
    setHasImageChanged(true);
  };

  // Xử lý khi submit form
  const handleFormSubmit: SubmitHandler<EditProductFormValues> = values => {
    console.log('🔍 [EditProductForm] Form submitted with values:', values);
    console.log('🔍 [EditProductForm] Image files:', imageFiles);
    console.log('🔍 [EditProductForm] Has detail changed:', hasDetailChanged);
    console.log('🔍 [EditProductForm] Has userManual changed:', hasUserManualChanged);
    console.log('🔍 [EditProductForm] Publish after update:', publishAfterUpdate);

    onSubmit({
      ...values,
      imageFiles, // Danh sách file ảnh mới
      oldImageKeys: Array.from(imageKeyMapping.values()), // Keys của ảnh cũ (để reference)
      deletedImageKeys, // Keys của ảnh bị xóa (để gửi DELETE operations)
      hasImageChanged, // Flag để biết ảnh có thay đổi không
      uploadedImageUrls, // URLs của ảnh đã upload
      hasDetailChanged, // Flag để biết detail có thay đổi không
      hasUserManualChanged, // Flag để biết userManual có thay đổi không
      publishAfterUpdate, // Flag để biết có publish sau khi update không
    } as EditProductFormValues & {
      imageFiles?: File[];
      oldImageKeys?: string[];
      deletedImageKeys?: string[];
      hasImageChanged?: boolean;
      uploadedImageUrls?: string[];
      hasDetailChanged?: boolean;
      hasUserManualChanged?: boolean;
      publishAfterUpdate?: boolean;
    });
  };

  return (
    <div className="p-6">
      <Typography variant="h2" className="mb-6">
        {t('admin:marketplace.product.editProduct', 'Chỉnh sửa sản phẩm')}
      </Typography>

      <Form
        onSubmit={handleFormSubmit as unknown as SubmitHandler<Record<string, unknown>>}
        defaultValues={{
          name: product?.name || '',
          description: product?.description || '',
          listedPrice: product?.listedPrice || 0,
          discountedPrice: product?.discountedPrice || 0,
          category: product?.category || ProductCategory.AGENT,
          detail: product?.detail || '',
          userManual: product?.userManual || '',
        }}
        className="space-y-6"
      >
        <FormItem name="name" label={t('admin:marketplace.product.form.name', 'Tên sản phẩm')} required>
          <Input
            placeholder={t('admin:marketplace.product.form.namePlaceholder', 'Nhập tên sản phẩm')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('admin:marketplace.product.form.description', 'Mô tả sản phẩm')}>
          <Input
            placeholder={t('admin:marketplace.product.form.descriptionPlaceholder', 'Nhập mô tả sản phẩm')}
            fullWidth
          />
        </FormItem>

        <FormItem name="image" label={t('admin:marketplace.product.form.images', 'Ảnh sản phẩm')}>
          <div className="space-y-4">
            {/* Hiển thị ảnh hiện có */}
            {imagePreviews.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={preview}
                      alt={`Product ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Icon name="x" size="xs" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload ảnh mới */}
            <div>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  files.forEach(file => {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      if (event.target?.result) {
                        handleAddImage(file, event.target.result as string);
                      }
                    };
                    reader.readAsDataURL(file);
                  });
                }}
                className="hidden"
                id="image-upload"
              />
              <label
                htmlFor="image-upload"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <Icon name="upload" size="lg" className="text-gray-400 mb-2" />
                <span className="text-sm text-muted">
                  {t('admin:marketplace.product.form.imagePlaceholder', 'Kéo thả hoặc click để tải lên ảnh sản phẩm')}
                </span>
                <span className="text-xs text-muted mt-1">
                  {t('admin:marketplace.product.form.imageSupport', 'Hỗ trợ nhiều ảnh, định dạng: JPG, PNG')}
                </span>
              </label>
            </div>

            {/* Thông tin file */}
            {imageFiles.length > 0 && (
              <div className="text-sm text-muted">
                <p>{t('admin:marketplace.product.form.selectedImages', 'Đã chọn {{count}} ảnh mới', { count: imageFiles.length })}</p>
              </div>
            )}
          </div>
        </FormItem>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="listedPrice" label={t('admin:marketplace.product.form.listedPrice', 'Giá niêm yết (rpoint)')}>
            <Input
              type="number"
              placeholder={t('admin:marketplace.product.form.listedPricePlaceholder', 'Nhập giá niêm yết')}
              min={0}
              step={1000}
              leftIcon={<Icon name="dollar-sign" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem name="discountedPrice" label={t('admin:marketplace.product.form.discountedPrice', 'Giá khuyến mãi (rpoint)')}>
            <Input
              type="number"
              placeholder={t('admin:marketplace.product.form.discountedPricePlaceholder', 'Nhập giá khuyến mãi')}
              min={0}
              step={1000}
              leftIcon={<Icon name="dollar-sign" size="sm" />}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="category" label={t('admin:marketplace.product.form.category', 'Thể loại')} required>
          <Select
            placeholder={t('admin:marketplace.product.form.categoryPlaceholder', 'Chọn thể loại')}
            options={[
              {
                value: ProductCategory.AGENT,
                label: t('admin:marketplace.product.category.AGENT', 'AI Agent'),
              },
              {
                value: ProductCategory.KNOWLEDGE_FILE,
                label: t('admin:marketplace.product.category.KNOWLEDGE_FILE', 'Knowledge File'),
              },
              {
                value: ProductCategory.TEMPLATE,
                label: t('admin:marketplace.product.category.TEMPLATE', 'Template')
              },
              {
                value: ProductCategory.OTHER,
                label: t('admin:marketplace.product.category.OTHER', 'Khác'),
              },
            ]}
            fullWidth
          />
        </FormItem>

        {/* Thêm input cho detail và userManual */}
        <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
          <Typography variant="h6">
            {t('admin:marketplace.product.form.additionalInfo', 'Thông tin bổ sung')}
          </Typography>

          <FormTextarea
            name="detail"
            label={t('admin:marketplace.product.form.detail', 'Thông tin chi tiết sản phẩm')}
            placeholder={t('admin:marketplace.product.form.detailPlaceholder', 'Nhập thông tin chi tiết sản phẩm (nếu có thay đổi)')}
            originalValue={originalDetail}
            onChangeDetected={setHasDetailChanged}
          />

          <FormTextarea
            name="userManual"
            label={t('admin:marketplace.product.form.userManual', 'Hướng dẫn sử dụng')}
            placeholder={t('admin:marketplace.product.form.userManualPlaceholder', 'Nhập hướng dẫn sử dụng sản phẩm (nếu có thay đổi)')}
            originalValue={originalUserManual}
            onChangeDetected={setHasUserManualChanged}
          />
        </div>

        {/* Checkbox để publish sau khi update */}
        <Checkbox
          id="publishAfterUpdate"
          checked={publishAfterUpdate}
          onChange={setPublishAfterUpdate}
          label={t('admin:marketplace.product.form.publishAfterUpdate', 'Xuất bản sau khi cập nhật')}
        />

        <div className="flex justify-end space-x-4 mt-8">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button variant="primary" type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? t('common:updating', 'Đang cập nhật...')
              : t('common:update', 'Cập nhật')
            }
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default EditProductForm;
