import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  Typography,
  Chip,
  Table,
  Card,
} from '@/shared/components/common';
import { Order, OrderStatus, OrderItem } from '@/modules/admin/marketplace/types/order.types';

export interface OrderFormProps {
  initialValues?: Partial<Order>;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  readOnly?: boolean;
}

/**
 * Form component for viewing order details
 */
const OrderForm: React.FC<OrderFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  readOnly = false,
}) => {
  const { t } = useTranslation();

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = React.useRef<any>(null);

  // Handle form submission
  const handleSubmit = (values: Record<string, unknown>) => {
    onSubmit(values);
  };

  // Render status chip
  const renderStatusChip = (status?: OrderStatus) => {
    if (!status) return null;

    let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

    switch (status) {
      case OrderStatus.COMPLETED:
        variant = 'success';
        break;
      case OrderStatus.PROCESSING:
        variant = 'primary';
        break;
      case OrderStatus.PENDING:
        variant = 'warning';
        break;
      case OrderStatus.CANCELLED:
      case OrderStatus.FAILED:
        variant = 'danger';
        break;
    }

    return (
      <Chip size="sm" variant={variant}>
        {t(`admin.marketplace.order.status.${status}`, status)}
      </Chip>
    );
  };

  // Columns for order items table
  const itemColumns = [
    {
      key: 'product',
      title: t('admin.marketplace.order.item.product', 'Sản phẩm'),
      dataIndex: 'product',
      width: '40%',
      render: (_: unknown, record: OrderItem) => <span>{record.product?.name}</span>,
    },
    {
      key: 'quantity',
      title: t('admin.marketplace.order.item.quantity', 'Số lượng'),
      dataIndex: 'quantity',
      width: '15%',
    },
    {
      key: 'price',
      title: t('admin.marketplace.order.item.price', 'Giá'),
      dataIndex: 'price',
      width: '20%',
      render: (value: unknown) => <span>{String(value)} points</span>,
    },
    {
      key: 'total',
      title: t('admin.marketplace.order.item.total', 'Tổng'),
      dataIndex: 'id', // Chỉ là placeholder, sẽ dùng render
      width: '25%',
      render: (_: unknown, record: OrderItem) => (
        <span>{record.quantity * record.price} points</span>
      ),
    },
  ];

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {readOnly
            ? t('admin.marketplace.order.details', 'Chi tiết đơn hàng')
            : initialValues?.id
              ? t('admin.marketplace.order.edit', 'Chỉnh sửa đơn hàng')
              : t('admin.marketplace.order.addNew', 'Thêm đơn hàng mới')}
          {initialValues?.orderNumber && readOnly && (
            <span className="ml-2 text-gray-500">#{initialValues.orderNumber}</span>
          )}
        </Typography>

        <Form
          ref={formRef}
          defaultValues={initialValues}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          className="space-y-4"
        >
          {/* Thông tin đơn hàng */}
          <Typography variant="subtitle2" className="mb-2">
            {t('admin.marketplace.order.info', 'Thông tin đơn hàng')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="orderNumber"
              label={t('admin.marketplace.order.form.orderNumber', 'Mã đơn hàng')}
            >
              <Input fullWidth readOnly={true} value={initialValues?.orderNumber || ''} />
            </FormItem>

            <FormItem
              name="userId"
              label={t('admin.marketplace.order.form.userId', 'ID người dùng')}
            >
              <Input fullWidth readOnly={true} value={initialValues?.userId?.toString() || ''} />
            </FormItem>

            <FormItem name="status" label={t('admin.marketplace.order.form.status', 'Trạng thái')}>
              <div className="py-2">{renderStatusChip(initialValues?.status)}</div>
            </FormItem>

            <FormItem name="type" label={t('admin.marketplace.order.form.type', 'Loại đơn hàng')}>
              <Input
                fullWidth
                readOnly={true}
                value={
                  initialValues?.type
                    ? t(`admin.marketplace.order.type.${initialValues.type}`, initialValues.type)
                    : ''
                }
              />
            </FormItem>

            <FormItem
              name="createdAt"
              label={t('admin.marketplace.order.form.createdAt', 'Ngày tạo')}
            >
              <Input
                fullWidth
                readOnly={true}
                value={
                  initialValues?.createdAt ? new Date(initialValues.createdAt).toLocaleString() : ''
                }
              />
            </FormItem>

            <FormItem
              name="totalPrice"
              label={t('admin.marketplace.order.form.totalPrice', 'Tổng tiền')}
            >
              <Input
                fullWidth
                readOnly={true}
                value={initialValues?.totalPrice ? `${initialValues.totalPrice} points` : ''}
              />
            </FormItem>
          </div>

          {/* Sản phẩm trong đơn hàng */}
          <div className="mb-4">
            <Typography variant="subtitle2" className="mb-2">
              {t('admin.marketplace.order.items', 'Sản phẩm trong đơn hàng')}
            </Typography>

            {initialValues?.items && initialValues.items.length > 0 ? (
              <div className="border border-gray-200 rounded-md overflow-hidden">
                <Table
                  columns={itemColumns}
                  data={initialValues.items}
                  rowKey="id"
                  pagination={false}
                />
              </div>
            ) : (
              <Typography className="text-gray-500 italic">
                {t('admin.marketplace.order.items.empty', 'Không có sản phẩm nào')}
              </Typography>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button variant="outline" onClick={onCancel}>
              {readOnly ? t('common.back', 'Quay lại') : t('common.cancel', 'Hủy')}
            </Button>
            {!readOnly && (
              <Button variant="primary" type="submit">
                {t('common.save', 'Lưu')}
              </Button>
            )}
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default OrderForm;
