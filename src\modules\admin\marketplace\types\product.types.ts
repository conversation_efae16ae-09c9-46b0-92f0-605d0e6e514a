/**
 * <PERSON><PERSON><PERSON> nghĩa các types cho Product trong Admin Marketplace
 */

/**
 * Enum cho trạng thái của sản phẩm
 */
export enum ProductStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED',
}

/**
 * Enum cho loại sản phẩm
 */
export enum ProductCategory {
  KNOWLEDGE_FILE = 'KNOWLEDGE_FILE',
  AGENT = 'AGENT',
  TEMPLATE = 'TEMPLATE',
  OTHER = 'OTHER',
}

/**
 * Interface cho thông tin người bán
 */
export interface Seller {
  id?: number;
  name: string;
  avatar: string;
  email?: string;
  phoneNumber?: string;
  type: string;
}

/**
 * Interface cho URL upload
 */
export interface UploadUrl {
  url: string;
  key: string;
  expiresAt?: number;
  index?: number;
}

/**
 * Interface cho URLs upload của sản phẩm
 */
export interface ProductUploadUrls {
  productId: string;
  imagesUploadUrls: UploadUrl[];
  userManualUploadUrl?: UploadUrl;
  detailUploadUrl?: UploadUrl;
}

/**
 * Interface cho sản phẩm
 */
export interface Product {
  id: string;
  name: string;
  description: string;
  images?: string[];
  listedPrice: number;
  discountedPrice: number;
  category: ProductCategory;
  userManual?: string;
  detail?: string;
  sourceId: string;
  status: ProductStatus;
  seller: Seller;
  createdAt: string;
  updatedAt: string;
  uploadUrls?: ProductUploadUrls;
}

/**
 * Interface cho tham số filter của sản phẩm
 */
export interface ProductFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ProductStatus | string;
  category?: ProductCategory | string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho phản hồi API danh sách sản phẩm
 */
export interface ProductListData {
  items: Product[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Alias cho kiểu dữ liệu phản hồi API danh sách sản phẩm
 */
export type ProductListResponse = ProductListData;

/**
 * Alias cho kiểu dữ liệu phản hồi API chi tiết sản phẩm
 */
export type ProductDetailResponse = Product;

/**
 * Enum cho các tùy chọn cập nhật sản phẩm
 */
export enum UpdateProductOption {
  SAVE_DRAFT = 'SAVE_DRAFT',
  SUBMIT_FOR_APPROVAL = 'SUBMIT_FOR_APPROVAL',
}

/**
 * Interface cho thao tác với hình ảnh sản phẩm
 */
export interface ImageOperation {
  operation: 'ADD' | 'DELETE';
  key?: string;
  index?: number;
  mimeType?: string;
}

/**
 * Interface cho dữ liệu tạo sản phẩm mới
 */
export interface CreateProductDto {
  name: string;
  description: string;
  listedPrice: number;
  discountedPrice: number;
  category: ProductCategory;
  sourceId?: string;
  images?: string[];
  userManual?: string;
  detail?: string;
  imagesMediaTypes?: string[];
  userManualMediaType?: string;
  detailMediaType?: string;
  status?: ProductStatus;
}

/**
 * Interface cho dữ liệu cập nhật sản phẩm
 */
export interface UpdateProductDto {
  productInfo: {
    name: string;
    listedPrice: number;
    discountedPrice: number;
    description: string;
  };
  images?: ImageOperation[];
  detailEdited?: boolean;
  userManual?: boolean;
  publishAfterUpdate?: boolean;
}

/**
 * Interface cho phản hồi khi tạo sản phẩm mới
 */
export interface CreateProductResponseDto extends Product {
  uploadUrls: ProductUploadUrls;
}

/**
 * Interface cho phản hồi khi cập nhật sản phẩm
 */
export interface UpdateProductResponseDto extends Product {
  presignedUrlImage?: Array<{
    uploadUrl: string;
    key: string;
  }>;
  presignedUrlDetail?: string;
  presignedUrlUserManual?: string;
}
