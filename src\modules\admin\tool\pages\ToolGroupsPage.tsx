/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,

  Button,
  Table,
  Tooltip,
  Modal,
  IconCard,

} from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ToolGroupForm from '../components/ToolGroupForm';

// Import hooks từ module tool
import {
  useAdminToolGroups,
  useCreateAdminToolGroup,
  useUpdateAdminToolGroup,
  useDeleteAdminToolGroup,
  useAdminToolGroupDetail,
} from '../hooks/useToolGroup';

// Import types từ module tool
import {
  ToolGroupListItem,
  ToolGroupDetail,
  ToolGroupQueryParams,
  CreateToolGroupParams,
  UpdateToolGroupParams,
} from '../types/tool.types';

/**
 * Trang quản lý nhóm tool
 */
const ToolGroupsPage: React.FC = () => {
  const { t } = useTranslation(['admin']);
  const [toolGroups, setToolGroups] = useState<ToolGroupListItem[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [toolGroupToDelete, setToolGroupToDelete] = useState<ToolGroupListItem | null>(null);
  const [toolGroupToView, setToolGroupToView] = useState<ToolGroupDetail | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<ToolGroupQueryParams>(() => {
    const params: ToolGroupQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy as 'name' | 'createdAt' | 'updatedAt' || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: toolGroupsData,
    isLoading: isLoadingToolGroups,
    error: toolGroupsError,
  } = useAdminToolGroups(queryParams);

  const { mutateAsync: createToolGroup, isPending: isCreating } = useCreateAdminToolGroup();
  const { mutateAsync: updateToolGroup, isPending: isUpdating } = useUpdateAdminToolGroup();
  const { mutateAsync: deleteToolGroup, isPending: isDeleting } = useDeleteAdminToolGroup();

  // Lấy chi tiết nhóm tool khi cần xem/chỉnh sửa
  const { data: toolGroupDetail, isLoading: isLoadingDetail } = useAdminToolGroupDetail(
    toolGroupToView?.id || 0
  );

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (toolGroupsData) {
      setToolGroups(toolGroupsData.items);
      setTotalItems(toolGroupsData.meta.totalItems);
    }
  }, [toolGroupsData, toolGroupsError]);

  // Cập nhật toolGroupToView khi có dữ liệu chi tiết
  useEffect(() => {
    if (toolGroupDetail && toolGroupToView) {
      setToolGroupToView(toolGroupDetail);
    }
  }, [toolGroupDetail]);

  // Xử lý submit form tạo nhóm tool
  const handleSubmitCreateToolGroup = useCallback(
    async (values: CreateToolGroupParams | UpdateToolGroupParams) => {
      try {
        await createToolGroup(values as CreateToolGroupParams);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating tool group:', error);
      }
    },
    [createToolGroup, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa nhóm tool
  const handleSubmitEditToolGroup = useCallback(
    async (values: CreateToolGroupParams | UpdateToolGroupParams) => {
      if (!toolGroupToView) return;

      try {
        await updateToolGroup({ id: toolGroupToView.id, data: values as UpdateToolGroupParams });
        hideEditForm();
        setToolGroupToView(null);
      } catch (error) {
        console.error('Error updating tool group:', error);
      }
    },
    [updateToolGroup, hideEditForm, toolGroupToView]
  );

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((toolGroup: ToolGroupListItem) => {
    setToolGroupToDelete(toolGroup);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setToolGroupToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!toolGroupToDelete) return;

    try {
      await deleteToolGroup(toolGroupToDelete.id);
      setShowDeleteConfirm(false);
      setToolGroupToDelete(null);
    } catch (error) {
      console.error('Error deleting tool group:', error);
    }
  }, [toolGroupToDelete, deleteToolGroup]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết nhóm tool
  const handleShowViewForm = useCallback(
    (toolGroup: ToolGroupListItem) => {
      setToolGroupToView(toolGroup as ToolGroupDetail);
      showEditForm();
    },
    [showEditForm]
  );

  // Format date
  const formatDate = (timestamp: number) => {
    const time = Number(timestamp);
    return isNaN(time) ? 'N/A' : new Date(time).toLocaleString();
  };

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('admin:tool.name', 'Tên nhóm'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('admin:tool.description', 'Mô tả'),
        dataIndex: 'description',
        width: '30%',
        render: (value: unknown) => {
          return value ? String(value) : t('admin:tool.noDescription', 'Không có mô tả');
        },
      },
      {
        key: 'toolCount',
        title: t('admin:tool.toolCount', 'Số lượng tool'),
        dataIndex: 'toolCount',
        width: '10%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('admin:tool.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => formatDate(value as number),
      },
      {
        key: 'createdBy',
        title: t('admin:tool.createdBy', 'Người tạo'),
        dataIndex: 'createdBy',
        width: '15%',
        render: (value: unknown) => {
          const creator = value as { name: string; email?: string } | undefined;
          return (
            <div className="flex flex-col">
              <span>{creator?.name || t('admin:tool.unknownUser', 'Không xác định')}</span>
              {creator?.email && <span className="text-xs text-gray-500">{creator.email}</span>}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('admin:tool.actions', 'Thao tác'),
        render: (_: unknown, record: ToolGroupListItem) => (
          <div className="flex space-x-2">
            <Tooltip content={t('admin:tool.view', 'Sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleShowViewForm(record)}
              />
            </Tooltip>

            <Tooltip content={t('admin:tool.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="primary"
                size="sm"
                onClick={() => handleShowDeleteConfirm(record)}
              />
            </Tooltip>
          </div>
        ),
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowViewForm]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={true}
            showColumnFilter={true}

            items={[
              
            ]}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ToolGroupForm
            onSubmit={handleSubmitCreateToolGroup}
            onCancel={hideCreateForm}
            isLoading={isCreating}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem/chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {toolGroupToView && (
            <ToolGroupForm
              initialValues={toolGroupToView}
              onSubmit={handleSubmitEditToolGroup}
              onCancel={hideEditForm}
              isLoading={isUpdating || isLoadingDetail}
              isEdit={true}
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<ToolGroupListItem>
            columns={columns}
            data={toolGroups}
            rowKey="id"
            loading={isLoadingToolGroups}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin:tool.confirmDelete', 'Xác nhận xóa')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelDelete} disabled={isDeleting}>
              {t('admin:tool.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('admin:tool.processing', 'Đang xử lý...') : t('admin:tool.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <p>
          {t(
            'admin:tool.confirmDeleteToolGroupMessage',
            'Bạn có chắc chắn muốn xóa nhóm tool này? Hành động này không thể hoàn tác.'
          )}
        </p>
        {toolGroupToDelete && (
          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="font-medium">{toolGroupToDelete.name}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ToolGroupsPage;
