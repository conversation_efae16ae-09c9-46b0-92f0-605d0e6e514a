import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Checkbox,
  Typography,
  Icon,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useCustomFieldWithFilter } from '../hooks/useCustomFieldWithFilter';
import { CustomFieldData } from './SimpleCustomFieldSelector';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu/ModernMenu';

interface AdvancedCustomFieldSelectorProps {
  onFieldSelect: (fieldData: CustomFieldData) => void;
  selectedFieldIds: number[];
  placeholder?: string;
  className?: string;
}

/**
 * Component nâng cấp để chọn custom fields với MenuIconBar và filter
 */
const AdvancedCustomFieldSelector: React.FC<AdvancedCustomFieldSelectorProps> = ({
  onFieldSelect,
  selectedFieldIds,
  placeholder = 'Nhập từ khóa và nhấn Enter để tìm kiếm...',
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Sử dụng hook với filter
  const {
    items,
    loading,
    hasMore,
    groupOptions,
    groupsLoading,
    filters,
    search,
    filterByGroup,
    sort,
    loadMore,
    initialLoad,
    clearFilters,
    getActiveFiltersCount,
    getFilterLabel,
  } = useCustomFieldWithFilter({
    pageSize: 20,
  });

  // Menu items cho filter
  const filterMenuItems: ModernMenuItem[] = groupOptions.map(option => ({
    id: option.id,
    label: option.label,
    icon: 'folder',
    onClick: () => filterByGroup(option.id),
  }));

  // Active filters hook
  const activeFilters = useActiveFilters({
    handleSearch: search,
    setSelectedFilterId: filterByGroup,
    setDateRange: () => {}, // Không sử dụng date range
    handleSortChange: (sortBy, direction) => {
      if (sortBy && direction) {
        // Convert lowercase to uppercase for API compatibility
        const apiDirection = direction.toUpperCase() as 'ASC' | 'DESC';
        sort(sortBy, apiDirection);
      }
    },
    selectedFilterValue: filters.groupId || 'all',
    filterValueLabelMap: groupOptions.reduce((acc, option) => {
      acc[option.id] = option.label;
      return acc;
    }, {} as Record<string, string>),
    t,
  });

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(true);
    initialLoad();
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  };

  // Handle key down - gọi API khi Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      search(inputValue);
    }
  };

  // Handle item selection
  const handleItemSelect = (item: typeof items[0]) => {
    const fieldData: CustomFieldData = {
      id: item.id,
      label: item.label,
      component: item.component,
      configId: item.configId,
      type: item.type,
      required: item.required,
    };

    onFieldSelect(fieldData);
  };

  // Handle scroll for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (
      scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
      hasMore &&
      !loading
    ) {
      loadMore();
    }
  };

  // Handle search from MenuIconBar
  const handleMenuSearch = (searchTerm: string) => {
    setInputValue(searchTerm);
    search(searchTerm);
  };

  // Handle clear all filters
  const handleClearAllFilters = () => {
    setInputValue('');
    clearFilters();
  };

  return (
    <div ref={containerRef} className={`relative w-full ${className}`}>
      {/* Input */}
      <div
        className={`
          flex items-center
          rounded-md
          bg-white dark:bg-gray-800
          py-2 px-3
          cursor-pointer
          transition-all duration-200
          ${isOpen ? 'ring-2 ring-primary/30' : 'hover:bg-gray-50 dark:hover:bg-gray-700'}
        `}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex-shrink-0 mr-2 text-gray-500">
          <Icon name="search" size="sm" />
        </div>

        <input
          type="text"
          value={inputValue}
          onChange={handleSearchChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          onClick={(e) => e.stopPropagation()}
          placeholder={placeholder}
          className="w-full bg-transparent border-none focus:outline-none focus:ring-0 text-foreground placeholder-gray-500"
        />

        {inputValue && (
          <div className="flex-shrink-0 ml-2 text-xs text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            Enter
          </div>
        )}

        <div className="flex-shrink-0 ml-2 text-gray-500">
          <Icon name={isOpen ? "chevron-up" : "chevron-down"} size="sm" />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="fixed z-[99999] w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden"
             style={{
               left: containerRef.current?.getBoundingClientRect().left || 0,
               top: (containerRef.current?.getBoundingClientRect().bottom || 0) + 4,
               width: containerRef.current?.getBoundingClientRect().width || 'auto',
               zIndex: 99999 // Đảm bảo z-index cao nhất
             }}>
          {/* Header */}
          <div className="bg-gradient-to-r from-red-500 to-orange-500 p-3 text-white">
            <div className="flex items-center justify-between">
              <Typography variant="subtitle2" className="font-medium text-white">
                {t('business:product.form.customFields.title', 'Trường tùy chỉnh')}
              </Typography>
              {getActiveFiltersCount() > 0 && (
                <div className="bg-white/20 px-2 py-1 rounded text-xs">
                  {getActiveFiltersCount()} {t('common:filters')}
                </div>
              )}
            </div>
          </div>

          {/* MenuIconBar - Tầng riêng biệt */}
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-2 relative z-[100000]">
            <MenuIconBar
              onSearch={handleMenuSearch}
              items={filterMenuItems}
              showDateFilter={false}
              showColumnFilter={false}
              isLoading={loading || groupsLoading}
            />
          </div>

          {/* Active Filters */}
          {getActiveFiltersCount() > 0 && (
            <div className="p-2 border-b border-gray-200 dark:border-gray-700">
              <ActiveFilters
                searchTerm={filters.search}
                onClearSearch={activeFilters.handleClearSearch}
                filterValue={filters.groupId || undefined}
                filterLabel={filters.groupId ? getFilterLabel(filters.groupId) : ''}
                onClearFilter={activeFilters.handleClearFilter}
                onClearAll={handleClearAllFilters}
              />
            </div>
          )}

          {/* Content */}
          <div
            ref={scrollRef}
            className="overflow-auto p-2"
            style={{ maxHeight: '300px' }}
            onScroll={handleScroll}
          >
            {loading && items.length === 0 ? (
              <div className="flex items-center justify-center p-4 text-gray-500">
                <div className="animate-spin mr-2">
                  <Icon name="loader" size="sm" />
                </div>
                <span>{t('common:loading', 'Đang tải...')}</span>
              </div>
            ) : items.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {t('common:noResults', 'Không có kết quả')}
              </div>
            ) : (
              <div className="space-y-1">
                {items.map((item) => {
                  const isSelected = selectedFieldIds.includes(item.id);

                  return (
                    <div
                      key={item.id}
                      className={`
                        flex items-center p-2 rounded-lg cursor-pointer
                        hover:bg-gray-50 dark:hover:bg-gray-700
                        transition-colors duration-150
                        ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                      `}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                          handleItemSelect(item);
                        }}
                        className="mr-3"
                      >
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleItemSelect(item)}
                          variant="filled"
                        />
                      </div>

                      <div className="flex-grow">
                        <div className="flex items-center">
                          {item.required && (
                            <span className="text-red-500 text-sm mr-2 font-bold">*</span>
                          )}
                          <Typography variant="body2" className="font-medium">
                            {item.label}
                          </Typography>
                        </div>

                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                            {item.component}
                          </span>
                          {item.configId && (
                            <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                              {item.configId}
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            {item.type}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Loading more indicator */}
                {loading && items.length > 0 && (
                  <div className="flex items-center justify-center p-2 text-gray-500">
                    <div className="animate-spin mr-2">
                      <Icon name="loader" size="xs" />
                    </div>
                    <span className="text-xs">{t('common:loadingMore', 'Đang tải thêm...')}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedCustomFieldSelector;
