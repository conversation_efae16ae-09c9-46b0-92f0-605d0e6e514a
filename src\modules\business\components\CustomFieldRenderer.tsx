import React from 'react';
import {
  Input,
  Textarea,
  Select,
  Checkbox,
  Typography,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { useTranslation } from 'react-i18next';

interface CustomFieldRendererProps {
  field: {
    id: number;
    fieldId: number;
    label: string;
    component: string;
    type: string;
    required: boolean;
    configJson: Record<string, unknown>;
    value: Record<string, unknown>;
  };
  value: string | number | boolean;
  onChange: (value: string | number | boolean) => void;
  onRemove: () => void;
}

/**
 * Component để render custom field dựa trên type
 */
const CustomFieldRenderer: React.FC<CustomFieldRendererProps> = ({
  field,
  value,
  onChange,
  onRemove,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Lấy config từ configJson
  const config = field.configJson || {};
  const placeholder = (config as Record<string, unknown>)?.placeholder as string || t('business:product.form.customFields.valuePlaceholder', 'Nhập giá trị');
  const options = (config as Record<string, unknown>)?.options as Array<{ label: string; value: string | number | boolean }> || [];

  // Render component dựa trên type
  const renderInput = () => {
    switch (field.component.toLowerCase()) {
      case 'textarea':
        return (
          <Textarea
            fullWidth
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            rows={3}
          />
        );

      case 'number':
        return (
          <Input
            fullWidth
            type="number"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'email':
        return (
          <Input
            fullWidth
            type="email"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'phone':
        return (
          <Input
            fullWidth
            type="tel"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'url':
        return (
          <Input
            fullWidth
            type="url"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'password':
        return (
          <Input
            fullWidth
            type="password"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'date':
        return (
          <Input
            fullWidth
            type="date"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'time':
        return (
          <Input
            fullWidth
            type="time"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'datetime':
        return (
          <Input
            fullWidth
            type="datetime-local"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'color':
        return (
          <Input
            fullWidth
            type="color"
            value={value as string || '#000000'}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'range':
        return (
          <Input
            fullWidth
            type="range"
            value={value as string || '0'}
            onChange={(e) => onChange(e.target.value)}
            min={(config as Record<string, unknown>)?.min as number || 0}
            max={(config as Record<string, unknown>)?.max as number || 100}
          />
        );

      case 'select':
        return (
          <Select
            fullWidth
            value={value as string || ''}
            onChange={(val) => onChange(val as string)}
            options={options.map((opt) => ({
              value: String(opt.value),
              label: opt.label,
            }))}
            placeholder={placeholder}
          />
        );

      case 'checkbox':
        return (
          <Checkbox
            checked={Boolean(value)}
            onChange={(checked) => onChange(checked)}
            label={placeholder}
          />
        );

      case 'switch':
        return (
          <Toggle
            checked={Boolean(value)}
            onChange={(checked: boolean) => onChange(checked)}
            label={placeholder}
          />
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {options.map((option, index: number) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={`radio-${field.id}`}
                  value={String(option.value)}
                  checked={value === option.value}
                  onChange={(e) => onChange(e.target.value)}
                  className="form-radio"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'text':
      default:
        return (
          <Input
            fullWidth
            type="text"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );
    }
  };

  return (
    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Typography variant="caption" className="font-medium">
            {field.label}
          </Typography>
          {field.required && (
            <span className="text-red-500 text-xs">*</span>
          )}
          <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
            {field.component}
          </span>
        </div>
        <button
          type="button"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700 text-sm"
        >
          ✕
        </button>
      </div>
      {renderInput()}
    </div>
  );
};

export default CustomFieldRenderer;
