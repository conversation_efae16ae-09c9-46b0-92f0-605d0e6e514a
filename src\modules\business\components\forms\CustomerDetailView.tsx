import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button } from '@/shared/components/common';
import {
  CustomerGeneralInfo,
  CustomerOverview,
  CustomerSocial,
  CustomerCustomFields,
  CustomerInteractions,
  CustomerOrders,
  type CustomerDetailData,
} from './sections';
interface CustomerDetailViewProps {
  /**
   * Dữ liệu khách hàng
   */
  customer: CustomerDetailData;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chỉnh sửa
   */
  onEdit?: () => void;
}

/**
 * Component hiển thị chi tiết khách hàng
 */
const CustomerDetailView: React.FC<CustomerDetailViewProps> = ({ customer, onClose, onEdit }) => {
  const { t } = useTranslation('business');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border pb-4">
        <div>
          <Typography variant="h4" className="text-foreground">
            {t('customer.detailForm')}
          </Typography>
          <Typography variant="body2" className="text-muted mt-1">
            {customer.name}
          </Typography>
        </div>
        <div className="flex space-x-3">
          {onEdit && (
            <Button variant="outline" onClick={onEdit}>
              {t('common.edit')}
            </Button>
          )}
          <Button variant="outline" onClick={onClose}>
            {t('common.close')}
          </Button>
        </div>
      </div>

      {/* Thông tin chung */}
      <CustomerGeneralInfo customer={customer} />

      {/* Trường tùy chỉnh */}
      <CustomerCustomFields customer={customer} />

      {/* Tổng quan */}
      <CustomerOverview customer={customer} />

      {/* Social */}
      <CustomerSocial customer={customer} />

      {/* Tương tác */}
      <CustomerInteractions customer={customer} />

      {/* Đơn hàng */}
      <CustomerOrders customer={customer} />
    </div>
  );
};

export default CustomerDetailView;
