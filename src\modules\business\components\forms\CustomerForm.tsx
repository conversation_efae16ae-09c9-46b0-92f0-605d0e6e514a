import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { Form, FormItem, Input, Button, Typography } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import TagsInput from '@/shared/components/common/TagsInput/TagsInput';
import { createCustomerFormSchema, CustomerFormValues } from '../../schemas/customer.schema';

interface CustomerFormProps {
  /**
   * Dữ liệu ban đầu cho form (dùng cho chỉnh sửa)
   */
  initialData?: Partial<CustomerFormValues>;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: CustomerFormValues) => void | Promise<void>;

  /**
   * Callback khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái loading khi submit
   */
  isLoading?: boolean;

  /**
   * Tiêu đề form
   */
  title?: string;

  /**
   * Form ref từ parent component để xử lý lỗi
   */
  formRef?: React.RefObject<FormRef<FieldValues>>;
}

/**
 * Component form thêm/chỉnh sửa khách hàng
 */
const CustomerForm: React.FC<CustomerFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  title,
  formRef: externalFormRef,
}) => {
  const { t } = useTranslation(['business', 'validation', 'common']);
  const internalFormRef = useRef<FormRef<FieldValues>>(null);

  // Sử dụng external formRef nếu có, nếu không thì dùng internal
  const formRef = externalFormRef || internalFormRef;

  // Tạo schema với translation
  const customerFormSchema = createCustomerFormSchema(t);

  // Giá trị mặc định cho form
  const defaultValues: CustomerFormValues = {
    name: '',
    email: '',
    phone: '',
    tags: '',
    ...initialData,
  };

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    try {
      await onSubmit(values as CustomerFormValues);
    } catch (error) {
      console.error('Error submitting customer form:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-border pb-4">
        <Typography variant="h4" className="text-foreground">
          {title || t('customer.addForm')}
        </Typography>
      </div>

      {/* Form */}
      <Form
        ref={formRef}
        schema={customerFormSchema}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Họ và tên */}
          <FormItem name="name" label={t('customer.form.name')} required>
            <Input
              placeholder={t('customer.form.namePlaceholder')}
              fullWidth
              disabled={isLoading}
            />
          </FormItem>

          {/* Email */}
          <FormItem name="email" label={t('customer.form.email')} required>
            <Input
              type="email"
              placeholder={t('customer.form.emailPlaceholder')}
              fullWidth
              disabled={isLoading}
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Số điện thoại */}
          <FormItem name="phone" label={t('customer.form.phone')} required>
            <Input
              type="tel"
              placeholder={t('customer.form.phonePlaceholder')}
              fullWidth
              disabled={isLoading}
            />
          </FormItem>

          {/* Tag khách hàng */}
          <FormItem name="tags" label={t('customer.form.tags')}>
            <TagsInput
              fieldName="tags"
              placeholder={t('customer.form.tagsPlaceholder')}
              formRef={formRef}
              initialValue={initialData?.tags}
              readOnly={isLoading}
            />
          </FormItem>
        </div>

        {/* Action buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="min-w-[100px]"
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
            className="min-w-[100px]"
          >
            {t('common.save')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default CustomerForm;
export type { CustomerFormValues };