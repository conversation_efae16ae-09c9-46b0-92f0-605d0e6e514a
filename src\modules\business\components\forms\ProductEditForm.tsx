import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  ConditionalField,
  Divider,
  Typography,
  Chip,
  Icon,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';

import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  UpdateProductDto,
  ProductDto
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';

import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductEditFormProps {
  product: ProductDto;
  onSubmit: (values: UpdateProductDto) => Promise<UpdateProductDto | ProductDto | ProductWithImagesResponse>;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Interface cho biến thể sản phẩm trong form
interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  customFields: SelectedCustomField[];
}

/**
 * Form chỉnh sửa sản phẩm
 */
const ProductEditForm: React.FC<ProductEditFormProps> = ({ product, onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);

  // Schema validation với kiểm tra điều kiện theo loại giá (sao chép từ ProductForm)
  const productSchema = z.object({
    name: z.string().min(1, 'Tên sản phẩm không được để trống'),
    typePrice: z.nativeEnum(PriceTypeEnum, {
      errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
    }),
    listPrice: z.union([z.string(), z.number()]).optional(),
    salePrice: z.union([z.string(), z.number()]).optional(),
    currency: z.string().optional(),
    priceDescription: z.string().optional(),
    description: z.string().optional(),
    tags: z.array(z.string()).optional(),
    shipmentConfig: z.object({
      lengthCm: z.union([z.string(), z.number()]).optional(),
      widthCm: z.union([z.string(), z.number()]).optional(),
      heightCm: z.union([z.string(), z.number()]).optional(),
      weightGram: z.union([z.string(), z.number()]).optional(),
    }).optional(),
    media: z.any().optional(),
    customFields: z.any().optional(),
    classifications: z.any().optional(), // Đổi tên từ variants thành classifications
  }).superRefine((data, ctx) => {
    // Kiểm tra giá phù hợp với loại giá
    if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
      // Kiểm tra listPrice
      if (!data.listPrice || data.listPrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá niêm yết',
          path: ['listPrice'],
        });
      } else {
        const listPrice = Number(data.listPrice);
        if (isNaN(listPrice) || listPrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá niêm yết phải là số >= 0',
            path: ['listPrice'],
          });
        }
      }

      // Kiểm tra salePrice
      if (!data.salePrice || data.salePrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá bán',
          path: ['salePrice'],
        });
      } else {
        const salePrice = Number(data.salePrice);
        if (isNaN(salePrice) || salePrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá bán phải là số >= 0',
            path: ['salePrice'],
          });
        }
      }

      // Kiểm tra currency
      if (!data.currency || data.currency.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng chọn đơn vị tiền tệ',
          path: ['currency'],
        });
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (data.listPrice && data.salePrice && data.listPrice !== '' && data.salePrice !== '') {
        const listPrice = Number(data.listPrice);
        const salePrice = Number(data.salePrice);

        if (!isNaN(listPrice) && !isNaN(salePrice) && listPrice > 0 && salePrice > 0) {
          if (listPrice <= salePrice) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá niêm yết phải lớn hơn giá bán',
              path: ['listPrice'],
            });
          }
        }
      }
    } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!data.priceDescription || !data.priceDescription.trim()) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập mô tả giá',
          path: ['priceDescription'],
        });
      }
    }
  });

  // State cho tags (khởi tạo từ product)
  const [tempTags, setTempTags] = useState<string[]>(product.tags || []);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho phân loại sản phẩm (đổi tên từ variants)
  const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Khởi tạo form với dữ liệu sản phẩm hiện tại
  useEffect(() => {
    if (product && formRef.current) {
      const formData: Record<string, unknown> = {
        name: product.name,
        typePrice: product.typePrice,
        description: product.description || '',
        tags: product.tags || [],
      };

      // Xử lý giá dựa trên loại giá
      if (product.typePrice === PriceTypeEnum.HAS_PRICE && product.price) {
        const price = product.price as HasPriceDto;
        formData.listPrice = price.listPrice;
        formData.salePrice = price.salePrice;
        formData.currency = price.currency;
      } else if (product.typePrice === PriceTypeEnum.STRING_PRICE && product.price) {
        const price = product.price as StringPriceDto;
        formData.priceDescription = price.priceDescription;
      }

      // Xử lý shipment config
      if (product.shipmentConfig) {
        formData.shipmentConfig = {
          lengthCm: product.shipmentConfig.lengthCm || '',
          widthCm: product.shipmentConfig.widthCm || '',
          heightCm: product.shipmentConfig.heightCm || '',
          weightGram: product.shipmentConfig.weightGram || '',
        };
      }

      // Set form values
      formRef.current.setValues(formData);

      // Set tags
      setTempTags(product.tags || []);
    }
  }, [product]);

  // Xử lý khi submit form (sao chép từ ProductForm và chuyển đổi cho edit)
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 ProductEditForm handleSubmit called with values:', values);

    if (!values.name || !values.typePrice) {
      console.error('❌ Missing required fields:', { name: values.name, typePrice: values.typePrice });
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', formValues);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        console.error('❌ Price validation error:', priceError);
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      const productData: UpdateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: priceData,
        description: formValues.description || undefined,
        tags: tempTags && tempTags.length > 0 ? tempTags : undefined,
        shipmentConfig: getShipmentConfig(formValues),
        imagesMediaTypes: mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : undefined,
        classifications: productClassifications.length > 0 ? productClassifications.map(variant => ({
          id: variant.id,
          name: variant.name,
          listPrice: Number(variant.listPrice) || 0,
          salePrice: Number(variant.salePrice) || 0,
          currency: variant.currency,
          customFields: variant.customFields,
        })) : undefined,
      };

      console.log('📤 Final product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      console.log('✅ Product updated successfully:', response);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có images array không
          const hasImages = response &&
                           typeof response === 'object' &&
                           'images' in response &&
                           Array.isArray((response as ProductWithImagesResponse).images);

          if (hasImages) {
            const images = (response as ProductWithImagesResponse).images;

            if (images.length > 0) {
              console.log('🚀 Starting image upload with TaskQueue...');
              console.log('📁 Media files:', mediaFiles.length);
              console.log('🔗 Upload URLs from backend:', images.length);

              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, images.length).map((fileData, index) => {
                const imageInfo = images[index];
                return {
                  file: fileData.file,
                  uploadUrl: imageInfo.url,
                  key: imageInfo.key,
                  position: imageInfo.position
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, { skipCacheInvalidation: true });

              console.log('✅ All product images uploaded successfully');

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t('business:product.mediaUploadSuccess', 'Tải lên ảnh sản phẩm thành công'),
                duration: 3000,
              });
            } else {
              console.warn('⚠️ Images array is empty');
              NotificationUtil.warning({
                message: t('business:product.mediaUploadWarning', 'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'),
                duration: 5000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');

            NotificationUtil.warning({
              message: t('business:product.mediaUploadWarning', 'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading product images:', uploadError);
          NotificationUtil.warning({
            message: t('business:product.mediaUploadError', 'Có lỗi xảy ra khi tải lên ảnh sản phẩm'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in ProductEditForm handleSubmit:', error);
      setIsUploading(false);

      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        console.error('Validation errors:', error);
        NotificationUtil.error({
          message: t('business:product.validationError'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.updateError'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá (sao chép từ ProductForm)
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      // Kiểm tra đầy đủ các trường bắt buộc
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }

      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }

      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }

      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }

      return {
        priceDescription: values.priceDescription.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.NO_PRICE) {
      return null;
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Hàm lấy dữ liệu cấu hình vận chuyển (sao chép từ ProductForm)
  const getShipmentConfig = (values: ProductFormValues) => {
    if (!values.shipmentConfig) return undefined;

    const config = values.shipmentConfig;
    const hasAnyValue = config.lengthCm || config.widthCm || config.heightCm || config.weightGram;

    if (!hasAnyValue) return undefined;

    return {
      lengthCm: Number(config.lengthCm) || undefined,
      widthCm: Number(config.widthCm) || undefined,
      heightCm: Number(config.heightCm) || undefined,
      weightGram: Number(config.weightGram) || undefined,
    };
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính (sao chép từ ProductForm)
  const handleToggleCustomFieldToProduct = useCallback((fieldId: number, fieldData?: Record<string, unknown>) => {
    setProductCustomFields(prev => {
      // Kiểm tra xem trường đã tồn tại chưa
      const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

      if (existingFieldIndex !== -1) {
        // Nếu đã tồn tại, xóa nó (bỏ chọn)
        return prev.filter((_, index) => index !== existingFieldIndex);
      }

      // Thêm trường mới với thông tin đầy đủ
      const newField: SelectedCustomField = {
        id: Date.now(), // ID tạm thời
        fieldId,
        label: (fieldData?.label as string) || `Field ${fieldId}`,
        component: (fieldData?.component as string) || 'text',
        type: (fieldData?.type as string) || 'string',
        required: (fieldData?.required as boolean) || false,
        configJson: (fieldData?.configJson as Record<string, unknown>) || {},
        value: { value: '' }, // Giá trị mặc định
      };

      return [...prev, newField];
    });
  }, []);

  // Xóa trường tùy chỉnh khỏi sản phẩm chính (sao chép từ ProductForm)
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Thêm/xóa trường tùy chỉnh vào phân loại (sao chép từ ProductForm)
  const handleToggleCustomFieldToVariant = useCallback((variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        // Kiểm tra xem trường đã tồn tại trong phân loại chưa
        const existingFieldIndex = variant.customFields.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          // Nếu đã tồn tại, xóa nó (bỏ chọn)
          return {
            ...variant,
            customFields: variant.customFields.filter((_, index) => index !== existingFieldIndex)
          };
        }

        // Thêm trường mới vào phân loại với thông tin đầy đủ
        return {
          ...variant,
          customFields: [
            ...variant.customFields,
            {
              id: Date.now(), // ID tạm thời
              fieldId,
              label: (fieldData?.label as string) || `Field ${fieldId}`,
              component: (fieldData?.component as string) || 'text',
              type: (fieldData?.type as string) || 'string',
              required: (fieldData?.required as boolean) || false,
              configJson: (fieldData?.configJson as Record<string, unknown>) || {},
              value: { value: '' }, // Giá trị mặc định
            }
          ]
        };
      }
      return variant;
    }));
  }, []);

  // Xóa trường tùy chỉnh khỏi phân loại (sao chép từ ProductForm)
  const handleRemoveCustomFieldFromVariant = useCallback((variantId: number, customFieldId: number) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.filter(field => field.id !== customFieldId)
        };
      }
      return variant;
    }));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong phân loại (sao chép từ ProductForm)
  const handleUpdateCustomFieldInVariant = useCallback((variantId: number, customFieldId: number, value: string) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.map(field => {
            if (field.id === customFieldId) {
              return {
                ...field,
                value: { value }
              };
            }
            return field;
          })
        };
      }
      return variant;
    }));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính (sao chép từ ProductForm)
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev => prev.map(field => {
      if (field.id === customFieldId) {
        return {
          ...field,
          value: { value }
        };
      }
      return field;
    }));
  }, []);

  // Thêm phân loại mới (sao chép từ ProductForm)
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(),
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      customFields: [],
    };
    setProductClassifications(prev => [...prev, newVariant]);
  }, []);

  // Xóa phân loại (sao chép từ ProductForm)
  const handleRemoveVariant = useCallback((variantId: number) => {
    setProductClassifications(prev => prev.filter(variant => variant.id !== variantId));
  }, []);

  // Cập nhật thông tin phân loại (sao chép từ ProductForm)
  const handleUpdateVariant = useCallback((variantId: number, field: keyof ProductVariant, value: string) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          [field]: value
        };
      }
      return variant;
    }));
  }, []);

  // Giá trị mặc định cho form - sử dụng useMemo để tránh re-create
  const defaultValues = useMemo(() => ({
    name: '',
    typePrice: PriceTypeEnum.HAS_PRICE,
    listPrice: '',
    salePrice: '',
    currency: 'VND',
    priceDescription: '',
    description: '',
    tags: [],
    shipmentConfig: {
      lengthCm: '',
      widthCm: '',
      heightCm: '',
      weightGram: '',
    },
    customFields: [],
    media: [],
    classifications: [], // Đổi tên từ variants
  }), []);

  return (
    <Card title={t('business:product.form.editTitle')} allowOverflow={true}>
      <Form
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        onError={(errors) => {
          console.error('🔥 Form validation errors:', errors);

          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            console.error(`❌ Field "${field}":`, errors[field]);
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
            if (errors[field] && typeof errors[field] === 'object' && 'type' in errors[field]) {
              console.error(`   Type: ${(errors[field] as { type: string }).type}`);
            }
          });

          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="p-4 space-y-4"
      >
        <FormItem
          name="name"
          label={t('business:product.name')}
          required
        >
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>

        <FormItem
          name="typePrice"
          label={t('business:product.priceType.title')}
          required
        >
          <Select
            fullWidth
            options={[
              { value: PriceTypeEnum.HAS_PRICE, label: t('business:product.priceType.hasPrice') },
              { value: PriceTypeEnum.STRING_PRICE, label: t('business:product.priceType.stringPrice') },
              { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
            ]}
          />
        </FormItem>

        {/* Hiển thị các trường giá dựa trên loại giá */}
        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.HAS_PRICE,
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="listPrice"
              label={t('business:product.listPrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="Nhập giá niêm yết" />
            </FormItem>
            <FormItem
              name="salePrice"
              label={t('business:product.salePrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="Nhập giá bán" />
            </FormItem>
            <FormItem
              name="currency"
              label={t('business:product.currency')}
              required
            >
              <Controller
                name="currency"
                render={({ field }) => (
                  <Select
                    fullWidth
                    value={field.value || 'VND'}
                    onChange={(value) => field.onChange(value)}
                    options={[
                      { value: 'VND', label: 'VND' },
                      { value: 'USD', label: 'USD' },
                      { value: 'EUR', label: 'EUR' },
                    ]}
                  />
                )}
              />
            </FormItem>
          </div>
        </ConditionalField>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.STRING_PRICE,
          }}
        >
          <FormItem
            name="priceDescription"
            label={t('business:product.priceDescription')}
            required
          >
            <Input fullWidth placeholder={t('business:product.form.priceDescriptionPlaceholder')} />
          </FormItem>
        </ConditionalField>

        <FormItem
          name="description"
          label={t('business:product.form.description')}
        >
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>

        {/* Upload media */}
        <FormItem
          name="media"
          label={t('business:product.form.media')}
        >
          <MultiFileUpload
            mediaOnly={true}
            accept="image/*,video/*"
            placeholder={t('business:product.form.mediaPlaceholder', 'Kéo thả hoặc click để tải lên ảnh/video')}
            onChange={(files) => {
              setMediaFiles(files);
              // Không gọi setValue để tránh reset form
            }}
            value={mediaFiles}
          />
        </FormItem>

        <FormItem
          name="tags"
          label={t('business:product.tags')}
        >
          <Controller
            name="tags"
            render={({ field }) => (
              <div className="space-y-2">
                <Input
                  fullWidth
                  placeholder={t('business:product.form.tagsPlaceholder')}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      e.preventDefault();

                      // Lấy tag mới
                      const newTag = e.currentTarget.value.trim();

                      // Thêm tag mới nếu chưa tồn tại
                      if (!tempTags.includes(newTag)) {
                        const newTags = [...tempTags, newTag];
                        setTempTags(newTags);
                        field.onChange(newTags); // Đồng bộ với form
                      }

                      e.currentTarget.value = '';
                    }
                  }}
                />
                <div className="flex flex-wrap gap-1 mt-2">
                  {tempTags.map((tag, tagIndex) => (
                    <Chip
                      key={`tag-${tagIndex}-${tag}`}
                      size="sm"
                      closable
                      onClose={() => {
                        const newTags = tempTags.filter(t => t !== tag);
                        setTempTags(newTags);
                        field.onChange(newTags); // Đồng bộ với form
                      }}
                    >
                      {tag}
                    </Chip>
                  ))}
                </div>
              </div>
            )}
          />
        </FormItem>

        {/* Trường tùy chỉnh cho sản phẩm */}
        <Divider className="my-4" />
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h6">
            {t('business:product.form.customFields.title', 'Trường tùy chỉnh')}
          </Typography>
        </div>

        <SimpleCustomFieldSelector
          onFieldSelect={(fieldData) => {
            handleToggleCustomFieldToProduct(fieldData.id, fieldData as unknown as Record<string, unknown>);
          }}
          selectedFieldIds={productCustomFields.map(f => f.fieldId)}
          placeholder={t('business:product.form.customFields.searchPlaceholder', 'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...')}
        />

        {productCustomFields.length > 0 && (
          <div className="space-y-3 mb-4">
            {productCustomFields.map((field) => (
              <CustomFieldRenderer
                key={field.id}
                field={field}
                value={field.value.value as string || ''}
                onChange={(value) => handleUpdateCustomFieldInProduct(field.id, value as string)}
                onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
              />
            ))}
          </div>
        )}

        {/* Cấu hình vận chuyển */}
        <Divider className="my-4" />
        <Typography variant="h6" className="mb-4">
          {t('business:product.form.shipmentConfig.title', 'Cấu hình vận chuyển')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="shipmentConfig.widthCm"
            label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.heightCm"
            label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.lengthCm"
            label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.weightGram"
            label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
        </div>

        {/* Phần biến thể sản phẩm */}
        <Divider className="my-4" />
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
          <Typography variant="h6">
            {t('business:product.form.variants.title', 'Biến thể sản phẩm')}
          </Typography>
          <Button
            size="sm"
            variant="primary"
            type="button"
            onClick={(e) => {
              e.preventDefault();
              handleAddVariant();
            }}
            className="w-full sm:w-auto"
          >
            <Icon name="plus" className="mr-1" size="sm" />
            {t('business:product.form.variants.addVariant', 'Thêm biến thể')}
          </Button>
        </div>

        {/* Danh sách phân loại (đổi tên từ biến thể) */}
        {productClassifications.length > 0 ? (
          <div className="space-y-6">
            {productClassifications.map((variant, index) => (
              <div key={variant.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="subtitle1" className="font-medium">
                    {t('business:product.form.variants.variant', 'Biến thể')} #{index + 1}
                  </Typography>
                  <Button
                    size="sm"
                    variant="ghost"
                    type="button"
                    onClick={() => handleRemoveVariant(variant.id)}
                  >
                    <Icon name="trash" size="sm" className="text-red-500" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormItem
                    label={t('business:product.form.variants.name', 'Tên biến thể')}
                  >
                    <Input
                      fullWidth
                      value={variant.name}
                      onChange={(e) => handleUpdateVariant(variant.id, 'name', e.target.value)}
                      placeholder={t('business:product.form.variants.namePlaceholder', 'Nhập tên biến thể')}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                  >
                    <Select
                      fullWidth
                      value={variant.currency}
                      onChange={(val) => handleUpdateVariant(variant.id, 'currency', val as string)}
                      options={[
                        { value: 'VND', label: 'VND' },
                        { value: 'USD', label: 'USD' },
                        { value: 'EUR', label: 'EUR' },
                      ]}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.listPrice}
                      onChange={(e) => handleUpdateVariant(variant.id, 'listPrice', e.target.value)}
                      placeholder="0"
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.salePrice', 'Giá bán')}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.salePrice}
                      onChange={(e) => handleUpdateVariant(variant.id, 'salePrice', e.target.value)}
                      placeholder="0"
                    />
                  </FormItem>
                </div>

                {/* Trường tùy chỉnh cho biến thể */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <Typography variant="subtitle2">
                      {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                    </Typography>
                  </div>

                  <SimpleCustomFieldSelector
                    onFieldSelect={(fieldData) => {
                      handleToggleCustomFieldToVariant(variant.id, fieldData.id, fieldData as unknown as Record<string, unknown>);
                    }}
                    selectedFieldIds={variant.customFields.map(f => f.fieldId)}
                    placeholder={t('business:product.form.variants.searchCustomField', 'Nhập từ khóa và nhấn Enter để tìm thuộc tính...')}
                  />

                  {variant.customFields.length > 0 && (
                    <div className="space-y-3">
                      {variant.customFields.map((field) => (
                        <CustomFieldRenderer
                          key={field.id}
                          field={field}
                          value={field.value.value as string || ''}
                          onChange={(value) => handleUpdateCustomFieldInVariant(variant.id, field.id, value as string)}
                          onRemove={() => handleRemoveCustomFieldFromVariant(variant.id, field.id)}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:product.form.variants.noVariants', 'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.')}
            </Typography>
          </div>
        )}

        <div className="flex flex-col sm:flex-row sm:justify-end gap-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isUploading}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isUploading}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default ProductEditForm;
