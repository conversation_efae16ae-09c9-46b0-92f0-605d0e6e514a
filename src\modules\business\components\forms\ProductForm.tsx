import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  ConditionalField,
  Divider,
  Typography,
  Chip,
  Icon,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';

import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { createProductSchema } from '../../schemas/product.schema';
import {
  PriceTypeEnum,
  ProductTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto
} from '../../types/product.types';
import { useCreateProduct } from '../../hooks/useProductQuery';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';

import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { ProductService } from '../../services/product.service';
import CustomFieldRenderer from '../CustomFieldRenderer';
import AdvancedCustomFieldSelector from '../AdvancedCustomFieldSelector';


interface ProductFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}



// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  variants?: ProductVariant[];
}

// Interface cho biến thể sản phẩm trong form
interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  customFields: SelectedCustomField[];
}

/**
 * Form tạo sản phẩm mới
 */
const ProductForm: React.FC<ProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createProduct, isPending } = useCreateProduct();



  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // State cho biến thể sản phẩm
  const [productVariants, setProductVariants] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload file với TaskQueue
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload product media',
    autoAddToQueue: true,
  });



  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    try {
      const formValues = values as ProductFormValues;
      setIsUploading(true);

      // Không còn sử dụng customFields riêng, chỉ sử dụng trong biến thể

      // Chuyển đổi giá trị form thành dữ liệu API
      const productData: CreateProductDto = {
        name: formValues.name,
        productType: ProductTypeEnum.PHYSICAL, // Mặc định là sản phẩm vật lý
        typePrice: formValues.typePrice,
        price: getPriceData(formValues),
        description: formValues.description,
        tags: formValues.tags || [], // Sử dụng tags từ form
        shipmentConfig: formValues.shipmentConfig ? {
          lengthCm: Number(formValues.shipmentConfig.lengthCm) || undefined,
          widthCm: Number(formValues.shipmentConfig.widthCm) || undefined,
          heightCm: Number(formValues.shipmentConfig.heightCm) || undefined,
          weightGram: Number(formValues.shipmentConfig.weightGram) || undefined,
        } : undefined,
        // Thêm trường tùy chỉnh cho sản phẩm chính
        customFields: productCustomFields.length > 0 ? productCustomFields.map(field => ({
          fieldId: field.fieldId,
          value: field.value,
        })) : undefined,
        variants: productVariants.length > 0 ? productVariants.map(variant => ({
          name: variant.name,
          listPrice: Number(variant.listPrice),
          salePrice: Number(variant.salePrice),
          currency: variant.currency,
          customFields: variant.customFields.map(field => ({
            fieldId: field.fieldId,
            value: field.value,
          })),
        })) : undefined,
      };

      // Gọi API tạo sản phẩm
      await createProduct(productData);

      // Upload media nếu có
      if (mediaFiles.length > 0) {
        try {
          // Tạo danh sách promises cho việc upload từng file
          const uploadPromises = mediaFiles.map(async (fileData) => {
            // Lấy presigned URL từ API
            const presignedData = await ProductService.getPresignedUrl(fileData.file.type);

            // Upload file lên presigned URL với TaskQueue
            return fileUploadWithQueue.uploadToUrlWithQueue({
              file: fileData.file,
              presignedUrl: presignedData.url,
              taskTitle: `Upload: ${fileData.file.name}`,
              taskDescription: `Kích thước: ${(fileData.file.size / 1024).toFixed(1)} KB`,
              onUploadProgress: (progress) => {
                setUploadProgress(progress);
              },
            });
          });

          // Chờ tất cả các file upload xong
          await Promise.all(uploadPromises);
        } catch (uploadError) {
          console.error('Error uploading media:', uploadError);
          NotificationUtil.warning({
            message: t('business:product.mediaUploadError'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.createSuccess'),
        duration: 3000,
      });

      // Gọi callback onSubmit
      onSubmit(values as Record<string, unknown>);
    } catch (error) {
      console.error('Error creating product:', error);
      setIsUploading(false);
      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE && values.listPrice && values.salePrice) {
      return {
        listPrice: Number(values.listPrice),
        salePrice: Number(values.salePrice),
        currency: values.currency || 'VND',
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE && values.priceDescription) {
      return {
        priceDescription: values.priceDescription,
      };
    }
    return null;
  };


  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback((fieldId: number, fieldData?: Record<string, unknown>) => {
    setProductCustomFields(prev => {
      // Kiểm tra xem trường đã tồn tại chưa
      const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

      if (existingFieldIndex !== -1) {
        // Nếu đã tồn tại, xóa nó (bỏ chọn)
        return prev.filter((_, index) => index !== existingFieldIndex);
      }

      // Thêm trường mới với thông tin đầy đủ
      const newField: SelectedCustomField = {
        id: Date.now(), // ID tạm thời
        fieldId,
        label: (fieldData?.label as string) || `Field ${fieldId}`,
        component: (fieldData?.component as string) || 'text',
        type: (fieldData?.type as string) || 'string',
        required: (fieldData?.required as boolean) || false,
        configJson: (fieldData?.configJson as Record<string, unknown>) || {},
        value: { value: '' }, // Giá trị mặc định
      };

      return [...prev, newField];
    });
  }, []);

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev => prev.map(field => {
      if (field.id === customFieldId) {
        return {
          ...field,
          value: { value }
        };
      }
      return field;
    }));
  }, []);





  // Thêm biến thể mới
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(), // ID tạm thời cho UI
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      customFields: [],
    };

    setProductVariants(prev => [...prev, newVariant]);
    // Không cần setValue cho variants vì chúng ta quản lý riêng trong state
  }, []);

  // Xóa biến thể
  const handleRemoveVariant = useCallback((variantId: number) => {
    setProductVariants(prev => prev.filter(variant => variant.id !== variantId));
    // Không cần setValue cho variants vì chúng ta quản lý riêng trong state
  }, []);

  // Cập nhật biến thể
  const handleUpdateVariant = useCallback((variantId: number, field: string, value: string | number) => {
    setProductVariants(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return { ...variant, [field]: value };
      }
      return variant;
    }));
    // Không cần setValue cho variants vì chúng ta quản lý riêng trong state
  }, []);

  // Thêm/xóa trường tùy chỉnh vào biến thể
  const handleToggleCustomFieldToVariant = useCallback((variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
    setProductVariants(prev => prev.map(variant => {
      if (variant.id === variantId) {
        // Kiểm tra xem trường đã tồn tại trong biến thể chưa
        const existingFieldIndex = variant.customFields.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          // Nếu đã tồn tại, xóa nó (bỏ chọn)
          return {
            ...variant,
            customFields: variant.customFields.filter((_, index) => index !== existingFieldIndex)
          };
        }

        // Thêm trường mới vào biến thể với thông tin đầy đủ
        return {
          ...variant,
          customFields: [
            ...variant.customFields,
            {
              id: Date.now(), // ID tạm thời
              fieldId,
              label: (fieldData?.label as string) || `Field ${fieldId}`,
              component: (fieldData?.component as string) || 'text',
              type: (fieldData?.type as string) || 'string',
              required: (fieldData?.required as boolean) || false,
              configJson: (fieldData?.configJson as Record<string, unknown>) || {},
              value: { value: '' }, // Giá trị mặc định
            }
          ]
        };
      }
      return variant;
    }));
  }, []);

  // Xóa trường tùy chỉnh khỏi biến thể
  const handleRemoveCustomFieldFromVariant = useCallback((variantId: number, customFieldId: number) => {
    setProductVariants(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.filter(field => field.id !== customFieldId)
        };
      }
      return variant;
    }));
    // Không cần setValue cho variants vì chúng ta quản lý riêng trong state
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong biến thể
  const handleUpdateCustomFieldInVariant = useCallback((variantId: number, customFieldId: number, value: string) => {
    setProductVariants(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.map(field => {
            if (field.id === customFieldId) {
              return {
                ...field,
                value: { value }
              };
            }
            return field;
          })
        };
      }
      return variant;
    }));
    // Không cần setValue cho variants vì chúng ta quản lý riêng trong state
  }, []);



  // Giá trị mặc định cho form - sử dụng useMemo để tránh re-create
  const defaultValues = useMemo(() => ({
    name: '',
    typePrice: PriceTypeEnum.HAS_PRICE,
    listPrice: '',
    salePrice: '',
    currency: 'VND',
    priceDescription: '',
    description: '',
    tags: [],
    shipmentConfig: {
      lengthCm: '',
      widthCm: '',
      heightCm: '',
      weightGram: '',
    },
    customFields: [],
    media: [],
    variants: [],
  }), []);

  return (
    <Card title={t('business:product.form.createTitle')} allowOverflow={true}>
      <Form
        ref={formRef}
        schema={createProductSchema(t)}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="p-4 space-y-4"
      >
        <FormItem
          name="name"
          label={t('business:product.name')}
          required
        >
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>



        <FormItem
          name="typePrice"
          label={t('business:product.priceType.title')}
          required
        >
          <Select
            fullWidth
            options={[
              { value: PriceTypeEnum.HAS_PRICE, label: t('business:product.priceType.hasPrice') },
              { value: PriceTypeEnum.STRING_PRICE, label: t('business:product.priceType.stringPrice') },
              { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
            ]}
          />
        </FormItem>

        {/* Hiển thị các trường giá dựa trên loại giá */}
        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.HAS_PRICE,
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="listPrice"
              label={t('business:product.listPrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="salePrice"
              label={t('business:product.salePrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="currency"
              label={t('business:product.currency')}
              required
            >
              <Select
                fullWidth
                options={[
                  { value: 'VND', label: 'VND' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' },
                ]}
              />
            </FormItem>
          </div>
        </ConditionalField>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.STRING_PRICE,
          }}
        >
          <FormItem
            name="priceDescription"
            label={t('business:product.priceDescription')}
            required
          >
            <Input fullWidth placeholder={t('business:product.form.priceDescriptionPlaceholder')} />
          </FormItem>
        </ConditionalField>

        <FormItem
          name="description"
          label={t('business:product.form.description')}
        >
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>

        {/* Upload media */}
        <FormItem
          name="media"
          label={t('business:product.form.media')}
        >
          <MultiFileUpload
            mediaOnly={true}
            accept="image/*,video/*"
            placeholder={t('business:product.form.mediaPlaceholder', 'Kéo thả hoặc click để tải lên ảnh/video')}
            onChange={(files) => {
              setMediaFiles(files);
              // Không gọi setValue để tránh reset form
            }}
            value={mediaFiles}
          />
        </FormItem>

        <FormItem
          name="tags"
          label={t('business:product.tags')}
        >
          <Controller
            name="tags"
            render={({ field }) => (
              <div className="space-y-2">
                <Input
                  fullWidth
                  placeholder={t('business:product.form.tagsPlaceholder')}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      e.preventDefault();

                      // Lấy tag mới
                      const newTag = e.currentTarget.value.trim();

                      // Thêm tag mới nếu chưa tồn tại
                      if (!tempTags.includes(newTag)) {
                        const newTags = [...tempTags, newTag];
                        setTempTags(newTags);
                        field.onChange(newTags); // Đồng bộ với form
                      }

                      e.currentTarget.value = '';
                    }
                  }}
                />
                <div className="flex flex-wrap gap-1 mt-2">
                  {tempTags.map((tag, tagIndex) => (
                    <Chip
                      key={`tag-${tagIndex}-${tag}`}
                      size="sm"
                      closable
                      onClose={() => {
                        const newTags = tempTags.filter(t => t !== tag);
                        setTempTags(newTags);
                        field.onChange(newTags); // Đồng bộ với form
                      }}
                    >
                      {tag}
                    </Chip>
                  ))}
                </div>
              </div>
            )}
          />
        </FormItem>

        {/* Trường tùy chỉnh cho sản phẩm */}
        <Divider className="my-4" />
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h6">
            {t('business:product.form.customFields.title', 'Trường tùy chỉnh')}
          </Typography>
        </div>

        <AdvancedCustomFieldSelector
          onFieldSelect={(fieldData) => {
            handleToggleCustomFieldToProduct(fieldData.id, fieldData as unknown as Record<string, unknown>);
          }}
          selectedFieldIds={productCustomFields.map(f => f.fieldId)}
          placeholder={t('business:product.form.customFields.searchPlaceholder', 'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...')}
          className="mb-4"
        />



        {productCustomFields.length > 0 && (
          <div className="space-y-3 mb-4">
            {productCustomFields.map((field) => (
              <CustomFieldRenderer
                key={field.id}
                field={field}
                value={field.value.value as string || ''}
                onChange={(value) => handleUpdateCustomFieldInProduct(field.id, value as string)}
                onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
              />
            ))}
          </div>
        )}



        {/* Cấu hình vận chuyển */}
        <Divider className="my-4" />
        <Typography variant="h6" className="mb-4">
          {t('business:product.form.shipmentConfig.title', 'Cấu hình vận chuyển')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="shipmentConfig.widthCm"
            label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.heightCm"
            label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.lengthCm"
            label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.weightGram"
            label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
        </div>

        {/* Phần biến thể sản phẩm */}
        <Divider className="my-4" />
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
          <Typography variant="h6">
            {t('business:product.form.variants.title', 'Biến thể sản phẩm')}
          </Typography>
          <Button
            size="sm"
            variant="primary"
            onClick={(e) => {
              e.preventDefault();
              handleAddVariant();
            }}
            className="w-full sm:w-auto"
          >
            <Icon name="plus" className="mr-1" size="sm" />
            {t('business:product.form.variants.addVariant', 'Thêm biến thể')}
          </Button>
        </div>

        {/* Danh sách biến thể */}
        {productVariants.length > 0 ? (
          <div className="space-y-6">
            {productVariants.map((variant, index) => (
              <div key={variant.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="subtitle1" className="font-medium">
                    {t('business:product.form.variants.variant', 'Biến thể')} #{index + 1}
                  </Typography>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveVariant(variant.id)}
                  >
                    <Icon name="trash" size="sm" className="text-red-500" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormItem
                    label={t('business:product.form.variants.name', 'Tên biến thể')}
                  >
                    <Input
                      fullWidth
                      value={variant.name}
                      onChange={(e) => handleUpdateVariant(variant.id, 'name', e.target.value)}
                      placeholder={t('business:product.form.variants.namePlaceholder', 'Nhập tên biến thể')}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                  >
                    <Select
                      fullWidth
                      value={variant.currency}
                      onChange={(val) => handleUpdateVariant(variant.id, 'currency', val as string)}
                      options={[
                        { value: 'VND', label: 'VND' },
                        { value: 'USD', label: 'USD' },
                        { value: 'EUR', label: 'EUR' },
                      ]}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.listPrice}
                      onChange={(e) => handleUpdateVariant(variant.id, 'listPrice', e.target.value)}
                      placeholder="0"
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.salePrice', 'Giá bán')}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.salePrice}
                      onChange={(e) => handleUpdateVariant(variant.id, 'salePrice', e.target.value)}
                      placeholder="0"
                    />
                  </FormItem>
                </div>

                {/* Trường tùy chỉnh cho biến thể */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <Typography variant="subtitle2">
                      {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                    </Typography>
                  </div>

                  <AdvancedCustomFieldSelector
                    onFieldSelect={(fieldData) => {
                      handleToggleCustomFieldToVariant(variant.id, fieldData.id, fieldData as unknown as Record<string, unknown>);
                    }}
                    selectedFieldIds={variant.customFields.map(f => f.fieldId)}
                    placeholder={t('business:product.form.variants.searchCustomField', 'Nhập từ khóa và nhấn Enter để tìm thuộc tính...')}
                    className="mb-3"
                  />

                  {variant.customFields.length > 0 && (
                    <div className="space-y-3">
                      {variant.customFields.map((field) => (
                        <CustomFieldRenderer
                          key={field.id}
                          field={field}
                          value={field.value.value as string || ''}
                          onChange={(value) => handleUpdateCustomFieldInVariant(variant.id, field.id, value as string)}
                          onRemove={() => handleRemoveCustomFieldFromVariant(variant.id, field.id)}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:product.form.variants.noVariants', 'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.')}
            </Typography>
          </div>
        )}



        <div className="flex flex-col sm:flex-row sm:justify-end gap-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isPending || isUploading}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isPending || isUploading}
            className="w-full sm:w-auto order-1 sm:order-2"
          >
            {isUploading ? t('business:product.uploading') : t('common:save')}
          </Button>
        </div>

        {/* Hiển thị tiến trình upload nếu đang upload */}
        {isUploading && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
              <div
                className="bg-primary-600 h-2.5 rounded-full"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <Typography variant="caption" className="mt-1 text-center block">
              {t('business:product.uploadingMedia', 'Đang tải lên media...')} {uploadProgress.toFixed(0)}%
            </Typography>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default ProductForm;
