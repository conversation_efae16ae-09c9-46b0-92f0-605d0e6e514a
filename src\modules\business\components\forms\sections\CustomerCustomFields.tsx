import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Card, Table, IconButton } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { CustomerDetailData } from './types';
import CustomFieldRenderer from '../../CustomFieldRenderer';

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho custom field trong table
interface CustomFieldTableItem {
  id: number;
  name: string;
  type: string;
  component: string;
  required: boolean;
  description?: string;
  isSelected: boolean;
}

// Interface cho query params
interface CustomFieldQueryParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
}

interface CustomerCustomFieldsProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị trường tùy chỉnh của khách hàng
 */
const CustomerCustomFields: React.FC<CustomerCustomFieldsProps> = () => {
  const { t } = useTranslation('business');
  const [customerCustomFields, setCustomerCustomFields] = useState<SelectedCustomField[]>([]);


  // Slide form hooks
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Mock data cho available custom fields
  const mockAvailableFields: CustomFieldTableItem[] = useMemo(() => [
    {
      id: 1,
      name: 'Nghề nghiệp',
      type: 'text',
      component: 'input',
      required: false,
      description: 'Nghề nghiệp của khách hàng',
      isSelected: customerCustomFields.some(f => f.fieldId === 1),
    },
    {
      id: 2,
      name: 'Tuổi',
      type: 'number',
      component: 'input',
      required: false,
      description: 'Tuổi của khách hàng',
      isSelected: customerCustomFields.some(f => f.fieldId === 2),
    },
    {
      id: 3,
      name: 'Sở thích',
      type: 'text',
      component: 'textarea',
      required: false,
      description: 'Sở thích của khách hàng',
      isSelected: customerCustomFields.some(f => f.fieldId === 3),
    },
    {
      id: 4,
      name: 'Ngày sinh',
      type: 'date',
      component: 'date',
      required: false,
      description: 'Ngày sinh của khách hàng',
      isSelected: customerCustomFields.some(f => f.fieldId === 4),
    },
  ], [customerCustomFields]);

  // Table columns
  const columns: TableColumn<CustomFieldTableItem>[] = useMemo(() => [
    {
      key: 'name',
      title: t('customField.name'),
      dataIndex: 'name',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground font-medium">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'type',
      title: t('customField.type'),
      dataIndex: 'type',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted capitalize">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'description',
      title: t('customField.description'),
      dataIndex: 'description',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {value ? String(value) : '-'}
        </Typography>
      ),
    },
  ], [t]);

  // Create query params function
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
  }): CustomFieldQueryParams => ({
    page: params.page,
    limit: params.pageSize,
    search: params.searchTerm || undefined,
    sortBy: params.sortBy || undefined,
    sortDirection: params.sortDirection || undefined,
  });

  // Use data table hook
  const dataTable = useDataTable(
    useDataTableConfig<CustomFieldTableItem, CustomFieldQueryParams>({
      columns,
      createQueryParams,
    })
  );

  // Handle add custom field
  const handleAddCustomField = useCallback(() => {
    showForm();
  }, [showForm]);

  // Handle form submit
  const handleFormSubmit = useCallback(() => {
    // TODO: Implement form submit logic
    hideForm();
  }, [hideForm]);

  // Handle form cancel
  const handleFormCancel = useCallback(() => {
    hideForm();
  }, [hideForm]);

  // Handle row selection
  const handleRowSelection = useCallback((keys: React.Key[]) => {


    // Add/remove fields based on selection
    keys.forEach(key => {
      const fieldId = Number(key);
      const field = mockAvailableFields.find(f => f.id === fieldId);
      if (field && !customerCustomFields.some(f => f.fieldId === fieldId)) {
        // Add field
        const newField: SelectedCustomField = {
          id: Date.now() + fieldId, // temporary ID
          fieldId: fieldId,
          label: field.name,
          component: field.component,
          type: field.type,
          required: field.required,
          configJson: {},
          value: { value: '' },
        };
        setCustomerCustomFields(prev => [...prev, newField]);
      }
    });

    // Remove unselected fields
    setCustomerCustomFields(prev =>
      prev.filter(field => keys.includes(field.fieldId))
    );
  }, [mockAvailableFields, customerCustomFields]);



  // Cập nhật giá trị trường tùy chỉnh trong khách hàng
  const handleUpdateCustomFieldInCustomer = useCallback((customFieldId: number, value: string) => {
    setCustomerCustomFields(prev => prev.map(field =>
      field.id === customFieldId
        ? { ...field, value: { value } }
        : field
    ));
  }, []);

  // Xóa trường tùy chỉnh khỏi khách hàng
  const handleRemoveCustomFieldFromCustomer = useCallback((customFieldId: number) => {
    setCustomerCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Xử lý lưu trường tùy chỉnh
  const handleSaveCustomFields = useCallback(() => {
    // TODO: Implement save logic
    console.log('Save custom fields:', customerCustomFields);
  }, [customerCustomFields]);



  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('business:customer.detail.customFields', 'Trường tùy chỉnh')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={handleAddCustomField}
          items={[]}
          showDateFilter={false}
          showColumnFilter={false}
        />

        {/* Table for available custom fields */}
        <Card className="overflow-hidden">
          <Table<CustomFieldTableItem>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={mockAvailableFields}
            rowKey="id"
            loading={false}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys: customerCustomFields.map(f => f.fieldId),
              onChange: handleRowSelection,
            }}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: 1,
              pageSize: 10,
              total: mockAvailableFields.length,
              onChange: () => {},
              showSizeChanger: false,
              showFirstLastButtons: false,
              showPageInfo: false,
            }}
          />
        </Card>

        {/* SlideInForm for adding custom field */}
        <SlideInForm isVisible={isVisible}>
          <Card className="p-6">
            <Typography variant="h6" className="mb-4">
              {t('customField.addNew', 'Thêm trường tùy chỉnh mới')}
            </Typography>
            {/* TODO: Add custom field form here */}
            <div className="flex justify-end space-x-2 mt-4">
              <IconButton
                icon="x"
                onClick={handleFormCancel}
                variant="outline"
              />
              <IconButton
                icon="check"
                onClick={handleFormSubmit}
                variant="primary"
              />
            </div>
          </Card>
        </SlideInForm>

        {customerCustomFields.length > 0 && (
          <div className="space-y-3">
            {customerCustomFields.map((field) => (
              <CustomFieldRenderer
                key={field.id}
                field={field}
                value={field.value.value as string || ''}
                onChange={(value) => handleUpdateCustomFieldInCustomer(field.id, value as string)}
                onRemove={() => handleRemoveCustomFieldFromCustomer(field.id)}
              />
            ))}
          </div>
        )}

        {customerCustomFields.length === 0 && (
          <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:customer.form.customFields.noFields', 'Chưa có trường tùy chỉnh nào. Chọn từ bảng trên để thêm trường.')}
            </Typography>
          </div>
        )}

        {/* Save button */}
        {customerCustomFields.length > 0 && (
          <div className="flex justify-end pt-4 border-t border-border">
            <IconButton
              icon="save"
              onClick={handleSaveCustomFields}
              variant="primary"
              size="md"
            />
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default CustomerCustomFields;
