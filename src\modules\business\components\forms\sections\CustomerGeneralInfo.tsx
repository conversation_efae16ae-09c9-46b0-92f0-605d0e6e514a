import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { Typography, Card, Chip, Avatar, Icon, Button, Input, FormItem, Form, CollapsibleCard, IconButton } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { CustomerDetailData } from './types';

interface CustomerGeneralInfoProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị thông tin chung của khách hàng
 */
const CustomerGeneralInfo: React.FC<CustomerGeneralInfoProps> = ({ customer }) => {
  const { t } = useTranslation('business');
  const formRef = useRef<FormRef<FieldValues>>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState<CustomerDetailData>(customer);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [newTag, setNewTag] = useState('');

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  // Get status chip variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'danger';
      default:
        return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Hoạt động';
      case 'inactive':
        return 'Không hoạt động';
      case 'blocked':
        return 'Bị khóa';
      default:
        return status;
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof CustomerDetailData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle add tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag('');
    }
  };

  // Handle remove tag
  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || [],
    }));
  };

  // Handle tag input key press
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle avatar upload
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Vui lòng chọn file hình ảnh');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Kích thước file không được vượt quá 5MB');
      return;
    }

    setIsUploadingAvatar(true);

    try {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);

      // Update form data with new avatar
      setFormData(prev => ({
        ...prev,
        avatar: previewUrl,
      }));

      // TODO: Implement actual upload to server
      // const uploadedUrl = await uploadAvatarToServer(file);
      // setFormData(prev => ({ ...prev, avatar: uploadedUrl }));

      console.log('Avatar uploaded:', file.name);
    } catch (error) {
      console.error('Error uploading avatar:', error);
      alert('Có lỗi xảy ra khi tải lên avatar');
    } finally {
      setIsUploadingAvatar(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle avatar click
  const handleAvatarClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle save
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement save logic
      console.log('Save customer data:', formData);
      // Show success message or notification here
    } catch (error) {
      console.error('Error saving customer:', error);
      // Show error message here
    } finally {
      setIsSaving(false);
    }
  };



  // Handle form submit
  const handleSubmit = async (data: FieldValues) => {
    setIsSaving(true);
    try {
      // TODO: Implement save logic
      console.log('Save customer data:', data);
      setFormData(data as CustomerDetailData);
      // Show success message or notification here
    } catch (error) {
      console.error('Error saving customer:', error);
      // Show error message here
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.generalInfo')}
          </Typography>
          <Chip variant={getStatusVariant(formData.status)} size="sm">
            {getStatusText(formData.status)}
          </Chip>
        </div>
      }
      defaultOpen={true}
    >
      <Card className="p-6">
        <Form ref={formRef} onSubmit={handleSubmit} defaultValues={formData}>
          <div className="space-y-6">
            {/* Avatar và thông tin cơ bản */}
            <div className="flex items-start space-x-4">
              <div className="relative">
                <div
                  className="relative cursor-pointer group"
                  onClick={handleAvatarClick}
                >
                  <Avatar
                    src={formData.avatar || `https://i.pravatar.cc/150?img=${formData.id}`}
                    alt={formData.name}
                    size="xl"
                    className={`${isUploadingAvatar ? 'opacity-50' : ''} transition-opacity`}
                  />

                  {/* Upload overlay - không có bóng */}
                  <div className="absolute inset-0 bg-black/30 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Icon name="camera" size="md" className="text-white" />
                  </div>

                  {/* Loading spinner */}
                  {isUploadingAvatar && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    </div>
                  )}
                </div>

                {/* Upload button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAvatarClick}
                  disabled={isUploadingAvatar}
                  className="mt-2 w-full"
                >
                  <Icon name="upload" size="sm" className="mr-1" />
                  {isUploadingAvatar ? 'Đang tải...' : 'Thay đổi'}
                </Button>

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                />
              </div>

              <div className="flex-1">
                <Typography variant="body2" className="text-muted mb-2">
                  ID: {formData.id}
                </Typography>
              </div>
            </div>

            {/* Form fields */}
            <div className="grid grid-cols-1 gap-4">
              <FormItem label={t('customer.form.name')} name="name" required>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('customer.form.namePlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('customer.form.email')} name="email" required>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t('customer.form.emailPlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('customer.form.phone')} name="phone">
                <Input
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t('customer.form.phonePlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('customer.form.address')} name="address">
                <Input
                  value={formData.address || ''}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder={t('customer.form.addressPlaceholder')}
                  fullWidth
                />
              </FormItem>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem label={t('customer.detail.customerSince')} name="customerSince">
                  <Input
                    value={formatDate(formData.customerSince)}
                    fullWidth
                    disabled={true}
                  />
                </FormItem>

                <FormItem label={t('customer.form.tags')} name="tags">
                  <div className="space-y-2">
                    {/* Existing tags */}
                    <div className="flex flex-wrap gap-1">
                      {formData.tags && formData.tags.length > 0 ? (
                        formData.tags.map((tag, index) => (
                          <Chip
                            key={index}
                            size="sm"
                            closable
                            onClose={() => handleRemoveTag(tag)}
                          >
                            {tag}
                          </Chip>
                        ))
                      ) : (
                        <Typography variant="body2" className="text-muted">
                          {t('customer.detail.noData')}
                        </Typography>
                      )}
                    </div>

                    {/* Add new tag */}
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={handleTagKeyPress}
                        placeholder="Thêm tag mới..."
                        className="flex-1"
                      />
                      <IconButton
                        icon="plus"
                        onClick={handleAddTag}
                        disabled={!newTag.trim()}
                        size="sm"
                        variant="outline"
                      />
                    </div>
                  </div>
                </FormItem>
              </div>
            </div>

            {/* Save button */}
            <div className="flex justify-end pt-4 border-t border-border">
              <IconButton
                icon={isSaving ? "loader" : "save"}
                onClick={handleSave}
                disabled={isSaving}
                size="md"
                variant="primary"
                className={isSaving ? "animate-spin" : ""}
              />
            </div>
          </div>
        </Form>
      </Card>
    </CollapsibleCard>
  );
};

export default CustomerGeneralInfo;
