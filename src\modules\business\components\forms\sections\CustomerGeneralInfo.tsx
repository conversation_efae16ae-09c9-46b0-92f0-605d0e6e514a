import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { Typography, Card, Chip, Avatar, Badge, Icon, Button, Input, FormItem, Form } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { CustomerDetailData } from './types';

interface CustomerGeneralInfoProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị thông tin chung của khách hàng
 */
const CustomerGeneralInfo: React.FC<CustomerGeneralInfoProps> = ({ customer }) => {
  const { t } = useTranslation('business');
  const formRef = useRef<FormRef<FieldValues>>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<CustomerDetailData>(customer);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'danger';
      default:
        return 'info';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return t('common.status.active');
      case 'inactive':
        return t('common.status.inactive');
      case 'blocked':
        return 'Bị khóa';
      default:
        return status;
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof CustomerDetailData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle save
  const handleSave = async () => {
    try {
      // TODO: Implement save logic
      console.log('Save customer data:', formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving customer:', error);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setFormData(customer);
    setIsEditing(false);
  };

  // Handle form submit
  const handleSubmit = async (data: FieldValues) => {
    try {
      // TODO: Implement save logic
      console.log('Save customer data:', data);
      setFormData(data as CustomerDetailData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving customer:', error);
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Typography variant="h6" className="text-foreground">
          {t('customer.detail.generalInfo')}
        </Typography>
        <div className="flex items-center space-x-2">
          <Badge variant={getStatusVariant(formData.status)}>
            {getStatusText(formData.status)}
          </Badge>
          {!isEditing ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Icon name="edit" size="sm" className="mr-1" />
              {t('common.edit')}
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
              >
                {t('common.cancel')}
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleSave}
              >
                {t('common.save')}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <Card className="p-6">
        <Form ref={formRef} onSubmit={handleSubmit} defaultValues={formData}>
          <div className="space-y-6">
            {/* Avatar và thông tin cơ bản */}
            <div className="flex items-start space-x-4">
              <Avatar
                src={formData.avatar || `https://i.pravatar.cc/150?img=${formData.id}`}
                alt={formData.name}
                size="xl"
              />
              <div className="flex-1">
                <Typography variant="body2" className="text-muted mb-2">
                  ID: {formData.id}
                </Typography>
              </div>
            </div>

            {/* Form fields */}
            <div className="grid grid-cols-1 gap-4">
              <FormItem label={t('customer.form.name')} name="name" required>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('customer.form.namePlaceholder')}
                  fullWidth
                  disabled={!isEditing}
                />
              </FormItem>

              <FormItem label={t('customer.form.email')} name="email" required>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t('customer.form.emailPlaceholder')}
                  fullWidth
                  disabled={!isEditing}
                />
              </FormItem>

              <FormItem label={t('customer.form.phone')} name="phone">
                <Input
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t('customer.form.phonePlaceholder')}
                  fullWidth
                  disabled={!isEditing}
                />
              </FormItem>

              <FormItem label={t('customer.form.address')} name="address">
                <Input
                  value={formData.address || ''}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder={t('customer.form.addressPlaceholder')}
                  fullWidth
                  disabled={!isEditing}
                />
              </FormItem>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem label={t('customer.detail.customerSince')} name="customerSince">
                  <Input
                    value={formatDate(formData.customerSince)}
                    fullWidth
                    disabled={true}
                  />
                </FormItem>

                <FormItem label={t('customer.form.tags')} name="tags">
                  <div className="flex flex-wrap gap-1">
                    {formData.tags && formData.tags.length > 0 ? (
                      formData.tags.map((tag, index) => (
                        <Chip key={index} size="sm">
                          {tag}
                        </Chip>
                      ))
                    ) : (
                      <Typography variant="body2" className="text-muted">
                        {t('customer.detail.noData')}
                      </Typography>
                    )}
                  </div>
                </FormItem>
              </div>
            </div>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default CustomerGeneralInfo;
