import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { CustomerService, CreateConvertCustomerDto } from '../services/customer.service';
import {
  QueryUserConvertCustomerDto,
  UserConvertCustomerListItemDto,
} from '../types/customer.types';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Query keys cho customer API
 */
export const CUSTOMER_QUERY_KEYS = {
  all: ['business', 'customers'] as const,
  list: (params: QueryUserConvertCustomerDto) => [...CUSTOMER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CUSTOMER_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook lấy danh sách khách hàng chuyển đổi
 */
export const useConvertCustomers = (params: QueryUserConvertCustomerDto = {}) => {
  return useQuery({
    queryKey: CUSTOMER_QUERY_KEYS.list(params),
    queryFn: () => CustomerService.getConvertCustomers(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết khách hàng chuyển đổi
 */
export const useConvertCustomer = (id?: number) => {
  return useQuery({
    queryKey: CUSTOMER_QUERY_KEYS.detail(id || 0),
    queryFn: () => CustomerService.getConvertCustomerById(id || 0),
    enabled: !!id, // Chỉ gọi API khi có ID
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo khách hàng chuyển đổi mới
 */
export const useCreateConvertCustomer = (options?: {
  showSuccessNotification?: boolean;
  showErrorNotification?: boolean;
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();
  const { showSuccessNotification = true, showErrorNotification = false } = options || {};

  return useMutation({
    mutationFn: (data: CreateConvertCustomerDto) =>
      CustomerService.createConvertCustomer(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });

      if (showSuccessNotification) {
        NotificationUtil.success({ message: t('business:customer.messages.createSuccess') || 'Tạo khách hàng thành công' });
      }
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi tạo khách hàng:', error);

      if (showErrorNotification) {
        NotificationUtil.error({
          message: t('business:customer.messages.createError') || 'Có lỗi xảy ra khi tạo khách hàng'
        });
      }

      // Không re-throw error, để parent component xử lý thông qua mutateAsync catch
    },
  });
};

/**
 * Hook cập nhật khách hàng chuyển đổi
 */
export const useUpdateConvertCustomer = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<UserConvertCustomerListItemDto> }) =>
      CustomerService.updateConvertCustomer(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });
      // Invalidate chi tiết khách hàng cụ thể
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.detail(variables.id) });

      NotificationUtil.success({
        message: t('business:customer.messages.updateSuccess') || 'Cập nhật khách hàng thành công'
      });
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi cập nhật khách hàng:', error);
      NotificationUtil.error({
        message: t('business:customer.messages.updateError') || 'Có lỗi xảy ra khi cập nhật khách hàng'
      });
    },
  });
};

/**
 * Hook xóa khách hàng chuyển đổi
 */
export const useDeleteConvertCustomer = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CustomerService.deleteConvertCustomer(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });

      NotificationUtil.success({
        message: t('business:customer.messages.deleteSuccess') || 'Xóa khách hàng thành công'
      });
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi xóa khách hàng:', error);
      NotificationUtil.error({
        message: t('business:customer.messages.deleteError') || 'Có lỗi xảy ra khi xóa khách hàng'
      });
    },
  });
};
