import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import { formatTimestamp } from '@/shared/utils/date';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

import { useOrders, useDeleteOrder } from '../hooks/useOrderQuery';
import { Order, OrderStatus, OrderQueryParams, TransformedOrderListItem } from '../services/order.service';
import OrderForm from '../components/forms/OrderForm';

/**
 * Trang quản lý đơn hàng
 */
const OrdersPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho form và modal
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Định nghĩa các tùy chọn filter
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), value: 'all' },
      {
        id: 'pending',
        label: t('business:order.status.pending'),
        value: OrderStatus.PENDING,
      },
      {
        id: 'processing',
        label: t('business:order.status.processing'),
        value: OrderStatus.PROCESSING,
      },
      {
        id: 'completed',
        label: t('business:order.status.completed'),
        value: OrderStatus.COMPLETED,
      },
      {
        id: 'cancelled',
        label: t('business:order.status.cancelled'),
        value: OrderStatus.CANCELLED,
      },
      {
        id: 'refunded',
        label: t('business:order.status.refunded'),
        value: OrderStatus.REFUNDED,
      },
    ],
    [t]
  );

  // Xử lý xem chi tiết đơn hàng
  const handleView = useCallback(
    (order: TransformedOrderListItem) => {
      setSelectedOrder(order as unknown as Order);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Xử lý xóa đơn hàng
  const handleDelete = useCallback((order: TransformedOrderListItem) => {
    setSelectedOrder(order as unknown as Order);
    setIsDeleteModalOpen(true);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<TransformedOrderListItem>[]>(
    () => [
      {
        key: 'orderNumber',
        title: t('business:order.orderNumber'),
        dataIndex: 'orderNumber',
        sortable: true,
        width: '15%',
      },
      {
        key: 'customerName',
        title: t('business:order.customerName'),
        dataIndex: 'customerName',
        sortable: true,
        width: '20%',
      },
      {
        key: 'totalAmount',
        title: t('business:order.totalAmount'),
        dataIndex: 'totalAmount',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const amount = value as number;
          return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(amount);
        },
      },
      {
        key: 'status',
        title: t('business:order.status.title'),
        dataIndex: 'status',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const status = value as OrderStatus;
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default' = 'default';
          let label = '';

          switch (status) {
            case OrderStatus.PENDING:
              chipVariant = 'warning';
              label = t('business:order.status.pending');
              break;
            case OrderStatus.PROCESSING:
              chipVariant = 'primary';
              label = t('business:order.status.processing');
              break;
            case OrderStatus.COMPLETED:
              chipVariant = 'success';
              label = t('business:order.status.completed');
              break;
            case OrderStatus.CANCELLED:
              chipVariant = 'danger';
              label = t('business:order.status.cancelled');
              break;
            case OrderStatus.REFUNDED:
              chipVariant = 'info';
              label = t('business:order.status.refunded');
              break;
            default:
              label = String(status);
          }

          return <Chip variant={chipVariant}>{label}</Chip>;
        },
      },
      {
        key: 'paymentStatus',
        title: t('business:order.paymentStatus.title'),
        dataIndex: 'paymentStatus',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const paymentStatus = value as 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default' = 'default';
          let label = '';

          switch (paymentStatus) {
            case 'PAID':
              chipVariant = 'success';
              label = t('business:order.paymentStatus.paid');
              break;
            case 'UNPAID':
              chipVariant = 'danger';
              label = t('business:order.paymentStatus.unpaid');
              break;
            case 'PARTIALLY_PAID':
              chipVariant = 'warning';
              label = t('business:order.paymentStatus.partiallyPaid');
              break;
            default:
              label = String(paymentStatus);
          }

          return <Chip variant={chipVariant}>{label}</Chip>;
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt'),
        dataIndex: 'createdAt',
        sortable: true,
        width: '15%',
        render: (value: unknown) => formatTimestamp(value as number),
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: TransformedOrderListItem) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleView(record),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleView, handleDelete]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedOrder(null);
  }, []);

  // Mutation để xóa đơn hàng
  const { mutateAsync: deleteOrder } = useDeleteOrder();

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!selectedOrder) return;

    try {
      // Gọi API xóa đơn hàng
      await deleteOrder(selectedOrder.id);

      // Đóng popup
      setIsDeleteModalOpen(false);
      setSelectedOrder(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:order.deleteSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting order:', error);
      NotificationUtil.error({
        message: t('business:order.deleteError'),
        duration: 3000,
      });
    }
  }, [selectedOrder, deleteOrder, t]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
    hideDetailForm();
    setSelectedOrder(null);
  };

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    // Xử lý thêm đơn hàng (sẽ triển khai sau)
    hideAddForm();
  };

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback((column: string | null, order: SortOrder) => {
    const sortDirection = order === 'asc' ? SortDirection.ASC : SortDirection.DESC;
    return { sortBy: column, sortDirection };
  }, []);

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as OrderStatus;
    }

    // Xử lý dateRange nếu có
    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.fromDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.toDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<TransformedOrderListItem, OrderQueryParams>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách đơn hàng với queryParams từ dataTable
  const { data: orderData, isLoading } = useOrders(dataTable.queryParams);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [OrderStatus.PENDING]: t('business:order.status.pending'),
      [OrderStatus.PROCESSING]: t('business:order.status.processing'),
      [OrderStatus.COMPLETED]: t('business:order.status.completed'),
      [OrderStatus.CANCELLED]: t('business:order.status.cancelled'),
      [OrderStatus.REFUNDED]: t('business:order.status.refunded'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <OrderForm onSubmit={handleSubmit} onCancel={handleCancel} isSubmitting={false} />
      </SlideInForm>

      {/* Form chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        <OrderForm
          order={selectedOrder}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={false}
          isViewMode={true}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={orderData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: orderData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: orderData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:order.confirmDeleteMessage')}
        itemName={selectedOrder?.orderNumber}
      />
    </div>
  );
};

export default OrdersPage;
