import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  QueryUserConvertCustomerDto,
  PaginatedUserConvertCustomerResult,
  UserConvertCustomerListItemDto,
} from '../types/customer.types';

/**
 * Interface cho dữ liệu tạo khách hàng chuyển đổi mới
 */
export interface CreateConvertCustomerDto {
  name: string;
  phone: string;
  email: {
    primary: string;
  };
  tags?: string[];
}

/**
 * Service xử lý API liên quan đến khách hàng chuyển đổi
 */
export const CustomerService = {
  /**
   * L<PERSON>y danh sách khách hàng chuyển đổi của người dùng
   * @param params Tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi với phân trang
   */
  getConvertCustomers: async (
    params?: QueryUserConvertCustomerDto
  ): Promise<ApiResponseDto<PaginatedUserConvertCustomerResult>> => {
    return apiClient.get('/user/convert-customers', { params });
  },

  /**
   * Lấy chi tiết khách hàng chuyển đổi theo ID
   * @param id ID của khách hàng
   * @returns Chi tiết khách hàng chuyển đổi
   */
  getConvertCustomerById: async (
    id: number
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.get(`/user/convert-customers/${id}`);
  },

  /**
   * Tạo khách hàng chuyển đổi mới
   * @param data Dữ liệu khách hàng
   * @returns Khách hàng được tạo
   */
  createConvertCustomer: async (
    data: CreateConvertCustomerDto
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.post('/user/convert-customers', data);
  },

  /**
   * Cập nhật khách hàng chuyển đổi
   * @param id ID của khách hàng
   * @param data Dữ liệu cập nhật
   * @returns Khách hàng được cập nhật
   */
  updateConvertCustomer: async (
    id: number,
    data: Partial<UserConvertCustomerListItemDto>
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.put(`/user/convert-customers/${id}`, data);
  },

  /**
   * Xóa khách hàng chuyển đổi
   * @param id ID của khách hàng
   * @returns Kết quả xóa
   */
  deleteConvertCustomer: async (id: number): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/convert-customers/${id}`);
  },
};

export default CustomerService;
