import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái chuyển đổi
 */
export enum ConversionStatus {
  COMPLETED = 'completed',
  PENDING = 'pending',
  FAILED = 'failed',
}

/**
 * Enum cho loại chuyển đổi
 */
export enum ConversionType {
  LEAD = 'lead',
  CUSTOMER = 'customer',
  SALE = 'sale',
}

/**
 * Interface cho thông tin khách hàng trong chuyển đổi
 */
export interface ConversionCustomerDto {
  id: number;
  name: string;
  email?: string | Record<string, string>;
  phone?: string;
  platform?: string;
  avatar?: string;
  createdAt: number;
  updatedAt?: number;
}

/**
 * Interface cho thông tin chuyển đổi
 */
export interface ConversionDto {
  id: number;
  name: string;
  source: string;
  destination: string;
  status: ConversionStatus;
  type: ConversionType;
  value: number;
  date: string;
  customerId: number;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách chuyển đổi
 */
export interface ConversionListItemDto {
  id: string | number;
  convertCustomerId?: string | number;
  userId?: number;
  conversionType?: string;
  source?: string;
  destination?: string;
  status?: ConversionStatus;
  type?: ConversionType;
  value?: number;
  date?: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Interface cho chi tiết chuyển đổi
 */
export interface ConversionDetailDto {
  id: number;
  convertCustomerId?: number;
  userId?: number;
  conversionType?: string;
  source?: string;
  notes?: string;
  content?: Record<string, unknown>;
  createdAt: number;
  updatedAt: number;
  convertCustomer?: ConversionCustomerDto;
}

/**
 * Interface cho tham số truy vấn chuyển đổi
 */
export interface ConversionQueryParams extends QueryDto {
  status?: ConversionStatus;
  type?: ConversionType;
  startDate?: string;
  endDate?: string;
  source?: string;
  destination?: string;
}
