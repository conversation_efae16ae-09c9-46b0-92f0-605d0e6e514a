import React, { useState, useEffect, useRef } from 'react';
import {
  IconCard,
  Tooltip,
  SearchBar,
  ModernMenu,
  DatePicker,
  Checkbox,
} from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';

/**
 * Interface định nghĩa cấu trúc hiển thị cột
 */
export interface ColumnVisibility {
  id: string;
  label: string;
  visible: boolean;
}

/**
 * Interface định nghĩa map nhãn cho các cột
 * Key là id của cột, value là key translation hoặc nhãn trực tiếp
 */
export interface ColumnLabelMap {
  [columnId: string]: string;
}

/**
 * Interface định nghĩa cấu trúc cột từ Table component
 * Được sử dụng để lấy thông tin title từ columns
 */
export interface TableColumnInfo {
  key: string;
  title: string | React.ReactNode;
  dataIndex?: string;
  width?: string | number;
  sortable?: boolean;
}

/**
 * <PERSON><PERSON><PERSON> nghĩa cấu trúc cho icon bổ sung
 */
export interface AdditionalIcon {
  /**
   * Icon cần hiển thị
   */
  icon: string;

  /**
   * Tooltip cho icon
   */
  tooltip: string;

  /**
   * Variant của icon
   */
  variant?: 'default' | 'primary' | 'secondary' | 'ghost';

  /**
   * Hàm xử lý khi click vào icon
   */
  onClick: () => void;

  /**
   * Class bổ sung cho icon
   */
  className?: string;

  /**
   * Điều kiện hiển thị icon
   */
  condition?: boolean;
}

interface MenuIconBarProps {
  onSearch: (term: string) => void;
  onAdd?: () => void;
  items: ModernMenuItem[];
  onDateRangeChange?: (range: [Date | null, Date | null]) => void;
  onColumnVisibilityChange?: (columns: ColumnVisibility[]) => void;
  columns?: ColumnVisibility[];
  columnLabelMap?: ColumnLabelMap;
  tableColumns?: TableColumnInfo[];
  showDateFilter?: boolean;
  showColumnFilter?: boolean;
  /**
   * Các icon bổ sung hiển thị bên cạnh các icon mặc định
   */
  additionalIcons?: AdditionalIcon[];
  /**
   * Trạng thái loading để hiển thị loading indicator
   */
  isLoading?: boolean;
}

/**
 * Component hiển thị thanh icon chức năng với các tính năng nâng cao:
 * - Tìm kiếm
 * - Thêm mới
 * - Lọc theo menu
 * - Lọc theo khoảng thời gian
 * - Tùy chỉnh hiển thị cột
 */
const MenuIconBar: React.FC<MenuIconBarProps> = ({
  onSearch,
  onAdd,
  items,
  onDateRangeChange,
  onColumnVisibilityChange,
  columns = [],
  columnLabelMap = {},
  tableColumns = [],
  showDateFilter = true,
  showColumnFilter = true,
  additionalIcons = [],
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showColumnVisibility, setShowColumnVisibility] = useState(false);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [columnSettings, setColumnSettings] = useState<ColumnVisibility[]>(columns || []);

  // Refs để theo dõi các phần tử DOM
  const columnVisibilityRef = useRef<HTMLDivElement>(null);
  const datePickerRef = useRef<HTMLDivElement>(null);
  const filterMenuRef = useRef<HTMLDivElement>(null);

  // Cập nhật columnSettings khi columns prop thay đổi
  useEffect(() => {
    setColumnSettings(prevSettings => {
      // Đảm bảo columns không null/undefined
      const safeColumns = columns || [];
      // Chỉ update nếu columns thực sự khác với settings hiện tại
      if (JSON.stringify(safeColumns) !== JSON.stringify(prevSettings)) {
        return safeColumns;
      }
      return prevSettings;
    });
  }, [columns]);

  // Xử lý click bên ngoài để đóng các menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Đóng menu hiển thị cột khi click bên ngoài
      if (
        showColumnVisibility &&
        columnVisibilityRef.current &&
        !columnVisibilityRef.current.contains(event.target as Node)
      ) {
        setShowColumnVisibility(false);
      }

      // Đóng filter menu khi click bên ngoài
      if (
        showFilterMenu &&
        filterMenuRef.current &&
        !filterMenuRef.current.contains(event.target as Node)
      ) {
        setShowFilterMenu(false);
      }
    };

    // Thêm event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColumnVisibility, showFilterMenu]);

  // Đóng tất cả các menu khác khi mở một menu mới
  const closeAllMenus = () => {
    setShowSearch(false);
    setShowFilterMenu(false);
    setShowDatePicker(false);
    setShowColumnVisibility(false);
  };

  const handleSearchClick = () => {
    if (showSearch) {
      setShowSearch(false);
      // Nếu đang có từ khóa tìm kiếm, reset và gọi lại API
      if (searchTerm) {
        setSearchTerm('');
        onSearch('');
      }
    } else {
      closeAllMenus();
      setShowSearch(true);
    }
  };

  // Xử lý khi giá trị tìm kiếm thay đổi (chỉ cập nhật state, không gọi onSearch)
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  // Xử lý khi người dùng nhấn nút tìm kiếm hoặc submit form
  const handleSearchSubmit = (value: string) => {
    onSearch(value);
  };

  const handleAddClick = () => {
    if (onAdd) {
      onAdd();
    }
  };

  const handleStartDateChange = (date: Date | null) => {
    // Nếu ngày bắt đầu lớn hơn ngày kết thúc hiện tại, reset ngày kết thúc
    let endDate = dateRange[1];
    if (date && endDate && date > endDate) {
      endDate = null; // Reset ngày kết thúc nếu ngày bắt đầu lớn hơn
    }

    const newRange: [Date | null, Date | null] = [date, endDate];
    setDateRange(newRange);
    if (onDateRangeChange) {
      onDateRangeChange(newRange);
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    // Kiểm tra nếu ngày kết thúc nhỏ hơn ngày bắt đầu
    if (date && dateRange[0] && date < dateRange[0]) {
      // Không cho phép chọn ngày kết thúc nhỏ hơn ngày bắt đầu
      return;
    }

    // Nếu người dùng chỉ nhập ngày kết thúc (không có ngày bắt đầu)
    // thì tự động lọc từ đầu đến ngày kết thúc
    let startDate = dateRange[0];
    if (!startDate && date) {
      // Nếu không có ngày bắt đầu, có thể đặt một ngày mặc định từ xa trong quá khứ
      // hoặc để null để hệ thống hiểu là "từ đầu"
      startDate = null; // Từ đầu đến ngày kết thúc
    }

    const newRange: [Date | null, Date | null] = [startDate, date];
    setDateRange(newRange);
    if (onDateRangeChange) {
      onDateRangeChange(newRange);
    }
  };

  const handleColumnVisibilityChange = (columnId: string, checked: boolean) => {
    // Kiểm tra an toàn cho columnSettings
    if (!columnSettings || !Array.isArray(columnSettings)) {
      console.warn('columnSettings is not available or not an array');
      return;
    }

    // Xử lý trường hợp "Chọn tất cả"
    if (columnId === 'all') {
      // Nếu chọn "Chọn tất cả", hiện tất cả các cột
      if (checked) {
        const updatedColumns = columnSettings.map(col => ({
          ...col,
          visible: true,
        }));
        setColumnSettings(updatedColumns);
        if (onColumnVisibilityChange) {
          onColumnVisibilityChange(updatedColumns);
        }
      }
      // Nếu bỏ chọn "Chọn tất cả", ẩn tất cả các cột
      else {
        const updatedColumns = columnSettings.map(col => ({
          ...col,
          visible: false,
        }));
        setColumnSettings(updatedColumns);
        if (onColumnVisibilityChange) {
          onColumnVisibilityChange(updatedColumns);
        }
      }
      return;
    }

    // Xử lý trường hợp chọn/bỏ chọn một cột cụ thể
    const updatedColumns = [...columnSettings];

    // Tìm và cập nhật cột được chọn/bỏ chọn
    const columnToUpdate = updatedColumns.find(col => col.id === columnId);
    if (columnToUpdate) {
      columnToUpdate.visible = checked;
    }

    // Luôn bỏ chọn "Chọn tất cả" khi có bất kỳ cột nào bị bỏ chọn
    if (!checked) {
      const allColumn = updatedColumns.find(col => col.id === 'all');
      if (allColumn) {
        allColumn.visible = false;
      }
    }
    // Chỉ chọn "Chọn tất cả" khi tất cả các cột khác đều được chọn
    else {
      const otherColumns = updatedColumns.filter(col => col.id !== 'all');
      const allSelected = otherColumns.every(col => col.visible);
      const allColumn = updatedColumns.find(col => col.id === 'all');
      if (allColumn) {
        allColumn.visible = allSelected;
      }
    }

    setColumnSettings(updatedColumns);
    if (onColumnVisibilityChange) {
      onColumnVisibilityChange(updatedColumns);
    }
  };

  const handleDatePickerToggle = () => {
    if (showDatePicker) {
      setShowDatePicker(false);
    } else {
      closeAllMenus();
      setShowDatePicker(true);
    }
  };

  const handleColumnVisibilityToggle = () => {
    if (showColumnVisibility) {
      setShowColumnVisibility(false);
    } else {
      closeAllMenus();
      setShowColumnVisibility(true);
    }
  };

  const handleFilterMenuToggle = () => {
    if (showFilterMenu) {
      setShowFilterMenu(false);
    } else {
      closeAllMenus();
      setShowFilterMenu(true);
    }
  };

  // Hàm lấy nhãn cho cột dựa trên tableColumns, columnLabelMap hoặc label mặc định
  const getColumnLabel = (column: ColumnVisibility): string => {
    // Ưu tiên 1: Nếu có trong columnLabelMap, sử dụng translation
    if (columnLabelMap[column.id]) {
      return t(columnLabelMap[column.id], columnLabelMap[column.id]);
    }

    // Ưu tiên 2: Tìm trong tableColumns
    if (tableColumns && tableColumns.length > 0) {
      const tableColumn = tableColumns.find(col => col.key === column.id);
      if (tableColumn && typeof tableColumn.title === 'string') {
        // Nếu title là string và có dạng 'namespace:key', sử dụng translation
        if (tableColumn.title.includes(':')) {
          return t(tableColumn.title, tableColumn.title);
        }
        // Nếu title là string thông thường, trả về trực tiếp
        return tableColumn.title;
      }
    }

    // Ưu tiên 3: Sử dụng label mặc định từ column
    return column.label;
  };

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 mb-4">
      <div className="flex items-center space-x-2">
        {/* Add Button */}
        {onAdd && (
          <Tooltip content={t('common.addNew')} position="bottom">
            <IconCard icon="plus" variant="primary" onClick={handleAddClick} />
          </Tooltip>
        )}

        {/* Search Icon */}
        <Tooltip content={t('common.search')} position="bottom">
          <IconCard
            icon="search"
            variant={showSearch ? 'primary' : 'default'}
            onClick={handleSearchClick}
            active={false} /* Loại bỏ hiệu ứng border khi active */
            className={
              showSearch ? 'shadow-md' : ''
            } /* Thêm shadow để thể hiện trạng thái active */
          />
        </Tooltip>

        {/* Calendar Icon */}
        {showDateFilter && (
          <div className="relative" ref={datePickerRef}>
            <Tooltip content={t('common.dateRange')} position="bottom">
              <IconCard
                icon="calendar"
                variant={showDatePicker ? 'primary' : 'default'}
                onClick={handleDatePickerToggle}
                active={false} /* Loại bỏ hiệu ứng border khi active */
                className={
                  showDatePicker ? 'shadow-md' : ''
                } /* Thêm shadow để thể hiện trạng thái active */
              />
            </Tooltip>
          </div>
        )}

        {/* Filter Menu */}
        {items.length > 0 && (
          <div className="relative" ref={filterMenuRef}>
            <Tooltip content={t('common.filter')} position="bottom">
              <IconCard
                icon="filter"
                variant={showFilterMenu ? 'primary' : 'default'}
                onClick={() => handleFilterMenuToggle()}
                active={false} /* Loại bỏ hiệu ứng border khi active */
                className={
                  showFilterMenu ? 'shadow-md' : ''
                } /* Thêm shadow để thể hiện trạng thái active */
              />

              {showFilterMenu && (
                <ModernMenu
                  isOpen={showFilterMenu}
                  onClose={() => setShowFilterMenu(false)}
                  placement="bottom"
                  width="180px"
                  items={items}
                />
              )}
            </Tooltip>
          </div>
        )}

        {/* Column Visibility */}
        {showColumnFilter && columns.length > 0 && (
          <div className="relative" ref={columnVisibilityRef}>
            <Tooltip content={t('common.columns')} position="bottom">
              <IconCard
                icon="filter-v2"
                variant={showColumnVisibility ? 'primary' : 'default'}
                onClick={handleColumnVisibilityToggle}
                active={false} /* Loại bỏ hiệu ứng border khi active */
                className={
                  showColumnVisibility ? 'shadow-md' : ''
                } /* Thêm shadow để thể hiện trạng thái active */
              />
            </Tooltip>

            {showColumnVisibility && (
              <div
                className="absolute z-50 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden animate-fade-in"
                style={{ width: '220px', maxWidth: 'calc(100vw - 20px)' }}
              >
                <div className="p-1">
                  {/* Tiêu đề menu */}
                  <div className="px-3 py-2 text-gray-800 dark:text-white font-medium border-b border-gray-200 dark:border-gray-700 mb-1">
                    {t('common.columns')}
                  </div>

                  {/* Chọn tất cả */}
                  <button
                    className={`
                      flex items-center w-full px-3 py-2 text-left transition-colors
                      ${
                        columnSettings?.find(col => col.id === 'all')?.visible
                          ? `bg-gray-100 dark:bg-gray-700 ${
                              // Kiểm tra xem item đầu tiên trong danh sách có được chọn không
                              columnSettings.filter(col => col.id !== 'all').length > 0 &&
                              columnSettings.filter(col => col.id !== 'all')[0]?.visible
                                ? 'rounded-t-md rounded-b-none' // Nếu item đầu tiên được chọn, chỉ có border radius ở trên và không có ở dưới
                                : 'rounded-md' // Nếu không, có border radius ở cả trên và dưới
                            }`
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md'
                      }
                    `}
                    onClick={() =>
                      handleColumnVisibilityChange(
                        'all',
                        !columnSettings?.find(col => col.id === 'all')?.visible
                      )
                    }
                  >
                    <div className="mr-2 flex-shrink-0">
                      <Checkbox
                        id="column-all"
                        checked={columnSettings?.find(col => col.id === 'all')?.visible || false}
                        onChange={checked => handleColumnVisibilityChange('all', checked)}
                        variant="filled"
                        size="sm"
                      />
                    </div>
                    <span className="truncate">{t('common.selectAll')}</span>
                  </button>

                  {/* Danh sách cột */}
                  {columnSettings && Array.isArray(columnSettings) && columnSettings
                    .filter(column => column.id !== 'all')
                    .map((column, index, filteredColumns) => {
                      // Kiểm tra xem item trước và sau có được chọn không
                      const prevItemSelected = index > 0 && filteredColumns[index - 1].visible;
                      const nextItemSelected =
                        index < filteredColumns.length - 1 && filteredColumns[index + 1].visible;
                      const isSelected = column.visible;

                      // Xác định border radius dựa trên trạng thái của các item liền kề
                      let borderRadiusClass = 'rounded-md';
                      if (isSelected) {
                        // Kiểm tra xem đây có phải là item đầu tiên và "Chọn tất cả" có được chọn không
                        const isFirstItem = index === 0;
                        const isAllSelected = columnSettings.find(
                          col => col.id === 'all'
                        )?.visible;

                        if (isFirstItem && isAllSelected) {
                          // Nếu là item đầu tiên và "Chọn tất cả" được chọn
                          if (nextItemSelected) {
                            // Nếu item tiếp theo cũng được chọn, không có border radius
                            borderRadiusClass = 'rounded-t-none';
                          } else {
                            // Nếu item tiếp theo không được chọn, chỉ có border radius ở dưới
                            borderRadiusClass = 'rounded-t-none rounded-b-md';
                          }
                        } else if (prevItemSelected && nextItemSelected) {
                          // Nếu cả item trước và sau đều được chọn, không có border radius
                          borderRadiusClass = '';
                        } else if (prevItemSelected) {
                          // Nếu chỉ item trước được chọn, chỉ có border radius ở dưới
                          borderRadiusClass = 'rounded-b-md';
                        } else if (nextItemSelected) {
                          // Nếu chỉ item sau được chọn, chỉ có border radius ở trên
                          borderRadiusClass = 'rounded-t-md';
                        }
                      }

                      return (
                        <button
                          key={column.id}
                          className={`
                          flex items-center w-full px-3 py-2 text-left transition-colors
                          ${
                            isSelected
                              ? `bg-gray-100 dark:bg-gray-700 ${borderRadiusClass}`
                              : 'hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md'
                          }
                        `}
                          onClick={() => handleColumnVisibilityChange(column.id, !column.visible)}
                        >
                          <div className="mr-2 flex-shrink-0">
                            <Checkbox
                              id={`column-${column.id}`}
                              checked={column.visible}
                              onChange={checked =>
                                handleColumnVisibilityChange(column.id, checked)
                              }
                              variant="filled"
                              size="sm"
                            />
                          </div>
                          <span className="truncate">{getColumnLabel(column)}</span>
                        </button>
                      );
                    })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
          </div>
        )}

        {/* Additional Icons */}
        {additionalIcons.map((iconConfig, index) =>
          // Chỉ hiển thị icon nếu không có điều kiện hoặc điều kiện là true
          (iconConfig.condition === undefined || iconConfig.condition) && (
            <div key={index} className="relative">
              <Tooltip content={iconConfig.tooltip} position="bottom">
                <IconCard
                  icon={iconConfig.icon}
                  variant={iconConfig.variant || 'default'}
                  onClick={iconConfig.onClick}
                  active={false}
                  className={iconConfig.className || ''}
                />
              </Tooltip>
            </div>
          )
        )}
      </div>

      {/* Search Bar */}
      <div className="w-full sm:w-auto">
        <SearchBar
          visible={showSearch}
          value={searchTerm}
          onChange={handleSearchChange}
          onSubmit={handleSearchSubmit}
          onToggle={handleSearchClick}
          maxWidth="100%"
          variant="flat"
          autoFocus={true}
          showSearchIcon={false}
          className="w-full"
          searchOnEnter={true}
        />
      </div>

      {/* Date Range Picker */}
      {showDateFilter && (
        <div
          className={`w-full sm:w-auto overflow-hidden transition-all duration-300 ease-in-out ${showDatePicker ? 'max-w-[500px] opacity-100' : 'max-w-0 opacity-0'}`}
          ref={datePickerRef}
        >
          <div className="relative flex items-center space-x-2">
            <DatePicker
              value={dateRange[0]}
              onChange={handleStartDateChange}
              placeholder={t('common.startDate')}
              fullWidth={false}
            />
            <DatePicker
              value={dateRange[1]}
              onChange={handleEndDateChange}
              placeholder={t('common.endDate')}
              fullWidth={false}
              minDate={dateRange[0] || undefined}
            />
            {(dateRange[0] || dateRange[1]) && (
              <Tooltip content={t('common.clearFilter')} position="bottom">
                <IconCard
                  icon="x"
                  variant="default"
                  onClick={() => {
                    setDateRange([null, null]);
                    if (onDateRangeChange) {
                      onDateRangeChange([null, null]);
                    }
                  }}
                  size="sm"
                />
              </Tooltip>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuIconBar;
