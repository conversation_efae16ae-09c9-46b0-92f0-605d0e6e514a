/**
 * Component form thông tin doanh nghiệp
 */
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FieldValues } from 'react-hook-form';
import { Typography, Button, Icon, Form, FormItem, Input } from '@/shared/components/common';
import { ContractStepProps, BusinessInfo } from '../types';
import { FormRef } from '@/shared/components/common/Form/Form';

const BusinessInfoForm: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Validation schema
  const schema = z.object({
    companyName: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(2, t('contract:validation.minLength', { length: 2 }))
      .max(100, t('contract:validation.maxLength', { length: 100 })),
    taxCode: z
      .string()
      .min(1, t('contract:validation.required'))
      .regex(/^[0-9]{10,13}$/, t('contract:validation.taxCode')),
    companyEmail: z
      .string()
      .min(1, t('contract:validation.required'))
      .email(t('contract:validation.email')),
    companyAddress: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(10, t('contract:validation.minLength', { length: 10 }))
      .max(200, t('contract:validation.maxLength', { length: 200 })),
    companyPhone: z
      .string()
      .min(1, t('contract:validation.required'))
      .regex(/^[0-9+\-\s()]{10,15}$/, t('contract:validation.phone')),
    representative: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(2, t('contract:validation.minLength', { length: 2 }))
      .max(50, t('contract:validation.maxLength', { length: 50 })),
    position: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(2, t('contract:validation.minLength', { length: 2 }))
      .max(50, t('contract:validation.maxLength', { length: 50 })),
  });

  const handleSubmit = (formData: FieldValues) => {
    onNext({ businessInfo: formData as BusinessInfo });
  };

  return (
    <div className="w-full p-6">
      <div className="text-center mb-8">
        <Typography variant="h2" className="mb-4">
          {t('contract:businessInfo.title')}
        </Typography>
      </div>

      <Form
        ref={formRef}
        schema={schema}
        onSubmit={handleSubmit}
        defaultValues={data.businessInfo}
      >
        <div className="space-y-6">
          {/* Tên công ty */}
          <FormItem name="companyName" label={t('contract:businessInfo.companyName')} required>
            <Input
              placeholder={t('contract:businessInfo.placeholders.companyName')}
              className="w-full"
            />
          </FormItem>

          {/* Mã số thuế */}
          <FormItem name="taxCode" label={t('contract:businessInfo.taxCode')} required>
            <Input
              placeholder={t('contract:businessInfo.placeholders.taxCode')}
              className="w-full"
            />
          </FormItem>

          {/* Email công ty */}
          <FormItem name="companyEmail" label={t('contract:businessInfo.companyEmail')} required>
            <Input
              type="email"
              placeholder={t('contract:businessInfo.placeholders.companyEmail')}
              className="w-full"
            />
          </FormItem>

          {/* Số điện thoại công ty */}
          <FormItem name="companyPhone" label={t('contract:businessInfo.companyPhone')} required>
            <Input
              type="tel"
              placeholder={t('contract:businessInfo.placeholders.companyPhone')}
              className="w-full"
            />
          </FormItem>

          {/* Địa chỉ công ty */}
          <FormItem name="companyAddress" label={t('contract:businessInfo.companyAddress')} required>
            <Input
              placeholder={t('contract:businessInfo.placeholders.companyAddress')}
              className="w-full"
            />
          </FormItem>

          {/* Người đại diện */}
          <FormItem name="representative" label={t('contract:businessInfo.representative')} required>
            <Input
              placeholder={t('contract:businessInfo.placeholders.representative')}
              className="w-full"
            />
          </FormItem>

          {/* Chức vụ */}
          <FormItem name="position" label={t('contract:businessInfo.position')} required>
            <Input
              placeholder={t('contract:businessInfo.placeholders.position')}
              className="w-full"
            />
          </FormItem>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            leftIcon={<Icon name="chevron-left" />}
            onClick={onPrevious}
            disabled={isLoading}
          >
            {t('contract:actions.previous')}
          </Button>

          <Button
            type="submit"
            variant="primary"
            rightIcon={<Icon name="chevron-right" />}
            isLoading={isLoading}
          >
            {t('contract:actions.next')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default BusinessInfoForm;
