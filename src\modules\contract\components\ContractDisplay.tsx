/**
 * Component hiển thị hợp đồng
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon } from '@/shared/components/common';
import { ContractStepProps } from '../types';
import PDFViewer from './PDFViewer';

const ContractDisplay: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');

  // Demo contract URL - sử dụng URL hợp đồng thực tế
  const contractUrl = data.contractUrl || 'https://cdn.redai.vn/contract/HDRULEBusiness';

  const handleNext = () => {
    onNext({});
  };

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <Typography variant="h3" className="mb-4">
          {t('contract:contractDisplay.title')}
        </Typography>
      </div>

      {/* PDF Viewer */}
      <div className="mb-8">
        <PDFViewer
          url={contractUrl}
          base64={data.contractBase64}
          title={t('contract:contractDisplay.title')}
          height="700px"
          showDownload={true}
        />
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          leftIcon={<Icon name="chevron-left" />}
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract:actions.previous')}
        </Button>

        <Button
          variant="primary"
          rightIcon={<Icon name="chevron-right" />}
          onClick={handleNext}
          isLoading={isLoading}
        >
          {t('contract:actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default ContractDisplay;
