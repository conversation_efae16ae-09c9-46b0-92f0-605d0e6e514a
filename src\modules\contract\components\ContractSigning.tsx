/**
 * Component ký hợp đồng (tải xuống và tải lên)
 */
import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon } from '@/shared/components/common';
import { ContractStepProps } from '../types';

const ContractSigning: React.FC<ContractStepProps> = ({ onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadError, setUploadError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      setUploadError(t('contract:validation.fileType'));
      return;
    }

    // Validate file size (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      setUploadError(t('contract:validation.fileSize'));
      return;
    }

    setUploadedFile(file);
    setUploadError('');
  };

  const handleRemoveFile = () => {
    setUploadedFile(null);
    setUploadError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleNext = () => {
    if (!uploadedFile) {
      setUploadError(t('contract:validation.fileRequired'));
      return;
    }

    // Convert file to base64 for storage (in real app, upload to server)
    const reader = new FileReader();
    reader.onload = () => {
      onNext({ contractBase64: reader.result as string });
    };
    reader.readAsDataURL(uploadedFile);
  };

  const handleDownload = () => {
    // Mock download - in real app, get contract URL from API
    const link = document.createElement('a');
    link.href = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
    link.download = 'contract.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-8">
        <Typography variant="h2" className="mb-4">
          {t('signing.title')}
        </Typography>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Download Contract */}
        <Card>
          <div className="p-6 text-center">
            <Icon name="download" size="xl" className="text-primary mb-4" />
            <Typography variant="h4" className="mb-2">
              {t('signing.step1Title')}
            </Typography>
            <Typography variant="body2" className="text-muted mb-4">
              {t('signing.step1Description')}
            </Typography>
            <Button
              variant="primary"
              leftIcon={<Icon name="download" />}
              onClick={handleDownload}
              fullWidth
            >
              {t('contractDisplay.download')}
            </Button>
          </div>
        </Card>

        {/* Upload Signed Contract */}
        <Card>
          <div className="p-6">
            <div className="text-center mb-4">
              <Icon name="upload" size="xl" className="text-primary mb-4" />
              <Typography variant="h4" className="mb-2">
                {t('signing.step2Title')}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {t('signing.step2Description')}
              </Typography>
            </div>

            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                uploadedFile
                  ? 'border-success bg-success/5'
                  : 'border-border hover:border-primary hover:bg-primary/5'
              }`}
              onClick={handleFileSelect}
            >
              {uploadedFile ? (
                <div>
                  <Icon name="check-circle" size="lg" className="text-success mb-2" />
                  <Typography variant="body1" className="font-medium mb-1">
                    {uploadedFile.name}
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    {formatFileSize(uploadedFile.size)}
                  </Typography>
                </div>
              ) : (
                <div>
                  <Icon name="upload" size="lg" className="text-muted mb-2" />
                  <Typography variant="body1" className="mb-1">
                    {t('signing.selectFile')}
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    {t('signing.supportedFormats')}
                    <br />
                    {t('signing.maxSize')}
                  </Typography>
                </div>
              )}
            </div>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={handleFileChange}
              className="hidden"
            />

            {/* Error message */}
            {uploadError && (
              <Typography variant="body2" className="text-error mt-2 text-center">
                {uploadError}
              </Typography>
            )}

            {/* Remove file button */}
            {uploadedFile && (
              <div className="text-center mt-4">
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<Icon name="trash-2" />}
                  onClick={handleRemoveFile}
                >
                  {t('signing.removeFile')}
                </Button>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          leftIcon={<Icon name="chevron-left" />}
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('actions.previous')}
        </Button>

        <Button
          variant="primary"
          rightIcon={<Icon name="chevron-right" />}
          onClick={handleNext}
          disabled={!uploadedFile}
          isLoading={isLoading}
        >
          {t('actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default ContractSigning;
