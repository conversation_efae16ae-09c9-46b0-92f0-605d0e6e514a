/**
 * Component chọn lo<PERSON>i hợp đồng (<PERSON><PERSON><PERSON> nghiệp/<PERSON><PERSON> nhân)
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon } from '@/shared/components/common';
import { ContractType, ContractStepProps } from '../types';

const ContractTypeSelector: React.FC<ContractStepProps> = ({ data, onNext }) => {
  const { t } = useTranslation('contract');

  const handleSelectType = (type: ContractType) => {
    onNext({ type });
  };

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <Typography variant="h3" className="mb-4">
          {t('contract:types.selectType')}
        </Typography>
        <Typography variant="body1" className="text-muted">
          {t('contract:types.selectTypeDescription')}
        </Typography>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {/* <PERSON><PERSON>h nghiệp */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
            data.type === ContractType.BUSINESS
              ? 'bg-primary/5 shadow-lg'
              : 'hover:shadow-md'
          }`}
          onClick={() => handleSelectType(ContractType.BUSINESS)}
        >
          <div className="text-center p-6">
            <div className="mb-4">
              <Icon
                name="building"
                size="xl"
                className={`mx-auto ${
                  data.type === ContractType.BUSINESS ? 'text-primary' : 'text-muted'
                }`}
              />
            </div>
            <Typography variant="h4" className="mb-2">
              {t('contract:types.business')}
            </Typography>
            <Typography variant="body2" className="text-muted mb-4">
              Dành cho các doanh nghiệp, công ty
            </Typography>
            <Button
              variant="outline"
              fullWidth
              onClick={() => handleSelectType(ContractType.BUSINESS)}
            >
              Chọn
            </Button>
          </div>
        </Card>

        {/* Cá nhân */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
            data.type === ContractType.PERSONAL
              ? 'bg-primary/5 shadow-lg'
              : 'hover:shadow-md'
          }`}
          onClick={() => handleSelectType(ContractType.PERSONAL)}
        >
          <div className="text-center p-6">
            <div className="mb-4">
              <Icon
                name="user"
                size="xl"
                className={`mx-auto ${
                  data.type === ContractType.PERSONAL ? 'text-primary' : 'text-muted'
                }`}
              />
            </div>
            <Typography variant="h4" className="mb-2">
              {t('contract:types.personal')}
            </Typography>
            <Typography variant="body2" className="text-muted mb-4">
              Dành cho cá nhân
            </Typography>
            <Button
              variant="outline"
              fullWidth
              onClick={() => handleSelectType(ContractType.PERSONAL)}
            >
              Chọn
            </Button>
          </div>
        </Card>
      </div>

      {/* Next button */}
      {data.type && (
        <div className="flex justify-center mt-8">
          <Button
            variant="primary"
            size="lg"
            rightIcon={<Icon name="chevron-right" />}
            onClick={() => onNext({})}
          >
            {t('contract:actions.next')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ContractTypeSelector;
