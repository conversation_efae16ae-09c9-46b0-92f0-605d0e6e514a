/**
 * Component xác thực OTP
 */
import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon } from '@/shared/components/common';
import { ContractStepProps } from '../types';

const OTPVerification: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [countdown]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single digit
    if (!/^\d*$/.test(value)) return; // Only allow numbers

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');

    // Auto focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    const newOtp = [...otp];

    for (let i = 0; i < pastedData.length && i < 6; i++) {
      newOtp[i] = pastedData[i];
    }

    setOtp(newOtp);
    setError('');

    // Focus the next empty input or the last input
    const nextIndex = Math.min(pastedData.length, 5);
    inputRefs.current[nextIndex]?.focus();
  };

  const handleVerify = () => {
    const otpCode = otp.join('');

    if (otpCode.length !== 6) {
      setError(t('contract:validation.otpLength'));
      return;
    }

    // Mock verification - in real app, call API
    if (otpCode === '123456') {
      onNext({ otpCode, isCompleted: true });
    } else {
      setError(t('contract:validation.otpIncorrect'));
    }
  };

  const handleResend = () => {
    // Mock resend - in real app, call API
    setCountdown(60);
    setOtp(['', '', '', '', '', '']);
    setError('');
    inputRefs.current[0]?.focus();
  };

  const isOtpComplete = otp.every(digit => digit !== '');
  const phoneNumber = data.personalInfo?.phone || data.businessInfo?.companyPhone || '';

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <Typography variant="h3" className="mb-4">
          {t('contract:otp.title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-2">
          {t('contract:otp.instruction')}
        </Typography>
        {phoneNumber && (
          <Typography variant="body2" className="text-primary font-medium">
            {phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, '$1 *** $3')}
          </Typography>
        )}
      </div>

      <div className="mb-6">
          {/* OTP Input */}
          <div className="flex justify-center space-x-2 sm:space-x-3 mb-6">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={el => inputRefs.current[index] = el}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={e => handleOtpChange(index, e.target.value)}
                onKeyDown={e => handleKeyDown(index, e)}
                onPaste={handlePaste}
                className={`w-10 h-10 sm:w-12 sm:h-12 text-center text-lg font-semibold border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200 ${
                  error
                    ? 'border-error bg-error/5 animate-shake'
                    : digit
                    ? 'border-primary bg-primary/5'
                    : 'border-border focus:border-primary hover:border-primary/50'
                }`}
                autoComplete="one-time-code"
              />
            ))}
          </div>

          {/* Error message */}
          {error && (
            <div className="text-center mb-4">
              <Typography variant="body2" className="text-error">
                {error}
              </Typography>
            </div>
          )}

          {/* Resend button */}
          <div className="text-center mb-6">
            {countdown > 0 ? (
              <Typography variant="body2" className="text-muted">
                Gửi lại mã sau {countdown}s
              </Typography>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResend}
                leftIcon={<Icon name="refresh-cw" />}
              >
                {t('contract:otp.resend')}
              </Button>
            )}
          </div>

        {/* Verify button */}
        <Button
          variant="primary"
          fullWidth
          onClick={handleVerify}
          disabled={!isOtpComplete}
          isLoading={isLoading}
        >
          {t('contract:otp.verify')}
        </Button>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          leftIcon={<Icon name="chevron-left" />}
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract:actions.previous')}
        </Button>
      </div>
    </div>
  );
};

export default OTPVerification;
