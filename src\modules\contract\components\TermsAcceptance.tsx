/**
 * Component chấp nhận đi<PERSON>u kho<PERSON>n
 */
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon, Checkbox } from '@/shared/components/common';
import { ContractStepProps } from '../types';

const TermsAcceptance: React.FC<ContractStepProps> = ({ data, onNext, onPrevious }) => {
  const { t } = useTranslation('contract');
  const [accepted, setAccepted] = useState(data.termsAccepted || false);
  const [showError, setShowError] = useState(false);

  const handleNext = () => {
    if (!accepted) {
      setShowError(true);
      return;
    }
    onNext({ termsAccepted: true });
  };

  const handleAcceptChange = (value: boolean) => {
    setAccepted(value);
    setShowError(false);
  };

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <Typography variant="h3" className="mb-4">
          {t('contract:terms.title')}
        </Typography>
      </div>

      <div className="mb-6">
        <div className="max-h-96 overflow-y-auto p-4 mb-6 bg-background">
          <div className="space-y-6 text-sm leading-relaxed">
            {/* Header */}
            <div className="text-center">
              <Typography variant="h4" className="font-bold mb-2">{t('contract:terms.content.header')}</Typography>
              <Typography variant="body2" className="text-muted-foreground">{t('contract:terms.content.lastUpdated')}</Typography>
            </div>

            {/* Mở đầu */}
            <div className="space-y-3">
              <p>{t('contract:terms.content.introduction.welcome')}</p>
              <p>{t('contract:terms.content.introduction.purpose')}</p>
              <p>{t('contract:terms.content.introduction.changes')}</p>
            </div>

            {/* ĐIỀU 1: GIẢI THÍCH TỪ NGỮ */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">{t('contract:terms.content.article1.title')}</Typography>
              <div className="space-y-2">
                <p><strong>{t('contract:terms.content.article1.definitions.redon')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.customer')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.partner')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.redai')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.service')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.account')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.api')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.thirdParty')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.userData')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.personalInfo')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.confidentialInfo')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.rpoint')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.intellectualProperty')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.violation')}</strong></p>
                <p><strong>{t('contract:terms.content.article1.definitions.technicalIncident')}</strong></p>
              </div>
            </div>

            {/* ĐIỀU 2: CÁC HÀNH VI CẤM THỰC HIỆN */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">{t('contract:terms.content.article2.title')}</Typography>
              <p className="mb-3">{t('contract:terms.content.article2.intro')}</p>

              <div className="space-y-3">
                <div>
                  <p className="font-medium">{t('contract:terms.content.article2.sections.illegal.title')}</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    {(t('contract:terms.content.article2.sections.illegal.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <p className="font-medium">{t('contract:terms.content.article2.sections.security.title')}</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    {(t('contract:terms.content.article2.sections.security.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <p className="font-medium">{t('contract:terms.content.article2.sections.promotion.title')}</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    {(t('contract:terms.content.article2.sections.promotion.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <p className="font-medium">{t('contract:terms.content.article2.sections.interference.title')}</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    {(t('contract:terms.content.article2.sections.interference.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <p className="font-medium">{t('contract:terms.content.article2.sections.enforcement')}</p>
                </div>

                <div>
                  <p className="font-medium">{t('contract:terms.content.article2.sections.liability')}</p>
                </div>
              </div>
            </div>

            {/* ĐIỀU 3: QUYỀN VÀ NGHĨA VỤ CỦA KHÁCH HÀNG */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">{t('contract:terms.content.article3.title')}</Typography>
              <div className="space-y-3">
                <div>
                  <p className="font-medium mb-2">{t('contract:terms.content.article3.rightsTitle')}</p>
                  {(t('contract:terms.content.article3.rights', { returnObjects: true }) as string[]).map((right: string, index: number) => (
                    <p key={index} className="font-medium mb-2">{right}</p>
                  ))}
                </div>
                <div>
                  <p className="font-medium mb-2">{t('contract:terms.content.article3.obligationsTitle')}</p>
                  {(t('contract:terms.content.article3.obligations', { returnObjects: true }) as string[]).map((obligation: string, index: number) => (
                    <p key={index} className="font-medium mb-2">{obligation}</p>
                  ))}
                </div>
              </div>
            </div>

            {/* Lưu ý: Các điều khoản khác */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">{t('contract:terms.content.otherTerms.title')}</Typography>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  {t('contract:terms.content.otherTerms.description')}
                </p>
              </div>
            </div>

            {/* Thông tin liên hệ */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">{t('contract:terms.content.contactInfo.title')}</Typography>
              <div className="space-y-1">
                <p><strong>{t('contract:terms.content.contactInfo.companyName')}</strong></p>
                <p>{t('contract:terms.content.contactInfo.address')}</p>
                <p>{t('contract:terms.content.contactInfo.businessLicense')}</p>
                <p>{t('contract:terms.content.contactInfo.hotline')}</p>
                <p>{t('contract:terms.content.contactInfo.email')}</p>
                <p>{t('contract:terms.content.contactInfo.website')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Checkbox chấp nhận */}
        <div className="space-y-2">
          <Checkbox
            id="terms-accept"
            label={t('contract:terms.accept')}
            checked={accepted}
            onChange={handleAcceptChange}
            variant="filled"
            color="primary"
            size="md"
          />
          {showError && (
            <p className="text-error text-sm">
              {t('contract:terms.mustAccept')}
            </p>
          )}
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          leftIcon={<Icon name="chevron-left" />}
          onClick={onPrevious}
        >
          {t('contract:actions.previous')}
        </Button>

        <Button
          variant="primary"
          rightIcon={<Icon name="chevron-right" />}
          onClick={handleNext}
          disabled={!accepted}
        >
          {t('contract:actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default TermsAcceptance;
