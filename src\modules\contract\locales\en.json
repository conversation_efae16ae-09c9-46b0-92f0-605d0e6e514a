{"contract": {"principle": {"title": "Principle Contract", "description": "Sign principle contract with RedAI", "steps": {"typeSelection": "Select contract type", "termsAcceptance": "Accept terms", "infoForm": "Contract information", "contractDisplay": "Contract display", "handSignature": "Hand signature", "otpVerification": "OTP verification", "completed": "Completed"}}, "types": {"business": "Business", "personal": "Personal", "selectType": "Select contract type", "selectTypeDescription": "Please select the contract type that suits you"}, "terms": {"title": "Terms and conditions", "accept": "I agree to the terms and conditions", "mustAccept": "You must accept the terms to continue", "content": {"header": "TERMS OF SERVICE & PRIVACY POLICY", "lastUpdated": "Last updated: December 18, 2024", "introduction": {"welcome": "Welcome to RedAI, provided by REDON Investment and Trading Joint Stock Company (hereinafter referred to as \"REDON\", \"we\" or \"company\"). By registering, accessing and using services at website https://redai.vn, you acknowledge that you have read, understood and agree to comply with all contents of this Terms of Service & Privacy Policy.", "purpose": "This Terms of Service & Privacy Policy is established to regulate the rights, obligations, responsibilities and relationships between REDON and all individuals, organizations/enterprises using services provided by RedAI (hereinafter referred to as \"users\"). The policy applies to all activities of accessing, using or cooperating related to RedAI services and ecosystem, including but not limited to customers, partners and other related parties.", "changes": "We reserve the right to change, update or modify any content in this Terms of Service & Privacy Policy at any time. Changes will be publicly posted on RedAI website and take effect immediately upon update. Your continued use of the service after changes means you have accepted the updated terms. If you do not agree with any content in this policy, please stop accessing and using RedAI services."}, "article1": {"title": "ARTICLE 1: DEFINITIONS", "definitions": {"redon": "\"We\", \"REDON\": Refers to REDON Investment and Trading Joint Stock Company, its branches, affiliated organizations and employees, representatives of the company.", "customer": "\"You\", \"Customer\": Refers to any individual or organization/enterprise accessing the website, registering an account, accessing and using services on RedAI software.", "partner": "\"Partner\": Refers to individuals, organizations cooperating with REDON to provide, distribute or integrate services related to RedAI software.", "redai": "\"RedAI\", \"RedAI Software\": Refers to the software system providing multi-task virtual assistant services for individuals and organizations/enterprises, owned and operated by REDON Investment and Trading Joint Stock Company. Including the official website at https://redai.vn, related products, features and services.", "service": "\"Service\": Includes all features, tools, APIs and other services provided by RedAI through web platform or other forms.", "account": "\"Account\": Refers to the account registered by Customer on RedAI Software, allowing access and use of services according to regulations in this Terms of Service & Privacy Policy.", "api": "\"API (Application Programming Interface)\": Refers to the application programming interface provided by RedAI to connect and integrate with third-party systems.", "thirdParty": "\"Third Party\": Refers to individuals or organizations/enterprises not directly under REDON, but have cooperation, provide services or integrate into RedAI Software.", "userData": "\"User Data\": Includes all personal information, transaction history, usage behavior, and data generated or collected during users' access and use of RedAI services such as: order information, URLs, images, or conversions.", "personalInfo": "\"Personal Information\": Refers to information that can identify an individual, including but not limited to: full name, date of birth, phone number, email address, bank account, transaction history and other information related to using RedAI services.", "confidentialInfo": "\"Confidential Information\": Refers to all information related to business, financial, technical activities, customer personal data and other information that REDON and Customer commit to keep confidential, not disclose to third parties except with consent or legal requirements.", "rpoint": "\"R-Point\": Refers to an internal transaction tool on RedAI Software, used to use or activate services and features on RedAI Software. R-Point is only valid for use within RedAI system, cannot be converted to cash, non-transferable and non-refundable after transaction.", "intellectualProperty": "\"Intellectual Property Rights\": Includes all rights related to software, technology, data, content and other intellectual assets owned by REDON, and rights protected under Vietnamese intellectual property law.", "violation": "\"Violation\": Refers to any action by users or related parties that violates regulations in this Terms of Service & Privacy Policy, including but not limited to unauthorized access, misuse, fraud or using services to perform illegal activities.", "technicalIncident": "\"Technical Incident\": Refers to any error, malfunction or downtime of RedAI software that is not due to user error or non-compliance with Terms of Service & Privacy Policy."}}, "article2": {"title": "ARTICLE 2: PROHIBITED ACTS WHEN ACCESSING WEBSITE AND/OR USING SERVICES", "intro": "To ensure a safe service environment and compliance with legal regulations, the following acts are strictly prohibited when accessing and/or using RedAI services:", "sections": {"illegal": {"title": "2.1. Illegal acts and ethical violations:", "items": ["Using RedAI to perform any actions that violate Vietnamese law, including but not limited to: distorting, defaming, making negative statements about organizations, individuals, state agencies, especially the Party and State of Vietnam.", "Disseminating, transmitting or storing content contrary to fine customs and traditions, inciting violence, racial discrimination, prejudice, insulting the honor of individuals or organizations/enterprises.", "Taking advantage of services to support or perform fraudulent, deceptive acts or other illegal activities."]}, "security": {"title": "2.2. Service and security violations:", "items": ["Abusing AI features or RedAI software to create, share or transmit inappropriate, offensive, misleading, fraudulent content, violating copyrights, intellectual property of third parties without permission or harming legitimate interests of third parties.", "Creating or sharing misleading content, causing public confusion or transmitting false information about RedAI, REDON or company partners.", "Abusing payment features, R-Point conversion, Affiliate program or related features to create fake transactions, manipulate data, fraud or illegal profit, distorting software activity data.", "Collecting, storing or unlawfully disclosing personal information, customer data or REDON business data without clear consent.", "Spamming messages or causing annoyance to others through RedAI services.", "Attacking, unauthorized access, exploiting vulnerabilities or performing acts that affect system security and safety."]}, "promotion": {"title": "2.3. Violations of promotion and linking policies:", "items": ["Using keywords related to RedAI brand in promotional campaigns, search engine advertising (SEM) or advertising content without written permission from REDON.", "Creating invalid orders, customers or spam referral links in Affiliate program for profit or causing damage to REDON or third parties."]}, "interference": {"title": "2.4. Interference with software operations:", "items": ["Using any methods, tools or devices to interfere, disrupt, sabotage or degrade the performance of RedAI website, system or services.", "Distributing viruses, malware or using technology to harm software or illegally infiltrate parts of RedAI or third-party systems integrated with RedAI."]}, "enforcement": "2.5. REDON has the right to immediately lock accounts, suspend or disable services for violating users without prior notice. We will apply necessary technical measures to protect the system and prevent damage caused by violations. At the same time, REDON has the right to sue or demand compensation from violating users.", "liability": "2.6. REDON is not responsible for legal, financial or any losses arising from user violations. All consequences, including financial, legal or reputational damage arising from violations, shall be the sole responsibility of the violating user."}}, "article3": {"title": "ARTICLE 3: RIGHTS AND OBLIGATIONS OF CUSTOMERS", "rightsTitle": "Customer rights:", "obligationsTitle": "Customer obligations:", "rights": ["3.1. Customers are entitled to access and use the Software stably and continuously within the scope of actual technical and operational conditions of RedAI, except for cases of Force Majeure events or REDON's system maintenance responsibilities.", "3.2. Customers are entitled to access and use customer support materials, including but not limited to: user guides, customer support portal, online help pages at https://help.redai.vn/.", "3.3. Customer personal information and transaction data will be protected according to REDON's Personal Data Protection Policy and current legal regulations.", "3.4. During use, customers are entitled to warranty and support for issues arising from Software technical errors. If errors or incidents occur, REDON will notify customers and implement remedial measures to ensure stable and continuous Software operation.", "3.5. Customers have the right to complain and request resolution of issues related to RedAI services. Complaints will be handled according to REDON's regulations.", "3.6. Customers have the right to terminate service use according to regulations, provided they fully comply with payment obligations and do not violate related terms."], "obligations": ["3.7. Customers are obligated to pay fully and on time all fees related to RedAI services, including but not limited to usage fees, API fees or other fees according to REDON's agreements or regulations.", "3.8. Customers are responsible for securing account information, including: username, password and related information. All actions performed through the account will be considered as performed by the customer and held responsible.", "3.9. Customers must provide accurate, complete and timely information when registering accounts or using services. Customers are responsible for updating contact information if there are changes. REDON is not responsible for damages due to providing misleading information.", "3.10. Customers must not use RedAI services for illegal purposes or acts that harm the system, including but not limited to spam, spreading misleading information or using AI to perform illegal acts.", "3.11. Customers are fully responsible for all content created and/or sent by AI or customers themselves on the Software. Ensure that this content has the consent of related parties, does not violate laws, third-party intellectual property rights and complies with this Terms of Service & Privacy Policy.", "3.12. Customers must not use accounts or RedAI Software for resale or transfer in any form, nor transfer or assign all or part of their rights and obligations to any third party without written consent from REDON.", "3.13. Agree that during operation, RedAI Software may encounter incidents due to objective reasons such as system errors, periodic maintenance, widespread network incidents or network attacks (hackers, crackers...). In these cases, customers commit not to complain, sue or pursue litigation against REDON.", "3.14. Commit not to promote, introduce or use RedAI images in ways that negatively affect REDON's reputation and interests."]}, "otherTerms": {"title": "Other Terms", "description": "In addition to the main terms mentioned above, there are other terms regarding REDON's rights and responsibilities, payment regulations, refund policy, liability exemption and agreement validity. Please refer to the complete content at the official website: https://redai.vn/chinh-sach-va-dieu-khoan-su-dung"}, "contactInfo": {"title": "CONTACT INFORMATION", "companyName": "REDON INVESTMENT AND TRADING JOINT STOCK COMPANY", "address": "Address: No. 40/74 Huu Hung Street, Dai Mo Ward, Nam Tu Liem District, Hanoi City, Vietnam", "businessLicense": "Business License: ********** issued on April 29, 2020 by Hanoi Department of Planning and Investment", "hotline": "Hotline: 0965.262.824", "email": "Email: <EMAIL>", "website": "Website: https://redai.vn"}}}, "businessInfo": {"title": "Business information", "companyName": "Company name", "taxCode": "Tax code", "companyEmail": "Company email", "companyAddress": "Company address", "companyPhone": "Company phone", "representative": "Representative", "position": "Position", "placeholders": {"companyName": "Enter company name", "taxCode": "Enter tax code", "companyEmail": "Enter company email", "companyAddress": "Enter company address", "companyPhone": "Enter company phone", "representative": "Enter representative name", "position": "Enter position"}}, "personalInfo": {"title": "Personal information", "fullName": "Full name", "dateOfBirth": "Date of birth", "idNumber": "ID number", "idIssuedDate": "ID issued date", "idIssuedPlace": "ID issued place", "phone": "Phone number", "address": "Address", "taxCode": "Tax code (optional)", "placeholders": {"fullName": "Enter full name", "dateOfBirth": "Select date of birth", "idNumber": "Enter ID number", "idIssuedDate": "Select issued date", "idIssuedPlace": "Enter issued place", "phone": "Enter phone number", "address": "Enter address", "taxCode": "Enter tax code"}}, "contractDisplay": {"title": "Contract content", "loading": "Loading contract...", "error": "Cannot load contract", "download": "Download contract"}, "signing": {"title": "Sign contract", "upload": "Upload signed contract", "selectFile": "Select file", "supportedFormats": "Supported: PDF, JPG, PNG", "maxSize": "Max size: 10MB"}, "handSignature": {"title": "Hand signature", "instruction": "Please sign your name in the box below", "clear": "Clear", "save": "Save signature"}, "otp": {"title": "OTP verification", "instruction": "Enter the 6-digit OTP code sent to your phone number", "placeholder": "Enter OTP code", "resend": "Resend code", "verify": "Verify"}, "actions": {"next": "Next", "previous": "Previous", "complete": "Complete", "submit": "Submit"}, "validation": {"required": "This field is required", "email": "Invalid email", "phone": "Invalid phone number", "taxCode": "Invalid tax code", "idNumber": "Invalid ID number", "otpLength": "OTP must be 6 digits"}, "success": {"title": "Contract signed successfully!", "message": "Your contract has been signed successfully. We will contact you as soon as possible.", "contractId": "Contract ID"}}}