{"contract": {"principle": {"title": "<PERSON><PERSON><PERSON> đồng nguyên tắc", "description": "<PERSON><PERSON> kết hợp đồng nguyên tắc với RedAI", "steps": {"typeSelection": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng", "termsAcceptance": "<PERSON><PERSON><PERSON> nhận đi<PERSON><PERSON>n", "infoForm": "<PERSON><PERSON><PERSON><PERSON> tin ký hợp đồng", "contractDisplay": "<PERSON><PERSON><PERSON> thị hợp đồng", "handSignature": "Chữ ký tay", "otpVerification": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON> th<PERSON>"}}, "types": {"business": "<PERSON><PERSON><PERSON>", "personal": "Cá nhân", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng", "selectTypeDescription": "<PERSON><PERSON> lòng chọn loại hợp đồng phù hợp với bạn"}, "terms": {"title": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n và điều kiện", "accept": "T<PERSON>i đồng ý với các điều khoản và điều kiện", "mustAccept": "Bạn phải chấp nhận điều khoản để tiếp tục", "content": {"header": "CHÍNH SÁCH & ĐIỀU KHOẢN SỬ DỤNG DỊCH VỤ", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần cu<PERSON>: 18/12/2024", "introduction": {"welcome": "Chào mừng bạn đến với RedAI, đ<PERSON><PERSON><PERSON> cung cấp bởi Công ty Cổ phần Đầu tư và Thương mại REDON (sau đây gọi tắt là \"REDON\", \"chúng tôi\" hoặc \"công ty\"). Bằng việc đăng ký, truy cập và sử dụng dịch vụ tại website https://redai.vn, đồng nghĩa rằng bạn đã đọc, hiểu và đồng ý tuân thủ toàn bộ nội dung của Chính sách & Điều khoản sử dụng dịch vụ này.", "purpose": "<PERSON><PERSON><PERSON> sách & <PERSON><PERSON><PERSON><PERSON> khoản sử dụng này đượ<PERSON> thiết lập nhằm quy định các quyền, ngh<PERSON><PERSON> vụ, trách nhiệm và mối quan hệ giữa REDON và tất cả các cá nhân, tổ chức/doanh nghiệp sử dụng dịch vụ do RedAI cung cấp (sau đây gọ<PERSON> là \"người dùng\"). Ch<PERSON>h sách áp dụng cho mọi hoạt động truy cập, sử dụng hoặc hợp tác liên quan đến dịch vụ và hệ sinh thái của RedAI, bao gồm nhưng không giới hạn ở khách hàng, đối tác và các bên liên quan khác.", "changes": "<PERSON>úng tôi bảo lưu quyền thay đổi, cập nhật hoặc chỉnh sửa bất kỳ nội dung nào trong Chính sách & Điều khoản sử dụng này vào bất kỳ thời điểm nào. Những thay đổi sẽ được đăng tải công khai trên website của RedAI và có hiệu lực ngay khi được cập nhật. Việc bạn tiếp tục sử dụng dịch vụ sau khi có thay đổi đồng nghĩa rằng bạn đã chấp nhận các điều khoản cập nhật. Nếu bạn không đồng ý với bất kỳ nội dung nào trong chính sách này, vui lòng ngừng truy cập và sử dụng dịch vụ của RedAI."}, "article1": {"title": "ĐIỀU 1: GIẢI THÍCH TỪ NGỮ", "definitions": {"redon": "\"<PERSON>úng tôi\", \"REDON\": <PERSON><PERSON> ty Cổ phần Đ<PERSON><PERSON> tư và Thương mại REDON, c<PERSON><PERSON> chi <PERSON>, tổ chức trự<PERSON> thuộc và các n<PERSON> viên, đại diện của công ty.", "customer": "\"Bạn\", \"Khá<PERSON> hàng\": <PERSON><PERSON> bất kỳ cá nhân hoặc tổ chức/doanh nghiệp truy cập website, đăng ký tài k<PERSON>, truy cập và sử dụng các dịch vụ trên phần mềm RedAI.", "partner": "\"Đối tác\": <PERSON><PERSON>, tổ chức hợp tác với REDON để cung cấp, phân phối hoặc tích hợp dịch vụ liên quan đến phần mềm RedAI.", "redai": "\"RedAI\", \"Phần mềm RedAI\": <PERSON><PERSON> hệ thống phần mềm cung cấp dịch vụ trợ lý ảo đa nhiệm cho cá nhân và tổ chức/do<PERSON>h nghiệ<PERSON>, thu<PERSON><PERSON> sở hữu và vận hành bởi Công ty Cổ phần Đầu tư và Thương mại REDON. Bao gồm website chính thức tại https://redai.vn, các sản phẩm, tính năng và dịch vụ liên quan.", "service": "\"Dịch vụ\": <PERSON><PERSON><PERSON><PERSON>àn bộ các t<PERSON>ng, cô<PERSON> cụ, API và các dịch vụ khác do RedAI cung cấp thông qua nền tảng web hoặc các hình thức khác.", "account": "\"Tài khoản\": <PERSON><PERSON> tài khoản do <PERSON>hách hàng đăng ký trên Phần mềm RedAI, cho phép truy cập và sử dụng các dịch vụ theo quy định trong Ch<PERSON> sách & <PERSON><PERSON><PERSON><PERSON> khoản sử dụng.", "api": "\"API (Application Programming Interface)\": <PERSON><PERSON> giao diện lập trình <PERSON>ng dụng được RedAI cung cấp để kết nối và tích hợp với hệ thống của bên thứ ba.", "thirdParty": "\"Bên thứ ba\": <PERSON><PERSON> các cá nhân hoặc tổ chức/doanh nghiệp không trực thuộc REDON, nh<PERSON><PERSON> c<PERSON> hợ<PERSON> tác, cung cấp dịch vụ hoặc tích hợp vào <PERSON> mềm RedAI.", "userData": "\"Dữ liệu người dùng\": <PERSON><PERSON>ồ<PERSON> tất cả thông tin cá nhân, lịch sử giao dịch, hành vi sử dụng, và các dữ liệu đư<PERSON><PERSON> phát sinh hoặc thu thập trong quá trình người dùng truy cập và sử dụng dịch vụ RedAI như: thông tin đơn hàng, URL, <PERSON><PERSON><PERSON>, hoặc các chuyển đổi.", "personalInfo": "\"Thông tin cá nhân\": <PERSON><PERSON> thông tin có thể xác định danh tính của cá nhân, bao gồm nhưng không giới hạn ở: h<PERSON> tên, ng<PERSON><PERSON>, số điện thoại, đ<PERSON><PERSON> chỉ email, tà<PERSON> kho<PERSON>n ngân hàng, lịch sử giao dịch và các thông tin khác liên quan đến việc sử dụng dịch vụ RedAI.", "confidentialInfo": "\"Thông tin mật\": <PERSON><PERSON> tất cả các thông tin liên quan đến hoạt động kinh do<PERSON>h, t<PERSON><PERSON>, <PERSON><PERSON> thu<PERSON>, dữ liệu cá nhân của khách hàng và các thông tin khác mà REDON và Khách hàng cam kết giữ bí mật, không tiết lộ cho bên thứ ba trừ khi có sự đồng ý hoặc yêu cầu pháp lý.", "rpoint": "\"R-Point\": <PERSON><PERSON> một công cụ giao dịch nội bộ trên Phần mềm RedAI, đ<PERSON><PERSON><PERSON> dùng để sử dụng hoặc kích hoạt các dịch vụ, t<PERSON>h năng trên Phần mềm RedAI. R-Point chỉ có giá trị sử dụng trong phạm vi hệ thống RedAI, không thể quy đổi thành tiền mặt, không chuyển nhượng và không hoàn lại sau khi đã giao dịch.", "intellectualProperty": "\"Quyền sở hữu trí tuệ\": <PERSON><PERSON> gồm toàn bộ các quyền liên quan đến phần mềm, công nghệ, d<PERSON> liệu, nội dung và các tài sản trí tuệ khác thuộc sở hữu của REDON, và các quyền được bảo hộ theo pháp luật sở hữu trí tuệ Việt Nam.", "violation": "\"Hành vi vi phạm\": <PERSON><PERSON> bất kỳ hành động nào của người dùng hoặc bên liên quan vi phạm các quy định trong Chính sách & <PERSON><PERSON><PERSON><PERSON> khoản sử dụng dịch vụ, bao gồm nhưng không giới hạn ở việc truy cập tr<PERSON>i phép, khai thác sai mục đích, gian lận hoặc sử dụng dịch vụ để thực hiện các hoạt động bất hợp pháp.", "technicalIncident": "\"Sự cố kỹ thuật\": <PERSON><PERSON> bất kỳ lỗi, hỏng hóc hoặc tình trạng ngừng hoạt động nào của phần mềm RedAI mà không phải do lỗi từ phía người dùng hoặc các hành vi không tuân thủ Ch<PERSON>h sách & Đ<PERSON><PERSON>u khoản sử dụng."}}, "article2": {"title": "ĐIỀU 2: CÁC HÀNH VI CẤM THỰC HIỆN KHI TRUY CẬP WEBSITE VÀ/HOẶC SỬ DỤNG DỊCH VỤ", "intro": "<PERSON><PERSON> đảm bảo môi trường dịch vụ an toàn và tuân thủ quy định pháp luật, các hành vi sau đây bị nghiêm cấm khi truy cập và/hoặc sử dụng dịch vụ của RedAI:", "sections": {"illegal": {"title": "2.1. <PERSON><PERSON><PERSON> vi trái pháp luật và vi phạm đạo đức:", "items": ["Sử dụng RedAI để thực hiện bất kỳ hành động nào vi phạm pháp luật Việt Nam, bao gồm nhưng không giới hạn ở: x<PERSON><PERSON><PERSON> tạc, b<PERSON><PERSON> nh<PERSON>, ph<PERSON><PERSON> ngôn tiêu cực về các tổ chức, c<PERSON> nhân, c<PERSON> quan nhà nước, đặc biệt là Đảng và Nhà nước Việt Nam.", "<PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON> tải hoặc lưu trữ các nội dung trái với thuần phong mỹ tục, k<PERSON><PERSON> động b<PERSON><PERSON> l<PERSON>, ph<PERSON> bi<PERSON><PERSON> chủng tộc, k<PERSON> thị, x<PERSON><PERSON> phạm danh dự cá nhân hoặc tổ chức/doanh nghiệp.", "Lợi dụng dịch vụ để hỗ trợ hoặc thực hiện các hành vi gian lận, lừa đảo hoặc các hoạt động phi pháp kh<PERSON>c."]}, "security": {"title": "2.2. <PERSON><PERSON> phạm dịch vụ và bảo mật:", "items": ["<PERSON>ạm dụng các tính năng AI hoặc phần mềm RedAI để tạo ra, chia sẻ hoặc truyền tải nội dung không phù hợp, <PERSON><PERSON><PERSON><PERSON> cảm, sa<PERSON> l<PERSON><PERSON>, g<PERSON> l<PERSON>, vi phạ<PERSON> bản quyền, sở hữu trí tuệ của bên thứ ba mà không có sự cho phép hoặc gây hại đến lợi ích hợp pháp của các bên thứ ba.", "Tạo hoặc chia sẻ nội dung sai lệch, g<PERSON><PERSON> hoang mang dư luận hoặc truyền tải thông tin giả mạo về RedAI, REDON hoặc các đối tác của công ty.", "<PERSON><PERSON><PERSON> dụng các tính năng <PERSON> toán, chuyển đổi R-Point, chư<PERSON><PERSON> trình Affiliate hoặc các tính năng liên quan để tạo giao dịch ảo, thao túng dữ liệu, g<PERSON> lận hoặc trục lợi bất ch<PERSON>, là<PERSON> sai lệch dữ liệu hoạt động của Phần mềm.", "<PERSON><PERSON> thậ<PERSON>, l<PERSON><PERSON> trữ hoặc tiết lộ trái phép thông tin cá nhân, dữ liệu khách hàng hoặc dữ liệu kinh doanh của REDON mà không có sự đồng ý rõ ràng.", "Spam tin nhắn hoặc gây phiền toái cho người khác thông qua các dịch vụ của RedAI.", "<PERSON><PERSON><PERSON> công, t<PERSON><PERSON> cậ<PERSON> tr<PERSON><PERSON>, khai thác lỗ hổng hoặc thực hiện các hành vi gây ảnh hưởng đến tính bảo mật và an toàn của hệ thống."]}, "promotion": {"title": "2.3. <PERSON><PERSON> <PERSON>h<PERSON>m ch<PERSON>h sách quảng bá và liên kết:", "items": ["Sử dụng từ khóa liên quan đến thương hiệu RedAI trong các chiến dịch quảng bá, quảng cáo trên công cụ tìm kiếm (SEM) hoặc các nội dung quảng cáo mà không được sự cho phép bằng văn bản của REDON.", "<PERSON><PERSON><PERSON> đơn hàng, <PERSON>h<PERSON><PERSON> hàng hoặc spam liên kết giới thiệu không hợp lệ trong chương trình Affiliate nhằm trục lợi hoặc gây thiệt hại đến REDON hoặc bên thứ ba."]}, "interference": {"title": "2.4. <PERSON> thi<PERSON><PERSON> v<PERSON>o hoạt động của phần mềm:", "items": ["<PERSON><PERSON> dụng bất kỳ ph<PERSON><PERSON><PERSON> ph<PERSON>p, cô<PERSON> cụ hoặc thiết bị nào để can thi<PERSON><PERSON>, làm g<PERSON><PERSON>, ph<PERSON> hoại hoặc làm suy giảm hiệu suất của website, hệ thống hoặc dịch vụ của RedAI.", "Phát tán virus, mã độc hoặc sử dụng công nghệ để gây hại cho phần mềm hoặc xâm nhập trái phép vào các phần của RedAI hoặc hệ thống bên thứ ba tích hợp với RedAI."]}, "enforcement": "2.5. REDON có quyền ngay lập tức khóa tà<PERSON>, tạm ngừng hoặc vô hiệu hóa dịch vụ đối với người dùng vi phạm mà không cần thông báo trước. Chúng tôi sẽ áp dụng các biện pháp kỹ thuật cần thiết để bảo vệ hệ thống và ngăn chặn thiệt hại do hành vi vi phạm gây ra. <PERSON><PERSON><PERSON> thời, REDON có quyền khởi kiện hoặc yêu cầu bồi thường từ phía người dùng vi phạm.", "liability": "2.6. <PERSON><PERSON><PERSON> không chịu trách nhiệm ph<PERSON><PERSON> lý, tà<PERSON> ch<PERSON>h hoặc bất kỳ tổn thất nào phát sinh từ hành vi vi phạm của người dùng. <PERSON><PERSON><PERSON> qu<PERSON>, bao gồm thiệt hại về tài chính, ph<PERSON><PERSON> lý hoặc uy tín, ph<PERSON>t sinh từ hành vi vi phạm sẽ thuộc trách nhiệm hoàn toàn của người dùng vi phạm."}}, "article3": {"title": "ĐIỀU 3: QUYỀN VÀ NGHĨA VỤ CỦA KHÁCH HÀNG", "rightsTitle": "<PERSON><PERSON><PERSON><PERSON> của kh<PERSON>ch hàng:", "obligationsTitle": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> của kh<PERSON>ch hàng:", "rights": ["3.1. <PERSON><PERSON><PERSON><PERSON> hàng đư<PERSON><PERSON> truy cập và sử dụng Phần mềm ổn định và liên tục trong phạm vi điều kiện kỹ thuật và vận hành thực tế của RedAI, trừ các trư<PERSON><PERSON> hợp thuộc sự kiện Bất <PERSON> hoặc trách nhiệm bảo trì hệ thống của REDON.", "3.2. <PERSON><PERSON><PERSON><PERSON> hàng được truy cập và sử dụng các tài liệu hỗ trợ khách hàng, bao gồm nhưng không giới hạn ở: tài liệu hướng dẫn sử dụng, cổ<PERSON> thông tin hỗ trợ khách hàng, trang hướng dẫn trực tuyến tại địa chỉ https://help.redai.vn/.", "3.3. <PERSON><PERSON><PERSON><PERSON> tin cá nhân và dữ liệu giao dịch của khách hàng sẽ được bảo vệ theo <PERSON> sách bảo vệ dữ liệu cá nhân của REDON và quy định pháp luật hiện hành.", "3.4. <PERSON><PERSON> thờ<PERSON> gian sử dụng, kh<PERSON>ch hàng được bảo hành và hỗ trợ về các vấn đề phát sinh từ lỗi kỹ thuật của Phần mềm. Nếu xảy ra lỗi hoặc sự cố, REDON sẽ thông báo cho khách hàng và triển khai các biện pháp khắc phục để đảm bảo Phần mềm hoạt động ổn định và liên tục.", "3.5. <PERSON><PERSON><PERSON><PERSON> hàng có quyền khiếu nại và yêu cầu giải quyết các vấn đề liên quan đến dịch vụ RedAI. Khiếu nại sẽ được xử lý theo quy định của REDON.", "3.6. <PERSON><PERSON><PERSON><PERSON> hàng có quyền chấm dứt sử dụng dịch vụ theo quy định, với điều kiện tuân thủ đầy đủ nghĩa vụ thanh toán và không vi phạm các điều khoản liên quan."], "obligations": ["3.7. <PERSON><PERSON><PERSON><PERSON> hàng có nghĩa vụ thanh toán đầy đủ và đúng hạn các khoản phí liên quan đến dịch vụ RedAI, bao gồm nhưng không giới hạn ở phí sử dụng, phí API hoặc các khoản phí khác theo thỏa thuận hoặc quy định của REDON.", "3.8. <PERSON><PERSON><PERSON><PERSON> hàng chịu trách nhiệm b<PERSON><PERSON> mật thông tin tài khoản, bao gồm: tên đăng nh<PERSON>, mật khẩu và các thông tin liên quan. <PERSON><PERSON><PERSON> hành động được thực hiện thông qua tài khoản sẽ được xem là do khách hàng thực hiện và chịu trách nhiệm.", "3.9. <PERSON><PERSON><PERSON><PERSON> hàng phải cung cấp thông tin chính x<PERSON>c, đầy đủ và kịp thời khi đăng ký tài khoản hoặc sử dụng dịch vụ. Kh<PERSON>ch hàng chịu trách nhiệm cập nhật thông tin liên lạc nếu có thay đổi. REDON không chịu trách nhiệm với thiệt hại do việc cung cấp thông tin sai lệch.", "3.10. <PERSON><PERSON><PERSON><PERSON> hàng không được sử dụng dịch vụ RedAI vào các mục đích bất hợp pháp hoặc các hành vi gây hại đến hệ thống, bao gồm nhưng không giới hạn ở spam, ph<PERSON><PERSON> tán thông tin sai lệch hoặc sử dụng AI để thực hiện hành vi trái pháp luật.", "3.11. <PERSON><PERSON><PERSON><PERSON> hàng chịu trách nhiệm hoàn toàn đối với tất cả các nội dung được tạo ra hoặc/và gửi đi bởi AI hoặc chính khách hàng trên Phần mềm. <PERSON><PERSON><PERSON> bảo rằng các nội dung này đã được sự đồng ý của các bên liên quan, không vi phạm pháp luật, quyền sở hữu trí tuệ của bên thứ ba và tuân thủ Chính sách & Điều khoản sử dụng dịch vụ này.", "3.12. <PERSON><PERSON><PERSON><PERSON> hàng không được sử dụng tài khoản hoặc Phần mềm RedAI để kinh doanh lại hoặc chuyển giao dưới bất kỳ hình thức nào, cũ<PERSON> như không được chuyển nhượng hoặc chuyển giao toàn bộ hay một phần quyền và nghĩa vụ của mình cho bất kỳ bên thứ ba nào mà không có sự đồng ý bằng văn bản từ REDON.", "3.13. <PERSON><PERSON><PERSON> ý rằng trong quá trình vận hành, <PERSON><PERSON><PERSON> mềm RedAI có thể gặp sự cố do các nguyên nhân khách quan như lỗi hệ thống, b<PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON> kỳ, sự cố mạng diện rộng hoặc tấn công mạng (hacker, tin tặc…). Trong các trường hợ<PERSON> n<PERSON>, kh<PERSON>ch hàng cam kết không khiếu nại, khởi kiện hoặc theo đuổi kiện tụng chống lại REDON.", "3.14. <PERSON> kết không quảng bá, gi<PERSON>i thiệu hoặc sử dụng hình ảnh của RedAI theo cách thức gây ảnh hưởng tiêu cực đến uy tín và lợi ích của REDON."]}, "otherTerms": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> k<PERSON> kh<PERSON>c", "description": "<PERSON><PERSON><PERSON><PERSON> các điều khoản chính đã nêu trên, còn có các điều khoản khác về quyền và trách nhiệm của REDON, quy định <PERSON> toán, ch<PERSON><PERSON> s<PERSON>ch hoà<PERSON> tiề<PERSON>, mi<PERSON><PERSON> trừ trách nhiệm và hiệu lực thỏa thuận. Vui lòng tham khảo toàn bộ nội dung tại website chính thức: https://redai.vn/chinh-sach-va-dieu-khoan-su-dung"}, "contactInfo": {"title": "THÔNG TIN LIÊN HỆ", "companyName": "CÔNG TY CP ĐẦU TƯ VÀ THƯƠNG MẠI REDON", "address": "Địa chỉ: <PERSON><PERSON> 40/74 đ<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Mỗ, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Việt Nam", "businessLicense": "<PERSON><PERSON><PERSON><PERSON> phép kinh doanh: ********** cấp ngày 29/04/2020 bởi Sở K<PERSON> và Đầu Tư Tp. <PERSON><PERSON>ộ<PERSON>", "hotline": "Hotline: 0965.262.824", "email": "Email: <EMAIL>", "website": "Website: https://redai.vn"}}}, "businessInfo": {"title": "<PERSON>h<PERSON>ng tin doanh nghiệp", "companyName": "<PERSON><PERSON>n công ty", "taxCode": "<PERSON><PERSON> số thuế", "companyEmail": "<PERSON>ail công ty", "companyAddress": "Địa chỉ công ty", "companyPhone": "<PERSON><PERSON> điện tho<PERSON>i công ty", "representative": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "placeholders": {"companyName": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "taxCode": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "companyEmail": "Nhập email công ty", "companyAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ công ty", "companyPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại công ty", "representative": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i đại <PERSON>n", "position": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>"}}, "personalInfo": {"title": "Thông tin cá nhân", "fullName": "Họ và tên", "dateOfBirth": "<PERSON><PERSON><PERSON>", "idNumber": "Số CCCD/CMND", "idIssuedDate": "<PERSON><PERSON><PERSON> c<PERSON>", "idIssuedPlace": "<PERSON><PERSON><PERSON> c<PERSON>p", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "taxCode": "<PERSON><PERSON> số thuế (nếu có)", "placeholders": {"fullName": "<PERSON><PERSON><PERSON><PERSON> họ và tên", "dateOfBirth": "<PERSON><PERSON><PERSON> ng<PERSON> sinh", "idNumber": "Nhập số CCCD/CMND", "idIssuedDate": "<PERSON><PERSON><PERSON> ng<PERSON> c<PERSON>p", "idIssuedPlace": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> cấp", "phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "taxCode": "<PERSON><PERSON><PERSON><PERSON> mã số thuế"}}, "contractDisplay": {"title": "<PERSON><PERSON><PERSON> dung hợp đồng", "loading": "<PERSON><PERSON> tải hợp đồng...", "error": "<PERSON><PERSON><PERSON><PERSON> thể tải hợp đồng", "download": "<PERSON><PERSON><PERSON> x<PERSON> h<PERSON>p đồng"}, "signing": {"title": "<PERSON><PERSON> h<PERSON><PERSON> đồng", "upload": "<PERSON><PERSON><PERSON> lên hợp đồng đã ký", "selectFile": "<PERSON><PERSON><PERSON> file", "supportedFormats": "Hỗ trợ: PDF, JPG, PNG", "maxSize": "<PERSON><PERSON><PERSON> th<PERSON> tối đa: 10MB"}, "handSignature": {"title": "Chữ ký tay", "instruction": "<PERSON><PERSON> lòng ký tên trong khung bên dư<PERSON>i", "clear": "Xóa", "save": "<PERSON><PERSON><PERSON> ch<PERSON> ký"}, "otp": {"title": "<PERSON><PERSON><PERSON>", "instruction": "<PERSON><PERSON><PERSON><PERSON> mã OTP 6 số đư<PERSON>c gửi đến số điện thoại của bạn", "placeholder": "<PERSON>hậ<PERSON> mã OTP", "resend": "<PERSON><PERSON><PERSON> lại mã", "verify": "<PERSON><PERSON><PERSON> th<PERSON>c"}, "actions": {"next": "<PERSON><PERSON><PERSON><PERSON>", "previous": "Quay lại", "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "submit": "<PERSON><PERSON><PERSON>"}, "validation": {"required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "email": "<PERSON><PERSON> h<PERSON> l<PERSON>", "phone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "taxCode": "<PERSON><PERSON> số thuế không hợp lệ", "idNumber": "Số CCCD/CMND không hợp lệ", "otpLength": "Mã OTP phải có 6 số"}, "success": {"title": "<PERSON><PERSON> hợp đồng thành công!", "message": "<PERSON><PERSON><PERSON> đồng của bạn đã được ký thành công. <PERSON>úng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.", "contractId": "<PERSON><PERSON> hợp đồng"}}}