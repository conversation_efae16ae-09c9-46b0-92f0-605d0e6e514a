{"contract": {"principle": {"title": "原则合同", "description": "与RedAI签署原则合同", "steps": {"typeSelection": "选择合同类型", "termsAcceptance": "接受条款", "infoForm": "合同信息", "contractDisplay": "合同显示", "handSignature": "手写签名", "otpVerification": "OTP验证", "completed": "完成"}}, "types": {"business": "企业", "personal": "个人", "selectType": "选择合同类型", "selectTypeDescription": "请选择适合您的合同类型"}, "terms": {"title": "条款和条件", "accept": "我同意条款和条件", "mustAccept": "您必须接受条款才能继续", "content": {"header": "服务条款和隐私政策", "lastUpdated": "最后更新：2024年12月18日", "introduction": {"welcome": "欢迎使用RedAI，由REDON投资贸易股份公司提供（以下简称\"REDON\"、\"我们\"或\"公司\"）。通过在网站https://redai.vn注册、访问和使用服务，意味着您已阅读、理解并同意遵守本服务条款和隐私政策的全部内容。", "purpose": "本服务条款和隐私政策旨在规定REDON与所有使用RedAI提供服务的个人、组织/企业（以下称为\"用户\"）之间的权利、义务、责任和关系。该政策适用于与RedAI服务和生态系统相关的所有访问、使用或合作活动，包括但不限于客户、合作伙伴和其他相关方。", "changes": "我们保留随时更改、更新或修改本服务条款和隐私政策任何内容的权利。更改将在RedAI网站上公开发布，并在更新后立即生效。您在更改后继续使用服务意味着您已接受更新的条款。如果您不同意本政策中的任何内容，请停止访问和使用RedAI服务。"}, "article1": {"title": "第1条：术语解释", "definitions": {"redon": "\"我们\"、\"REDON\"：指REDON投资贸易股份公司、其分支机构、附属组织及公司员工、代表。", "customer": "\"您\"、\"客户\"：指任何访问网站、注册账户、访问和使用RedAI软件服务的个人或组织/企业。", "partner": "\"合作伙伴\"：指与REDON合作提供、分销或集成RedAI软件相关服务的个人、组织。", "redai": "\"RedAI\"、\"RedAI软件\"：指为个人和组织/企业提供多任务虚拟助手服务的软件系统，由REDON投资贸易股份公司拥有和运营。包括官方网站https://redai.vn、相关产品、功能和服务。", "service": "\"服务\"：包括RedAI通过网络平台或其他形式提供的所有功能、工具、API和其他服务。", "account": "\"账户\"：指客户在RedAI软件上注册的账户，允许根据本服务条款和隐私政策的规定访问和使用服务。", "api": "\"API（应用程序编程接口）\"：指RedAI提供的用于连接和集成第三方系统的应用程序编程接口。", "thirdParty": "\"第三方\"：指不直接隶属于REDON，但与RedAI软件有合作、提供服务或集成的个人或组织/企业。", "userData": "\"用户数据\"：包括用户访问和使用RedAI服务过程中产生或收集的所有个人信息、交易历史、使用行为和数据，如：订单信息、URL、图像或转换。", "personalInfo": "\"个人信息\"：指可以识别个人身份的信息，包括但不限于：姓名、出生日期、电话号码、电子邮件地址、银行账户、交易历史和与使用RedAI服务相关的其他信息。", "confidentialInfo": "\"机密信息\"：指与业务、财务、技术活动、客户个人数据相关的所有信息以及REDON和客户承诺保密、不向第三方披露的其他信息，除非有同意或法律要求。", "rpoint": "\"R-Point\"：指RedAI软件上的内部交易工具，用于使用或激活RedAI软件上的服务和功能。R-Point仅在RedAI系统范围内有效，不能兑换现金，不可转让，交易后不可退还。", "intellectualProperty": "\"知识产权\"：包括REDON拥有的软件、技术、数据、内容和其他知识资产的所有相关权利，以及受越南知识产权法保护的权利。", "violation": "\"违规行为\"：指用户或相关方违反本服务条款和隐私政策规定的任何行为，包括但不限于未经授权访问、滥用、欺诈或使用服务进行非法活动。", "technicalIncident": "\"技术事故\"：指RedAI软件的任何错误、故障或停机，不是由于用户错误或不遵守服务条款和隐私政策造成的。"}}, "article2": {"title": "第2条：访问网站和/或使用服务时的禁止行为", "intro": "为确保安全的服务环境并遵守法律法规，在访问和/或使用RedAI服务时严格禁止以下行为：", "sections": {"illegal": {"title": "2.1. 违法行为和道德违规：", "items": ["使用RedAI执行任何违反越南法律的行为，包括但不限于：歪曲、诽谤、对组织、个人、国家机关发表负面言论，特别是对越南党和国家。", "传播、传输或存储违背良好风俗习惯的内容，煽动暴力、种族歧视、偏见，侮辱个人或组织/企业的荣誉。", "利用服务支持或实施欺诈、欺骗行为或其他非法活动。"]}, "security": {"title": "2.2. 服务和安全违规：", "items": ["滥用AI功能或RedAI软件创建、分享或传输不当、冒犯、误导、欺诈内容，未经许可违反第三方版权、知识产权或损害第三方合法利益。", "创建或分享误导性内容，造成公众困惑或传输关于RedAI、REDON或公司合作伙伴的虚假信息。", "滥用支付功能、R-Point转换、联盟计划或相关功能创建虚假交易、操纵数据、欺诈或非法牟利，扭曲软件活动数据。", "未经明确同意收集、存储或非法披露个人信息、客户数据或REDON业务数据。", "通过RedAI服务发送垃圾邮件或对他人造成困扰。", "攻击、未经授权访问、利用漏洞或执行影响系统安全和安全性的行为。"]}, "promotion": {"title": "2.3. 违反推广和链接政策：", "items": ["未经REDON书面许可，在推广活动、搜索引擎广告（SEM）或广告内容中使用与RedAI品牌相关的关键词。", "在联盟计划中创建无效订单、客户或垃圾推荐链接以牟利或对REDON或第三方造成损害。"]}, "interference": {"title": "2.4. 干扰软件运行：", "items": ["使用任何方法、工具或设备干扰、中断、破坏或降低RedAI网站、系统或服务的性能。", "传播病毒、恶意软件或使用技术损害软件或非法渗透RedAI部分或与RedAI集成的第三方系统。"]}, "enforcement": "2.5. REDON有权立即锁定账户、暂停或禁用违规用户的服务，无需事先通知。我们将采取必要的技术措施保护系统并防止违规行为造成的损害。同时，REDON有权起诉或要求违规用户赔偿。", "liability": "2.6. REDON不对用户违规行为产生的法律、财务或任何损失负责。违规行为产生的所有后果，包括财务、法律或声誉损害，应由违规用户承担全部责任。"}}, "article3": {"title": "第3条：客户的权利和义务", "rightsTitle": "客户权利：", "obligationsTitle": "客户义务：", "rights": ["3.1. 客户有权在RedAI实际技术和运营条件范围内稳定持续地访问和使用软件，除不可抗力事件或REDON系统维护责任的情况外。", "3.2. 客户有权访问和使用客户支持材料，包括但不限于：用户指南、客户支持门户、在线帮助页面https://help.redai.vn/。", "3.3. 客户个人信息和交易数据将根据REDON个人数据保护政策和现行法律法规受到保护。", "3.4. 在使用期间，客户有权获得软件技术错误问题的保修和支持。如果发生错误或事故，REDON将通知客户并实施补救措施以确保软件稳定持续运行。", "3.5. 客户有权投诉并要求解决与RedAI服务相关的问题。投诉将根据REDON的规定处理。", "3.6. 客户有权根据规定终止服务使用，条件是完全遵守付款义务且不违反相关条款。"], "obligations": ["3.7. 客户有义务全额按时支付与RedAI服务相关的所有费用，包括但不限于使用费、API费或根据REDON协议或规定的其他费用。", "3.8. 客户负责保护账户信息，包括：用户名、密码和相关信息。通过账户执行的所有操作将被视为由客户执行并承担责任。", "3.9. 客户在注册账户或使用服务时必须提供准确、完整和及时的信息。如有变更，客户负责更新联系信息。REDON不对因提供误导信息而造成的损害负责。", "3.10. 客户不得将RedAI服务用于非法目的或损害系统的行为，包括但不限于垃圾邮件、传播误导信息或使用AI执行非法行为。", "3.11. 客户对AI或客户自己在软件上创建和/或发送的所有内容承担全部责任。确保此内容已获得相关方同意，不违反法律、第三方知识产权并遵守本服务条款和隐私政策。", "3.12. 客户不得以任何形式使用账户或RedAI软件进行转售或转让，也不得在未经REDON书面同意的情况下将其全部或部分权利和义务转让或转让给任何第三方。", "3.13. 同意在运营过程中，RedAI软件可能由于客观原因遇到事故，如系统错误、定期维护、广泛网络事故或网络攻击（黑客、破解者...）。在这些情况下，客户承诺不对REDON提出投诉、起诉或追究诉讼。", "3.14. 承诺不以对REDON声誉和利益产生负面影响的方式推广、介绍或使用RedAI图像。"]}, "otherTerms": {"title": "其他条款", "description": "除上述主要条款外，还有关于REDON权利和责任、付款规定、退款政策、责任免除和协议有效性的其他条款。请参考官方网站的完整内容：https://redai.vn/chinh-sach-va-dieu-khoan-su-dung"}, "contactInfo": {"title": "联系信息", "companyName": "REDON投资贸易股份公司", "address": "地址：越南河内市南慈廉郡大墨坊友兴街40/74号", "businessLicense": "营业执照：**********，2020年4月29日由河内市计划投资厅颁发", "hotline": "热线：0965.262.824", "email": "邮箱：<EMAIL>", "website": "网站：https://redai.vn"}}}, "businessInfo": {"title": "企业信息", "companyName": "公司名称", "taxCode": "税号", "companyEmail": "公司邮箱", "companyAddress": "公司地址", "companyPhone": "公司电话", "representative": "代表人", "position": "职位", "placeholders": {"companyName": "输入公司名称", "taxCode": "输入税号", "companyEmail": "输入公司邮箱", "companyAddress": "输入公司地址", "companyPhone": "输入公司电话", "representative": "输入代表人姓名", "position": "输入职位"}}, "personalInfo": {"title": "个人信息", "fullName": "姓名", "dateOfBirth": "出生日期", "idNumber": "身份证号", "idIssuedDate": "发证日期", "idIssuedPlace": "发证地点", "phone": "电话号码", "address": "地址", "taxCode": "税号（可选）", "placeholders": {"fullName": "输入姓名", "dateOfBirth": "选择出生日期", "idNumber": "输入身份证号", "idIssuedDate": "选择发证日期", "idIssuedPlace": "输入发证地点", "phone": "输入电话号码", "address": "输入地址", "taxCode": "输入税号"}}, "contractDisplay": {"title": "合同内容", "loading": "正在加载合同...", "error": "无法加载合同", "download": "下载合同"}, "signing": {"title": "签署合同", "upload": "上传已签署的合同", "selectFile": "选择文件", "supportedFormats": "支持：PDF、JPG、PNG", "maxSize": "最大大小：10MB"}, "handSignature": {"title": "手写签名", "instruction": "请在下方框内签名", "clear": "清除", "save": "保存签名"}, "otp": {"title": "OTP验证", "instruction": "输入发送到您手机号码的6位OTP验证码", "placeholder": "输入OTP验证码", "resend": "重新发送验证码", "verify": "验证"}, "actions": {"next": "下一步", "previous": "上一步", "complete": "完成", "submit": "提交"}, "validation": {"required": "此字段为必填项", "email": "邮箱格式无效", "phone": "电话号码无效", "taxCode": "税号无效", "idNumber": "身份证号无效", "otpLength": "OTP必须为6位数字"}, "success": {"title": "合同签署成功！", "message": "您的合同已成功签署。我们将尽快与您联系。", "contractId": "合同编号"}}}