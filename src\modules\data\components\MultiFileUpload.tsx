import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Typography, Icon, FormItem, FileDisplay } from '@/shared/components/common';


export interface FileWithMetadata {
  file: File;
  id: string;
  preview?: string;
}

interface MultiFileUploadProps {
  /**
   * Callback khi chọn file
   */
  onChange: (files: FileWithMetadata[]) => void;

  /**
   * Danh sách file đã chọn
   */
  value?: FileWithMetadata[];

  /**
   * Loại file được chấp nhận
   */
  accept?: string;

  /**
   * Label hiển thị
   */
  label?: string;

  /**
   * Text hiển thị khi chưa có file
   */
  placeholder?: string;

  /**
   * Có hiển thị preview không
   */
  showPreview?: boolean;

  /**
   * Có yêu cầu không
   */
  required?: boolean;

  /**
   * Thông báo lỗi
   */
  error?: string;

  /**
   * <PERSON>ều cao của vùng kéo thả
   */
  height?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Chỉ chấp nhận ảnh và video
   */
  mediaOnly?: boolean;
}

/**
 * Component upload nhiều file với drag-and-drop
 */
const MultiFileUpload: React.FC<MultiFileUploadProps> = ({
  onChange,
  value = [],
  accept = '*',
  label,
  placeholder = 'Kéo thả hoặc click để tải lên file',
  showPreview = true,
  required = false,
  error,
  height = 'h-40',
  className = '',
  mediaOnly = false,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // Kiểm tra khả năng scroll
  const checkScrollability = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const scrollLeft = container.scrollLeft;
      const scrollWidth = container.scrollWidth;
      const clientWidth = container.clientWidth;

      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1); // -1 để tránh lỗi floating point
    }
  }, []);

  console.log(showPreview);

  // Xử lý scroll ngang
  const handleScrollClick = useCallback((direction: 'left' | 'right') => {
    if (containerRef.current) {
      const container = containerRef.current;
      const scrollAmount = 200; // Số pixel cuộn mỗi lần

      if (direction === 'left') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  }, []);

  // Kiểm tra scroll khi component mount và khi có thay đổi files
  useEffect(() => {
    // Delay để đảm bảo DOM đã render xong
    const timer = setTimeout(() => {
      checkScrollability();
    }, 200);
    return () => clearTimeout(timer);
  }, [value, checkScrollability]);

  // Thêm một useEffect khác để kiểm tra khi window resize
  useEffect(() => {
    const handleResize = () => {
      setTimeout(checkScrollability, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [checkScrollability]);

  // Thêm event listener cho scroll
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollability);
      // Kiểm tra ngay khi mount
      checkScrollability();
      return () => container.removeEventListener('scroll', checkScrollability);
    }
    // Return empty cleanup function nếu không có container
    return () => {};
  }, [checkScrollability]);





  // Xử lý khi kéo file vào vùng upload
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  // Xử lý khi thả file
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  // Xử lý các file đã chọn
  const processFiles = (fileList: FileList) => {
    const newFiles: FileWithMetadata[] = [];

    Array.from(fileList).forEach(file => {
      // Nếu mediaOnly = true, chỉ chấp nhận ảnh và video
      if (mediaOnly && !file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        return;
      }

      // Tạo ID duy nhất cho file
      const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Tạo preview cho file ảnh và video
      let preview: string | undefined = undefined;
      if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
        preview = URL.createObjectURL(file);
      }

      newFiles.push({
        file,
        id: fileId,
        preview,
      });
    });

    // Nếu không có file nào được thêm, không gọi callback
    if (newFiles.length === 0) return;

    // Gọi callback với danh sách file mới + file cũ
    onChange([...value, ...newFiles]);

    // Reset input để có thể chọn lại cùng một file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Xử lý xóa file
  const handleRemoveFile = (fileId: string) => {
    const updatedFiles = value.filter(f => f.id !== fileId);
    onChange(updatedFiles);

    // Giải phóng URL object nếu là preview ảnh
    const fileToRemove = value.find(f => f.id === fileId);
    if (fileToRemove?.preview) {
      URL.revokeObjectURL(fileToRemove.preview);
    }
  };

  // Không cần hàm getFileIcon vì đã sử dụng component FileDisplay

  return (
    <FormItem label={label} required={required} className="w-full overflow-visible">
      <div className={`w-full overflow-visible ${className}`}>
        {/* Vùng kéo thả */}
        <div
          className={`
            rounded-lg p-4 flex flex-col items-center justify-center relative cursor-pointer border border-dashed border-gray-300 dark:border-gray-700
            ${height}
            ${isDragging ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-400 dark:border-primary-600' : 'bg-gray-50 dark:bg-gray-800/30 hover:bg-gray-100 dark:hover:bg-gray-800/50'}
            ${error ? 'border-red-500' : ''}
          `}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <Icon name="upload" size="lg" className="mb-2 text-gray-400" />
          <Typography variant="body2" className="text-center text-gray-500 dark:text-gray-400">
            {placeholder}
          </Typography>
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />
        </div>

        {/* Danh sách file đã chọn */}
        {value.length > 0 && (
          <div className="mt-4 space-y-2">
            <Typography variant="subtitle2" className="font-medium">
              Đã chọn {value.length} file
            </Typography>

            {/* Hiển thị dạng cuộn ngang cho tất cả file - Break out of container */}
            <div
              className="relative"
              style={{
                width: '100vw',
                marginLeft: 'calc(-50vw + 50%)',
                paddingLeft: '1rem',
                paddingRight: '1rem'
              }}
            >
              {/* Scroll buttons */}
              {canScrollLeft && (
                <button
                  type="button"
                  onClick={() => handleScrollClick('left')}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-1.5 shadow-md hover:shadow-lg transition-all hover:scale-105 opacity-90 hover:opacity-100"
                  title="Cuộn trái"
                >
                  <Icon name="chevron-left" size="sm" className="text-gray-600 dark:text-gray-400" />
                </button>
              )}

              {canScrollRight && (
                <button
                  type="button"
                  onClick={() => handleScrollClick('right')}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-1.5 shadow-md hover:shadow-lg transition-all hover:scale-105 opacity-90 hover:opacity-100"
                  title="Cuộn phải"
                >
                  <Icon name="chevron-right" size="sm" className="text-gray-600 dark:text-gray-400" />
                </button>
              )}

              {/* Horizontal scroll container with custom scrollbar */}
              <div
                ref={containerRef}
                className="flex flex-nowrap gap-2 overflow-x-auto overflow-y-hidden px-12 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 custom-scrollbar mx-4"
                style={{
                  minHeight: '96px'
                }}
              >
                    {value.map(fileData => (
                      <div key={fileData.id} className="relative group flex-shrink-0">
                        {fileData.preview ? (
                          <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800 shadow-sm group-hover:shadow-md transition-shadow">
                            {fileData.file.type.startsWith('video/') ? (
                              <div className="w-full h-full flex items-center justify-center bg-black rounded-lg">
                                <video
                                  src={fileData.preview}
                                  className="w-full h-full object-cover"
                                  controls={false}
                                />
                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg">
                                  <Icon name="play" size="sm" className="text-white" />
                                </div>
                              </div>
                            ) : (
                              <img
                                src={fileData.preview}
                                alt={fileData.file.name}
                                className="w-full h-full object-cover"
                                loading="lazy"
                              />
                            )}

                            {/* File name overlay - chỉ hiển thị khi hover */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-1 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                              <Typography variant="caption" className="text-white truncate block text-xs">
                                {fileData.file.name}
                              </Typography>
                            </div>

                            {/* Remove button - chỉ hiển thị khi hover */}
                            <button
                              type="button"
                              onClick={e => {
                                e.stopPropagation();
                                handleRemoveFile(fileData.id);
                              }}
                              className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 rounded-full p-1 text-white transition-all shadow-sm opacity-0 group-hover:opacity-100 scale-90 group-hover:scale-100"
                              title="Xóa ảnh"
                            >
                              <Icon name="x" size="xs" />
                            </button>
                          </div>
                        ) : (
                          <div className="relative w-20 h-20">
                            <FileDisplay
                              fileName={fileData.file.name}
                              fileSize={fileData.file.size}
                              className="w-full h-full group-hover:bg-gray-50 dark:group-hover:bg-gray-800 rounded-lg shadow-sm"
                            />
                            <button
                              type="button"
                              onClick={e => {
                                e.stopPropagation();
                                handleRemoveFile(fileData.id);
                              }}
                              className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 rounded-full p-1 text-white transition-all shadow-sm opacity-0 group-hover:opacity-100 scale-90 group-hover:scale-100"
                              title="Xóa file"
                            >
                              <Icon name="x" size="xs" />
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>
          </div>
        )}
      </div>
    </FormItem>
  );
};

export default MultiFileUpload;
