import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Tooltip, IconCard, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import {
  useKnowledgeFiles,
  useDeleteKnowledgeFile,
  useDeleteMultipleKnowledgeFiles,
} from '@/modules/data/knowledge-files/hooks/useKnowledgeFileQuery';
import {
  FileResponseDto,
  QueryFileDto,
} from '@/modules/data/knowledge-files/types/knowledge-files.types';
import KnowledgeFileForm from '@/modules/data/components/forms/KnowledgeFileForm';

/**
 * Trang hiển thị danh sách file tri thức
 */
const KnowledgeFilesPage: React.FC = () => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<FileResponseDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [fileToDelete, setFileToDelete] = useState<FileResponseDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho chọn nhiều file
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<QueryFileDto>(() => {
    const params: QueryFileDto = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection,
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: knowledgeFilesData,
    isLoading: isLoadingFiles,
    error: filesError,
  } = useKnowledgeFiles(queryParams);

  // Hook để xóa file
  const { mutateAsync: deleteFile } = useDeleteKnowledgeFile();

  // Hook để xóa nhiều file cùng lúc
  const { mutateAsync: deleteMultipleFiles } = useDeleteMultipleKnowledgeFiles();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (knowledgeFilesData) {
      setFiles(knowledgeFilesData.items);
      setTotalItems(knowledgeFilesData.meta.totalItems);
    }

    setIsLoading(isLoadingFiles);
  }, [knowledgeFilesData, filesError, isLoadingFiles]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      // Gọi API xóa file
      await deleteFile(fileToDelete.id);

      // Đóng popup
      setShowDeleteConfirm(false);
      setFileToDelete(null);
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  }, [fileToDelete, deleteFile]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: 'Vui lòng chọn ít nhất một file để xóa',
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều file cùng lúc
      await deleteMultipleFiles(selectedRowKeys as string[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: `Đã xóa ${selectedRowKeys.length} file thành công`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting files:', error);
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi xóa file',
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleFiles]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý khi form upload thành công
  const handleUploadSuccess = useCallback(() => {
    // Đóng form
    hideForm();
  }, [hideForm]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('data:knowledgeFiles.table.name', 'Tên file'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'extension',
        title: t('data:knowledgeFiles.table.extension', 'Định dạng'),
        dataIndex: 'extension',
        width: '10%',
        sortable: true,
      },
      {
        key: 'storage',
        title: t('data:knowledgeFiles.table.size', 'Kích thước'),
        dataIndex: 'storage',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          // Chuyển đổi byte sang KB, MB, GB
          const size = Number(value);
          if (isNaN(size)) return 'N/A';

          if (size < 1024) {
            return `${size} B`;
          } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(2)} KB`;
          } else if (size < 1024 * 1024 * 1024) {
            return `${(size / (1024 * 1024)).toFixed(2)} MB`;
          } else {
            return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          }
        },
      },
      {
        key: 'viewUrl',
        title: t('data:knowledgeFiles.table.url', 'URL'),
        dataIndex: 'viewUrl',
        width: '25%',
        render: (value: unknown) => {
          const url = String(value || '');
          return (
            <div className="flex items-center">
              <Tooltip content={t('common.copy', 'Sao chép')}>
                <IconCard
                  icon="copy"
                  variant="default"
                  size="sm"
                  className="ml-2"
                  onClick={() => {
                    navigator.clipboard.writeText(url);
                  }}
                />
              </Tooltip>
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('data:common.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const timestamp = Number(value);
          return isNaN(timestamp) ? 'N/A' : new Date(timestamp).toLocaleString();
        },
      },
      
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showForm()}
            items={[
              {
                id: 'all',
                label: t('common.all', 'Tất cả'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common.bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: handleShowBulkDeleteConfirm,
                className: 'text-red-500',
                condition: selectedRowKeys.length > 0,
              },
            ]}
          />

          {/* Hiển thị ActiveFilters */}
          <ActiveFilters
            searchTerm={searchTerm}
            onClearSearch={() => {
              setSearchTerm('');
              handleSearch('');
            }}
            sortBy={sortBy}
            sortDirection={sortDirection as SortDirection}
            onClearSort={() => {
              setSortBy('');
              setSortDirection(SortDirection.ASC);
              handleSortChange('', 'asc');
            }}
            onClearAll={() => {
              setSearchTerm('');
              setSortBy('');
              setSortDirection(SortDirection.ASC);
              handleSearch('');
              handleSortChange('', 'asc');
            }}
          />
        </div>

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isVisible}>
          <KnowledgeFileForm onSuccess={handleUploadSuccess} onCancel={hideForm} />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<FileResponseDto>
            columns={filteredColumns}
            data={files}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t(
          'data:knowledgeFiles.confirmDeleteMessage',
          'Bạn có chắc chắn muốn xóa file tri thức này?'
        )}
        itemName={fileToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t(
          'data:knowledgeFiles.confirmBulkDeleteMessage',
          `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} file đã chọn?`
        )}
      />
    </div>
  );
};

export default KnowledgeFilesPage;
