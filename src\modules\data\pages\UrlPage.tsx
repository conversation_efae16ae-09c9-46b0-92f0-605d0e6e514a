import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Chip,
  DataTableWithActions,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig } from '@/shared/hooks/table';
import { NotificationUtil } from '@/shared/utils/notification';

import {
  useUrls,
  useCreateUrl,
  useDeleteUrl,
  useDeleteMultipleUrls,
} from '@/modules/data/url/hooks/useUrlQuery';
import { UrlDto, FindAllUrlDto, CreateUrlDto, CrawlDto } from '@/modules/data/url/types/url.types';
import UrlForm from '@/modules/data/components/forms/UrlForm';
import { CrawlUrlForm } from '@/modules/data/components/forms';
import { CrawlUrlFormValues } from '@/modules/data/components/forms/CrawlUrlForm';
import useCrawlUrlWithQueue from '@/modules/data/hooks/useCrawlUrlWithQueue';
import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Trang quản lý URL - Sử dụng DataTableWithActions
 */
const UrlPage: React.FC<Record<string, never>> = () => {
  const { t } = useTranslation(['data', 'common']);

  // State cho form và modal
  const [selectedUrl, setSelectedUrl] = useState<UrlDto | null>(null);
  const [isCrawling, setIsCrawling] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([
    { id: 'title', label: t('data:url.table.title', 'Tiêu đề'), visible: true },
    { id: 'type', label: t('data:url.table.type', 'Loại'), visible: true },
    { id: 'tags', label: t('data:url.table.tags', 'Tags'), visible: true },
    { id: 'createdAt', label: t('data:url.table.createdAt', 'Ngày tạo'), visible: true },
  ]);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form crawl URL
  const {
    isVisible: isCrawlFormVisible,
    showForm: showCrawlForm,
    hideForm: hideCrawlForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (url: UrlDto) => {
      setSelectedUrl(url);
      showEditForm();
    },
    [showEditForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<UrlDto>[]>(
    () => [
      {
        key: 'title',
        title: t('data:url.table.title', 'Tiêu đề'),
        dataIndex: 'title',
        render: (_, record: UrlDto) => (
          <div className="flex items-center">
            <div className="flex-1 min-w-0">
              <Typography variant="body2" className="font-medium truncate">
                {record.title}
              </Typography>
              <Typography variant="caption" className="text-gray-500 truncate">
                {record.url}
              </Typography>
            </div>
          </div>
        ),
        sortable: true,
      },
      {
        key: 'type',
        title: t('data:url.table.type', 'Loại'),
        dataIndex: 'type',
        render: (value: unknown) => <span>{String(value || '-')}</span>,
        sortable: true,
      },
      {
        key: 'tags',
        title: t('data:url.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags?.length > 0
                ? tags.map((tag, index) => (
                    <Chip key={index} size="sm">
                      {tag}
                    </Chip>
                  ))
                : '-'}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('data:url.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        render: (value: unknown) => {
          // Kiểm tra và chuyển đổi timestamp thành Date
          if (value) {
            const timestamp = typeof value === 'string' ? parseInt(value, 10) : (value as number);
            if (!isNaN(timestamp)) {
              return new Date(timestamp).toLocaleDateString('vi-VN');
            }
          }
          return '-';
        },
        sortable: true,
      },
    ],
    [t]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [{ id: 'all', label: t('common:all', 'Tất cả'), icon: 'list', value: 'all' }],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): FindAllUrlDto => {
    return {
      page: params.page,
      limit: params.pageSize,
      keyword: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  }, []);

  // Cấu hình data table
  const dataTableConfig = useDataTableConfig<UrlDto, FindAllUrlDto>({
    columns,
    filterOptions,
    showDateFilter: false,
    createQueryParams,
  });

  // API hooks
  const { data: urlsData, isLoading } = useUrls(dataTableConfig.createQueryParams({
    page: 1,
    pageSize: 10,
    searchTerm: '',
    sortBy: null,
    sortDirection: null,
    filterValue: undefined,
    dateRange: [null, null],
  }));

  const { mutateAsync: createUrl } = useCreateUrl();
  const { mutateAsync: deleteUrl } = useDeleteUrl();
  const { mutateAsync: deleteMultipleUrls } = useDeleteMultipleUrls();
  const { crawlUrlWithQueue } = useCrawlUrlWithQueue();



  // Xử lý submit form thêm mới
  const handleSubmitForm = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);

        // Chuẩn bị dữ liệu cho API
        const urlData: CreateUrlDto = {
          url: values.url as string,
          title: values.title as string,
          content: (values.description as string) || '',
          type: values.type as string,
          tags: values.tags ? (values.tags as string).split(',').map(tag => tag.trim()) : undefined,
        };

        if (selectedUrl?.id) {
          // Cập nhật URL
          await createUrl({
            ...urlData,
            id: selectedUrl.id, // id đã được thêm vào type CreateUrlDto
          });
          NotificationUtil.success({
            message: t('data:url.updateSuccess', 'Cập nhật URL thành công'),
          });
        } else {
          // Tạo mới URL
          await createUrl(urlData);
          NotificationUtil.success({
            message: t('data:url.createSuccess', 'Tạo URL mới thành công'),
          });
        }

        hideAddForm();
        hideEditForm();
        setSelectedUrl(null);
      } catch (error) {
        console.error('Error submitting URL form:', error);
        NotificationUtil.error({
          message: t('data:url.formError', 'Lỗi khi lưu URL'),
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [selectedUrl, createUrl, hideAddForm, hideEditForm, t]
  );

  // Xử lý submit form crawl URL
  const handleSubmitCrawlUrl = useCallback(
    async (values: CrawlUrlFormValues) => {
      try {
        setIsCrawling(true);

        // Chuẩn bị dữ liệu cho API
        const crawlData: CrawlDto = {
          url: values.url,
          depth: values.depth,
          maxUrls: values.maxUrls,
          ignoreRobotsTxt: values.ignoreRobotsTxt,
        };

        // Gọi API crawl URL với TaskQueue
        await crawlUrlWithQueue(crawlData);

        // Đóng form sau khi thêm vào queue
        hideCrawlForm();
      } catch (error) {
        console.error('Error crawling URL:', error);
        NotificationUtil.error({
          message: t('data:url.crawlError', 'Lỗi khi crawl URL'),
        });
      } finally {
        setIsCrawling(false);
      }
    },
    [crawlUrlWithQueue, hideCrawlForm, t]
  );

  // Xử lý hủy form crawl URL
  const handleCancelCrawlForm = useCallback(() => {
    setIsCrawling(false);
    hideCrawlForm();
  }, [hideCrawlForm]);

  return (
    <div>
      <div className="space-y-4">
        {/* DataTable với Actions */}
        <DataTableWithActions<UrlDto, FindAllUrlDto>
          data={urlsData?.items || []}
          loading={isLoading}
          columns={columns}
          dataTableConfig={dataTableConfig}
          actionsConfig={{
            onEdit: handleEdit,
            singleDelete: {
              deleteMutation: async (id: string) => {
                await deleteUrl(id);
              },
              i18nNamespace: 'data',
              entityName: 'URL',
            },
            bulkDelete: {
              deleteMutation: async (ids: string[]) => {
                await deleteMultipleUrls({ ids });
              },
              i18nNamespace: 'data',
              entityName: 'URL',
            },
          }}
          pagination={{
            current: urlsData?.meta.currentPage || 1,
            pageSize: 10,
            total: urlsData?.meta.totalItems || 0,
            onChange: () => {},
          }}
          columnVisibility={{
            columns: visibleColumns,
            onChange: setVisibleColumns,
          }}
          additionalIcons={[
            {
              icon: 'link',
              tooltip: t('data:url.crawl', 'Crawl URL'),
              variant: 'primary',
              onClick: showCrawlForm,
            },
          ]}
          onAdd={showAddForm}
          showDateFilter={false}
          showColumnFilter={true}
          rowKey="id"
          i18nNamespace="data"
        />

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isAddFormVisible}>
          <UrlForm onSubmit={handleSubmitForm} onCancel={hideAddForm} isSubmitting={isSubmitting} />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {selectedUrl && (
            <UrlForm
              initialValues={selectedUrl}
              onSubmit={handleSubmitForm}
              onCancel={hideEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form crawl URL */}
        <SlideInForm isVisible={isCrawlFormVisible}>
          <CrawlUrlForm
            onSubmit={handleSubmitCrawlUrl}
            onCancel={handleCancelCrawlForm}
            isLoading={isCrawling}
          />
        </SlideInForm>
      </div>
    </div>
  );
};

export default UrlPage;
