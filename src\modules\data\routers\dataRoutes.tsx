import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import DataManagementPage from '../pages/DataManagementPage';
import i18n from '@/lib/i18n';

// Import Data module pages
const FilesPage = lazy(() => import('@/modules/data/pages/MediaPage'));
const KnowledgeFilesPage = lazy(() => import('@/modules/data/pages/KnowledgeFilesPage'));
const UrlPage = lazy(() => import('@/modules/data/pages/UrlPage'));
const VectorStorePage = lazy(() => import('@/modules/data/pages/VectorStorePage'));

/**
 * Data module routes
 */
const dataRoutes: RouteObject[] = [
  {
    path: '/data',
    element: (
      <MainLayout title={i18n.t('data:title', 'Quản lý dữ liệu')}>
        <Suspense fallback={<Loading />}>
          <DataManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/data/media',
    element: (
      <MainLayout title="File Management">
        <Suspense fallback={<Loading />}>
          <FilesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/data/knowledge-files',
    element: (
      <MainLayout title="Knowledge Files">
        <Suspense fallback={<Loading />}>
          <KnowledgeFilesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/data/url',
    element: (
      <MainLayout title={i18n.t('data:url.title', 'Quản lý URL')}>
        <Suspense fallback={<Loading />}>
          <UrlPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/data/vector-store',
    element: (
      <MainLayout title="Vector Store Management">
        <Suspense fallback={<Loading />}>
          <VectorStorePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default dataRoutes;
