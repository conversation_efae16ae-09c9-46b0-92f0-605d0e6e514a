import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * DTO cho thông tin URL
 */
export interface UrlDto {
  /**
   * Mã định danh tài nguyên URL
   */
  id: string;

  /**
   * Đường dẫn URL
   */
  url: string;

  /**
   * Tiêu đề của tài nguyên URL
   */
  title: string;

  /**
   * Nội dung về tài nguyên URL
   */
  content: string;

  /**
   * Loại tài nguyên URL
   */
  type?: string;

  /**
   * Các thẻ phân loại URL
   */
  tags?: string[];

  /**
   * Mã người sở hữu tài nguyên URL
   */
  ownedBy: number;

  /**
   * Thời điểm tạo bản ghi (unix timestamp)
   */
  createdAt: number;

  /**
   * Thời điểm cập nhật bản ghi (unix timestamp)
   */
  updatedAt: number;

  /**
   * Index signature to satisfy generic constraints
   */
  [key: string]: unknown;
}

/**
 * DTO cho tạo URL mới
 */
export interface CreateUrlDto {
  /**
   * Mã định danh tài nguyên URL (chỉ dùng khi cập nhật)
   */
  id?: string;

  /**
   * Đường dẫn URL
   */
  url: string;

  /**
   * Tiêu đề của tài nguyên URL
   */
  title: string;

  /**
   * Nội dung về tài nguyên URL
   */
  content: string;

  /**
   * Loại tài nguyên URL
   */
  type?: string;

  /**
   * Các thẻ phân loại URL
   */
  tags?: string[];
}

/**
 * DTO cho cập nhật URL
 */
export interface UpdateUrlDto {
  /**
   * Đường dẫn URL
   */
  url?: string;

  /**
   * Tiêu đề của tài nguyên URL
   */
  title?: string;

  /**
   * Nội dung về tài nguyên URL
   */
  content?: string;

  /**
   * Loại tài nguyên URL
   */
  type?: string;

  /**
   * Các thẻ phân loại URL
   */
  tags?: string[];
}

/**
 * DTO cho tìm kiếm URL
 */
export interface FindAllUrlDto {
  /**
   * Số trang
   */
  page?: number;

  /**
   * Số lượng kết quả trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm URL, tìm kiếm theo title, content và url
   */
  keyword?: string;

  /**
   * Loại URL cần lọc
   */
  type?: string;

  /**
   * Các thẻ cần lọc
   */
  tags?: string[];

  /**
   * Trường sắp xếp
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: SortDirection;
}

/**
 * DTO cho crawl URL
 */
export interface CrawlDto {
  /**
   * URL gốc để bắt đầu crawl
   */
  url: string;

  /**
   * Độ sâu tìm kiếm URL (1-3)
   */
  depth: number;

  /**
   * Bỏ qua kiểm tra robots.txt (mặc định: false)
   */
  ignoreRobotsTxt?: boolean;

  /**
   * Số lượng URL tối đa để crawl (1-100, mặc định: 20)
   */
  maxUrls?: number;
}

/**
 * Interface cho metadata được trích xuất
 */
export interface ExtractedMetadata {
  /**
   * Đường dẫn URL
   */
  url: string;

  /**
   * Tiêu đề của tài nguyên URL
   */
  title: string;

  /**
   * Nội dung về tài nguyên URL
   */
  content: string;

  /**
   * Các thẻ phân loại URL
   */
  tags: string;
}

/**
 * DTO cho kết quả crawl URL
 */
export interface CrawlResultDto {
  /**
   * Trạng thái của quá trình crawl
   */
  status: 'success' | 'error';

  /**
   * Thông báo kết quả
   */
  message: string;

  /**
   * Số lượng URL đã xử lý
   */
  urlsProcessed?: number;

  /**
   * Danh sách URL đã crawl
   */
  urls?: UrlDto[];

  /**
   * Danh sách lỗi (nếu có)
   */
  errors?: string[];
}

/**
 * Kiểu dữ liệu cho danh sách URL có phân trang
 */
export type PaginatedUrlResult = PaginatedResult<UrlDto>;
