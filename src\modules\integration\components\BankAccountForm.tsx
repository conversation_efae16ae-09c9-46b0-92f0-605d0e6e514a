import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Card,
  FormItem,
  Input,
  Select,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/useFormErrors';
import { NotificationUtil } from '@/shared/utils/notification';
import { useCreateBankAccount } from '../hooks/useBankAccount';
import {
  createBankAccountSchema,
  CreateBankAccountFormData,
} from '../schemas/bank-account.schema';
import {
  BankAccountDto,
  BANK_OPTIONS,
  requiresOTP,
  BankCode,
} from '../types/bank-account.types';

interface BankAccountFormProps {
  /**
   * Callback khi tạo thành công
   */
  onSuccess: (bankAccount: BankAccountDto, needsOTP: boolean) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component form tạo tài khoản ngân hàng
 */
const BankAccountForm: React.FC<BankAccountFormProps> = ({
  onSuccess,
  onCancel,
  className = '',
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // Form hooks
  const { setFormErrors } = useFormErrors<CreateBankAccountFormData>();

  // API hooks
  const createBankAccountMutation = useCreateBankAccount();

  // Form state
  const [formData, setFormData] = useState<CreateBankAccountFormData>({
    bankCode: BankCode.OCB, // Default to OCB
    accountNumber: '',
    idNumber: '',
    phoneNumber: '',
    storeName: '',
    storeAddress: '',
  });

  /**
   * Handle input changes
   */
  const handleInputChange = (field: keyof CreateBankAccountFormData, value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  /**
   * Xử lý submit form
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data using schema
    const result = createBankAccountSchema.safeParse(formData);
    if (!result.success) {
      const errors: Record<string, string> = {};
      result.error.errors.forEach(error => {
        if (error.path.length > 0) {
          errors[error.path[0] as string] = error.message;
        }
      });
      setFormErrors(errors);
      return;
    }
    try {
      const response = await createBankAccountMutation.mutateAsync(formData);

      NotificationUtil.success({
        title: t('integration:bankAccount.createSuccess', 'Tạo tài khoản thành công'),
        message: t(
          'integration:bankAccount.createSuccessDescription',
          'Tài khoản ngân hàng đã được tạo thành công'
        ),
      });

      // Kiểm tra xem có cần OTP không
      const needsOTP = requiresOTP(formData.bankCode);
      onSuccess(response.result, needsOTP);
    } catch (error: unknown) {
      console.error('Error creating bank account:', error);

      // Xử lý lỗi từ API
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { data?: { errors?: Record<string, string> } } };
        if (apiError.response?.data?.errors) {
          setFormErrors(apiError.response.data.errors);
        } else {
          NotificationUtil.error({
            title: t('integration:bankAccount.createError', 'Tạo tài khoản thất bại'),
            message: t(
              'integration:bankAccount.createErrorDescription',
              'Không thể tạo tài khoản ngân hàng. Vui lòng kiểm tra lại thông tin.'
            ),
          });
        }
      } else {
        NotificationUtil.error({
          title: t('integration:bankAccount.createError', 'Tạo tài khoản thất bại'),
          message: t(
            'integration:bankAccount.createErrorDescription',
            'Không thể tạo tài khoản ngân hàng. Vui lòng kiểm tra lại thông tin.'
          ),
        });
      }
    }
  };

  // Tạo options cho Select ngân hàng
  const bankOptions = BANK_OPTIONS.map(bank => ({
    value: bank.value,
    label: bank.label,
  }));

  return (
    <Card className={`p-6 ${className}`}>
      <div className="mb-6">
        <Typography variant="h5" className="mb-2">
          {t('integration:bankAccount.createTitle', 'Thêm tài khoản ngân hàng')}
        </Typography>
        <Typography variant="body2" color="muted">
          {t(
            'integration:bankAccount.createDescription',
            'Nhập thông tin tài khoản ngân hàng để tích hợp với hệ thống'
          )}
        </Typography>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Chọn ngân hàng */}
        <FormItem
          name="bankCode"
          label={t('integration:bankAccount.bankName', 'Ngân hàng')}
          required
        >
          <Select
            value={formData.bankCode}
            onChange={(value) => handleInputChange('bankCode', value)}
            options={bankOptions}
            placeholder={t('integration:bankAccount.selectBank', 'Chọn ngân hàng')}
            fullWidth
          />
        </FormItem>

        {/* Số tài khoản */}
        <FormItem
          name="accountNumber"
          label={t('integration:bankAccount.accountNumber', 'Số tài khoản')}
          required
          helpText={t(
            'integration:bankAccount.accountNumberHelp',
            'Nhập số tài khoản ngân hàng (6-20 ký tự số)'
          )}
        >
          <Input
            value={formData.accountNumber}
            onChange={(e) => handleInputChange('accountNumber', e.target.value)}
            placeholder={t('integration:bankAccount.accountNumberPlaceholder', 'Nhập số tài khoản')}
            fullWidth
          />
        </FormItem>

        {/* Số CMND/CCCD */}
        <FormItem
          name="idNumber"
          label={t('integration:bankAccount.idNumber', 'Số CMND/CCCD')}
          required
          helpText={t(
            'integration:bankAccount.idNumberHelp',
            'Nhập số CMND/CCCD đã đăng ký với ngân hàng'
          )}
        >
          <Input
            value={formData.idNumber}
            onChange={(e) => handleInputChange('idNumber', e.target.value)}
            placeholder={t('integration:bankAccount.idNumberPlaceholder', 'Nhập số CMND/CCCD')}
            fullWidth
          />
        </FormItem>

        {/* Số điện thoại */}
        <FormItem
          name="phoneNumber"
          label={t('integration:bankAccount.phoneNumber', 'Số điện thoại')}
          required
          helpText={t(
            'integration:bankAccount.phoneNumberHelp',
            'Số điện thoại đã đăng ký với ngân hàng để nhận OTP'
          )}
        >
          <Input
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
            placeholder={t('integration:bankAccount.phoneNumberPlaceholder', 'Nhập số điện thoại')}
            fullWidth
          />
        </FormItem>

        {/* Tên điểm bán */}
        <FormItem
          name="storeName"
          label={t('integration:bankAccount.storeName', 'Tên điểm bán')}
          required
          helpText={t(
            'integration:bankAccount.storeNameHelp',
            'Tên cửa hàng/điểm bán hàng của bạn'
          )}
        >
          <Input
            value={formData.storeName}
            onChange={(e) => handleInputChange('storeName', e.target.value)}
            placeholder={t('integration:bankAccount.storeNamePlaceholder', 'Nhập tên điểm bán')}
            fullWidth
          />
        </FormItem>

        {/* Địa chỉ điểm bán */}
        <FormItem
          name="storeAddress"
          label={t('integration:bankAccount.storeAddress', 'Địa chỉ điểm bán')}
          required
          helpText={t(
            'integration:bankAccount.storeAddressHelp',
            'Địa chỉ chi tiết của cửa hàng/điểm bán hàng'
          )}
        >
          <Input
            value={formData.storeAddress}
            onChange={(e) => handleInputChange('storeAddress', e.target.value)}
            placeholder={t('integration:bankAccount.storeAddressPlaceholder', 'Nhập địa chỉ điểm bán')}
            fullWidth
          />
        </FormItem>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={createBankAccountMutation.isPending}
          >
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={createBankAccountMutation.isPending}
          >
            {t('integration:bankAccount.create', 'Tạo tài khoản')}
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default BankAccountForm;
