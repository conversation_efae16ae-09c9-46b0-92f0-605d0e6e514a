import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Input, FormItem, Form, Icon } from '@/shared/components/common';
import { providerModelSchema } from '../provider-model/schemas';
import { ProviderModel } from '../provider-model/types';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '../provider-model/types';

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-gray-900 dark:text-gray-100">{name}</div>
      </div>
    </Card>
  );
};

interface ProviderModelFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: ProviderModel | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Form tạo/chỉnh sửa Provider Model
 */
const ProviderModelForm: React.FC<ProviderModelFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const isEditMode = !!initialData;

  // State để quản lý provider được chọn
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    initialData?.type || TypeProviderEnum.OPENAI
  );

  // Chuẩn bị giá trị mặc định cho form
  const defaultValues = initialData
    ? {
        name: initialData.name,
        type: selectedProvider,
        apiKey: '', // Không hiển thị API key cũ vì lý do bảo mật
      }
    : {
        name: '',
        type: selectedProvider,
        apiKey: '',
      };

  // Cập nhật selectedProvider khi initialData thay đổi
  useEffect(() => {
    if (initialData?.type) {
      setSelectedProvider(initialData.type);
    }
  }, [initialData?.type]);

  // Xử lý submit form
  const handleFormSubmit = (values: Record<string, unknown>) => {
    // Thêm selectedProvider vào values
    const finalValues = {
      ...values,
      type: selectedProvider,
    };
    onSubmit(finalValues);
  };

  // Xử lý khi chọn provider
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    if (!readOnly && !isSubmitting && !isEditMode) {
      setSelectedProvider(provider);
    }
  };

  // Danh sách providers với tên hiển thị
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  return (
    <div
      title={readOnly
        ? t('admin:integration.providerModel.form.view', 'Xem Provider Model')
        : isEditMode
        ? t('admin:integration.providerModel.form.edit', 'Chỉnh sửa Provider Model')
        : t('admin:integration.providerModel.form.create', 'Tạo Provider Model')}

    >
      <Form
        schema={providerModelSchema}
        onSubmit={handleFormSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
      >
        <div className="space-y-6">
          {/* Provider Name */}
          <FormItem
            name="name"
            label={t('admin:integration.providerModel.form.fields.name')}
            required
          >
            <Input
              placeholder={t('admin:integration.providerModel.form.placeholders.name')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* Provider Type */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('admin:integration.providerModel.form.fields.type')}
              <span className="text-red-500 ml-1">*</span>
            </label>
            <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
              {providers.map((provider) => (
                <ProviderCard
                  key={provider.type}
                  provider={provider.type}
                  name={provider.name}
                  isSelected={selectedProvider === provider.type}
                  onClick={handleProviderSelect}
                  disabled={readOnly || isSubmitting || isEditMode}
                />
              ))}
            </div>
            {/* Hidden input để Form component có thể track giá trị */}
            <div className="hidden">
              <FormItem name="type">
                <Input value={selectedProvider} readOnly />
              </FormItem>
            </div>
          </div>

          {/* API Key */}
          <FormItem
            name="apiKey"
            label={t('admin:integration.providerModel.form.fields.apiKey')}
            required={!isEditMode} // Không bắt buộc khi edit (có thể giữ nguyên API key cũ)
          >
            <Input
              type="password"
              placeholder={
                isEditMode
                  ? 'Để trống nếu không muốn thay đổi API key...'
                  : t('admin:integration.providerModel.form.placeholders.apiKey')
              }
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* Hiển thị thông tin bổ sung khi ở chế độ xem */}
          {readOnly && initialData && (
            <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdAt')}:
                  </span>
                  <div className="mt-1">
                    {new Date(initialData.createdAt).toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdBy')}:
                  </span>
                  <div className="mt-1">
                    {initialData.createdBy.name}
                  </div>
                </div>
                {initialData.updatedAt && (
                  <>
                    <div>
                      <span className="font-medium text-gray-500 dark:text-gray-400">
                        {t('common:updatedAt')}:
                      </span>
                      <div className="mt-1">
                        {new Date(initialData.updatedAt).toLocaleString()}
                      </div>
                    </div>
                    {initialData.updatedBy && (
                      <div>
                        <span className="font-medium text-gray-500 dark:text-gray-400">
                          {t('common:updatedBy')}:
                        </span>
                        <div className="mt-1">
                          {initialData.updatedBy.name}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('admin:integration.providerModel.actions.cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              {t('admin:integration.providerModel.actions.save')}
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default ProviderModelForm;
