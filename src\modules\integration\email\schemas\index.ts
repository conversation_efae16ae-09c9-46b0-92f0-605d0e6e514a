import { z } from 'zod';

/**
 * Email Server Configuration Schemas
 */

export const emailServerConfigurationSchema = z.object({
  serverName: z
    .string()
    .min(1, 'admin:integration.email.validation.serverName.required')
    .max(100, 'admin:integration.email.validation.serverName.maxLength'),

  host: z
    .string()
    .min(1, 'admin:integration.email.validation.host.required')
    .max(255, 'admin:integration.email.validation.host.maxLength'),

  port: z
    .number()
    .min(1, 'admin:integration.email.validation.port.min')
    .max(65535, 'admin:integration.email.validation.port.max'),

  username: z
    .string()
    .min(1, 'admin:integration.email.validation.username.required')
    .email('admin:integration.email.validation.username.email'),

  password: z
    .string()
    .min(1, 'admin:integration.email.validation.password.required')
    .min(6, 'admin:integration.email.validation.password.minLength'),

  useSsl: z.boolean(),

  useStartTls: z.boolean(),

  additionalSettings: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      try {
        JSON.parse(val);
        return true;
      } catch {
        return false;
      }
    }, 'admin:integration.email.validation.additionalSettings.invalidJson'),

  userId: z.number().optional(),

  isActive: z.boolean().default(true),
});

export const updateEmailServerConfigurationSchema = emailServerConfigurationSchema.partial();

export const testEmailServerSchema = z.object({
  recipientEmail: z
    .string()
    .email('admin:integration.email.validation.recipientEmail.email')
    .optional(),

  subject: z
    .string()
    .max(200, 'admin:integration.email.validation.subject.maxLength')
    .optional(),
});

export const emailServerQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  userId: z.number().optional(),
});

export type EmailServerConfigurationFormData = z.infer<typeof emailServerConfigurationSchema>;
export type UpdateEmailServerConfigurationFormData = z.infer<typeof updateEmailServerConfigurationSchema>;
export type TestEmailServerFormData = z.infer<typeof testEmailServerSchema>;
export type EmailServerQueryFormData = z.infer<typeof emailServerQuerySchema>;
