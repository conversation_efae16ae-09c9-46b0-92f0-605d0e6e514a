{"integration": {"common": {"title": "Integration", "description": "Manage integrations with external services", "back": "Back", "cancel": "Cancel", "delete": "Delete", "confirm": "Confirm", "activate": "Activate", "deactivate": "Deactivate", "retry": "Retry", "error": "An error occurred"}, "sms": {"title": "SMS Integration", "description": "Configure integration with SMS providers", "configName": "Configuration Name", "configNameHelp": "Example: SMS Marketing, SMS Notifications", "configNamePlaceholder": "Enter configuration name", "isActive": "Active", "provider": "SMS Provider", "selectProvider": "Select provider", "otherProvider": "Other", "fromPhone": "From Phone Number", "fromPhoneHelp": "Phone number displayed when sending SMS", "apiKey": "API Key", "apiSecret": "API Secret", "testConnection": "Test Connection", "testResult": "Connection Test Result", "testSuccess": "Successfully connected to SMS provider", "testError": "Could not connect to SMS provider. Please check your configuration.", "saveSuccess": "SMS configuration saved successfully", "saveError": "Error saving SMS configuration"}, "email": {"title": "Email Integration", "description": "Configure integration with Email providers", "configName": "Configuration Name", "configNameHelp": "Example: Company Email, Personal Gmail", "configNamePlaceholder": "Enter configuration name", "senderEmail": "Sender <PERSON><PERSON>", "senderName": "Sender Name", "senderNameHelp": "Name displayed when recipient views email", "senderNamePlaceholder": "Enter sender name", "smtpHost": "SMTP Host", "smtpHostHelp": "Example: smtp.gmail.com, smtp.office365.com", "smtpPort": "SMTP Port", "smtpPortHelp": "Example: 587 for TLS, 465 for SSL", "requireSSL": "Require SSL", "securityProtocol": "Security Protocol", "requireAuth": "Require Authentication", "username": "Username", "password": "Password", "testConnection": "Test Connection", "testResult": "Connection Test Result", "testSuccess": "Successfully connected to email server", "testError": "Could not connect to email server. Please check your configuration.", "saveSuccess": "Email configuration saved successfully", "saveError": "Error saving email configuration"}, "social": {"title": "Social Media Integration", "description": "Manage integration with social media platforms", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn", "youtube": "YouTube", "tiktok": "TikTok", "connect": "Connect", "disconnect": "Disconnect", "connected": "Connected", "notConnected": "Not Connected", "connectSuccess": "Successfully connected to {{platform}}", "connectError": "Error connecting to {{platform}}", "disconnectSuccess": "Successfully disconnected from {{platform}}", "disconnectError": "Error disconnecting from {{platform}}", "networkAriaLabel": "{{name}} social network"}, "facebook": {"title": "Facebook Integration", "description": "Manage linked Facebook accounts", "addPage": "Add Facebook Page", "connecting": "Connecting...", "processing": "Processing Facebook connection...", "search": "Search Facebook Page...", "loadError": "Unable to load Facebook Pages list", "noPages": "No Facebook Pages yet", "noPagesDescription": "You haven't linked any Facebook Pages yet. Add a Facebook Page to get started.", "confirmDelete": "Are you sure you want to delete this Facebook Page?", "connectAgent": "Connect Agent", "disconnectAgent": "Disconnect Agent", "status": {"label": "Status", "active": "Active", "inactive": "Inactive", "error": "Error"}, "agent": {"label": "Agent"}, "pageName": "Page Name", "personalName": "Personal Name", "pageId": "Page ID", "isActive": "Is Active", "hasError": "<PERSON>"}, "accounts": {"title": "Linked Accounts", "description": "Manage accounts linked to the system", "addAccount": "Add Account", "removeAccount": "Remove Account", "accountName": "Account Name", "accountType": "Account Type", "linkedDate": "Linked Date", "noAccounts": "No accounts linked yet", "confirmRemove": "Are you sure you want to remove this account?", "removeSuccess": "Account removed successfully", "removeError": "Error removing account", "defaultAccount": "De<PERSON><PERSON> Account", "failedToLoad": "Failed to load accounts"}, "website": {"title": "Website Integration", "description": "Manage integration with websites", "domain": "Domain", "apiKey": "API Key", "secretKey": "Secret Key", "webhookUrl": "Webhook URL", "generateKey": "Generate New Key", "copyKey": "Copy", "keyCopied": "Copied to clipboard", "saveSuccess": "Website configuration saved successfully", "saveError": "Error saving website configuration", "confirmActivate": "Are you sure you want to activate this website?", "confirmDeactivate": "Are you sure you want to deactivate this website?", "host": "Host", "status": "Status", "verified": "Verified", "notVerified": "Not Verified", "connected": "Connected", "notConnected": "Not Connected", "agent": "Agent", "noAgent": "No Agent Connected", "createdAt": "Created At", "actions": "Actions", "widgetScript": "Widge<PERSON>", "widgetScriptDesc": "Copy and paste this script into your website to integrate the chat widget.", "createTitle": "Add New Website", "createSuccess": "Website created successfully!", "createSuccessDesc": "Website has been added to the list.", "createError": "Failed to create website!", "createErrorDesc": "Please try again later.", "creating": "Creating...", "create": "Create Website", "deleteSuccess": "Website deleted successfully!", "deleteSuccessDesc": "Website has been removed from the list.", "deleteError": "Failed to delete website!", "deleteErrorDesc": "Please try again later.", "copySuccess": "Copied!", "copySuccessDesc": "Script has been copied to clipboard.", "copyScript": "Copy", "noWebsites": "No Websites Yet", "noWebsitesDescription": "You haven't added any websites yet. Add a website to get started.", "addWebsite": "Add Website", "noSearchResults": "No Results Found", "noSearchResultsDescription": "No websites match your search criteria.", "clearFilters": "Clear Filters", "confirmDelete": "Confirm Delete", "confirmDeleteDesc": "Are you sure you want to delete this website? This action cannot be undone.", "deleting": "Deleting...", "form": {"websiteName": "Website Name", "websiteNamePlaceholder": "Enter website name", "host": "Host/Domain", "hostPlaceholder": "redai.vn or https://www.redai.vn", "hostDescription": "Enter domain or full URL. System will automatically normalize."}, "filter": {"websiteName": "Website Name", "host": "Host", "createdAt": "Created At", "verify": "Verification Status", "asc": "Ascending", "desc": "Descending", "all": "All", "verified": "Verified", "unverified": "Unverified"}}, "bankAccount": {"title": "Bank Account Integration", "description": "Manage bank accounts integrated with the system", "createTitle": "Add Bank Account", "createDescription": "Enter bank account information to integrate with the system", "bankName": "Bank", "selectBank": "Select bank", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter account number", "accountNumberHelp": "Enter bank account number (6-20 digits)", "accountName": "Account Name", "idNumber": "ID Number", "idNumberPlaceholder": "Enter ID number", "idNumberHelp": "Enter ID number registered with the bank", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "phoneNumberHelp": "Phone number registered with the bank to receive OTP", "storeName": "Store Name", "storeNamePlaceholder": "Enter store name", "storeNameHelp": "Your store/business name", "storeAddress": "Store Address", "storeAddressPlaceholder": "Enter store address", "storeAddressHelp": "Detailed address of your store/business", "create": "Create Account", "bankInfo": "Bank Information", "status": {"active": "Active", "pending": "Pending Verification", "inactive": "Inactive", "suspended": "Suspended", "expired": "Expired"}, "virtualAccount": {"title": "Virtual Account", "yes": "Yes", "no": "No"}, "createVA": "Create Virtual Account", "createSuccess": "Account created successfully", "createSuccessDescription": "Bank account has been created successfully", "createError": "Failed to create account", "createErrorDescription": "Unable to create bank account. Please check the information.", "createCompleteSuccess": "Account creation completed", "createCompleteSuccessDescription": "Bank account has been created and activated successfully", "deleteSuccess": "Deleted successfully", "deleteSuccessDescription": "Bank account has been deleted successfully", "deleteError": "Failed to delete", "deleteErrorDescription": "Unable to delete bank account. Please try again.", "deleteConfirmTitle": "Confirm account deletion", "deleteConfirmMessage": "Are you sure you want to delete this bank account? This action cannot be undone.", "bulkDeleteSuccess": "Deleted successfully", "bulkDeleteSuccessDescription": "Deleted {{count}} bank accounts", "bulkDeleteError": "Failed to delete", "bulkDeleteErrorDescription": "Unable to delete bank accounts. Please try again.", "bulkDeleteConfirmTitle": "Confirm multiple account deletion", "bulkDeleteConfirmMessage": "Are you sure you want to delete {{count}} selected bank accounts? This action cannot be undone.", "selectAccountsToDelete": "Please select at least one account to delete", "createVASuccess": "Virtual account created successfully", "createVASuccessDescription": "Virtual account has been created successfully", "createVAError": "Failed to create virtual account", "createVAErrorDescription": "Unable to create virtual account. Please try again.", "activateSuccess": "Activated successfully", "activateSuccessDescription": "Bank account has been activated successfully", "otpVerification": "OTP Verification", "otpVerificationDescription": "Enter the OTP code sent to the phone number registered with {{bankName}}", "enterOtp": "Enter OTP code", "otpSent": "OTP code sent", "otpSentDescription": "Please check SMS messages on your phone", "otpSendError": "Failed to send OTP", "otpSendErrorDescription": "Unable to send OTP code. Please try again later.", "resendOtp": "Resend OTP", "resendOtpCountdown": "Resend code in {{countdown}} seconds", "verify": "Verify", "otpVerifySuccess": "Verification successful", "otpVerifySuccessDescription": "Bank account has been activated successfully", "otpVerifyError": "Verification failed", "otpVerifyErrorDescription": "OTP code is incorrect or expired. Please try again.", "invalidOtpLength": "Incorrect OTP length", "invalidOtpLengthDescription": "OTP code must be {{length}} characters"}, "banking": {"bankAccount": "Bank Account", "bank": "Bank", "connectionMethod": "Connection Method", "estimatedBalance": "Estimated Balance", "accountName": "Account Name", "accountNumber": "Account Number", "connectApi": "Connect Banking API"}, "ai": {"title": "AI Providers Management", "description": "Description", "id": "ID", "name": "Provider Name", "baseUrl": "Base URL", "apiVersion": "API Version", "models": "Supported Models", "maxTokens": "<PERSON>", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "addProvider": "Add AI Provider", "editProvider": "Edit AI Provider", "providersList": "AI Providers List", "namePlaceholder": "Enter AI provider name", "baseUrlPlaceholder": "Enter API base URL", "descriptionPlaceholder": "Enter description", "apiVersionPlaceholder": "Enter API version", "noData": "No data available", "searchPlaceholder": "Search providers...", "filterByStatus": "Filter by status", "allStatuses": "All statuses", "enable": "Enable", "disable": "Disable", "confirmDeleteMessage": "Are you sure you want to delete AI provider {{name}}?"}, "apiKeys": {"title": "API Keys Management", "description": "Description", "id": "ID", "apiKey": "API Key", "scope": "Access Scope", "environment": "Environment", "expiredAt": "Expiration Date", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "addNew": "Create New API Key", "createNew": "Create New API Key", "list": "API Keys List", "descriptionPlaceholder": "Enter description for API Key", "selectDate": "Select expiration date", "noData": "No data available", "searchPlaceholder": "Search by description...", "filterByStatus": "Filter by status", "allStatuses": "All statuses", "enable": "Enable", "disable": "Disable", "confirmDeleteMessage": "Are you sure you want to delete this API Key?", "createSuccess": "Created new API Key with description: {{description}}", "deleteSuccess": "Deleted API Key: {{api<PERSON>ey}}", "toggleSuccess": "{{action}} API Key: {{api<PERSON>ey}}"}}}