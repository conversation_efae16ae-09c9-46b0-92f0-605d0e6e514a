{"integration": {"common": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp với dịch vụ bên ngo<PERSON>i", "back": "Quay lại", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "confirm": "<PERSON><PERSON><PERSON>", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "Tắt", "retry": "<PERSON><PERSON><PERSON> lại", "error": "Đ<PERSON> xảy ra lỗi"}, "sms": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hình tích hợp với nhà cung cấp SMS", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "<PERSON><PERSON> dụ: SMS Marketing, SMS Thông báo", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "isActive": "<PERSON><PERSON><PERSON>", "provider": "<PERSON>hà cung cấp SMS", "selectProvider": "<PERSON><PERSON><PERSON> nhà cung cấp", "otherProvider": "K<PERSON><PERSON><PERSON>", "fromPhone": "<PERSON><PERSON> điện tho<PERSON>i g<PERSON>i", "fromPhoneHelp": "<PERSON><PERSON> điện thoại hiển thị khi gửi SMS", "apiKey": "API Key", "apiSecret": "API Secret", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "testSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công đến nhà cung cấp SMS", "testError": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến nhà cung cấp SMS. <PERSON><PERSON> lòng kiểm tra lại cấu hình.", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình <PERSON> thành công", "saveError": "Lỗi khi lưu cấu hình <PERSON>"}, "email": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hình tích hợp với nhà cung cấp <PERSON>", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "Ví dụ: <PERSON><PERSON>, <PERSON><PERSON> c<PERSON> nhân", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "senderEmail": "<PERSON><PERSON>", "senderName": "<PERSON><PERSON><PERSON><PERSON>i", "senderNameHelp": "<PERSON><PERSON>n hiển thị khi người nhận xem email", "senderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i", "smtpHost": "SMTP Host", "smtpHostHelp": "Ví dụ: smtp.gmail.com, smtp.office365.com", "smtpPort": "SMTP Port", "smtpPortHelp": "Ví dụ: 587 cho TLS, 465 cho SSL", "requireSSL": "<PERSON><PERSON><PERSON> c<PERSON>u SSL", "securityProtocol": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> b<PERSON><PERSON> mật", "requireAuth": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c thực", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "testSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công đến máy chủ email", "testError": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến máy chủ email. <PERSON><PERSON> lòng kiểm tra lại cấu hình.", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình <PERSON> thành công", "saveError": "Lỗi khi lưu cấu hình email"}, "social": {"title": "<PERSON><PERSON><PERSON> h<PERSON>p mạng xã hội", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp với các mạng xã hội", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn", "youtube": "YouTube", "tiktok": "TikTok", "connect": "<PERSON><PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connectSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công với {{platform}}", "connectError": "Lỗi khi kết nối với {{platform}}", "disconnectSuccess": "<PERSON><PERSON> ngắt kết nối với {{platform}}", "disconnectError": "Lỗi khi ngắt kết nối với {{platform}}", "networkAriaLabel": "<PERSON><PERSON><PERSON> xã hội {{name}}"}, "facebook": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài k<PERSON>n Facebook đã liên kết", "addPage": "Thêm Facebook Page", "connecting": "<PERSON><PERSON> kết nối...", "processing": "<PERSON><PERSON> x<PERSON> lý kết nối Facebook...", "search": "<PERSON><PERSON><PERSON> Facebook Page...", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Facebook Pages", "noPages": "Chưa có Facebook Page nào", "noPagesDescription": "<PERSON><PERSON>n chưa liên kết Facebook Page nào. <PERSON><PERSON><PERSON> thêm Facebook Page để bắt đầu.", "confirmDelete": "Bạn có chắc chắn muốn xóa Facebook Page này?", "connectAgent": "<PERSON><PERSON><PERSON>", "disconnectAgent": "<PERSON><PERSON><PERSON> k<PERSON>", "status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "error": "Lỗi"}, "agent": {"label": "Agent"}, "pageName": "<PERSON><PERSON><PERSON>", "personalName": "<PERSON><PERSON><PERSON> c<PERSON> nhân", "pageId": "Page ID", "isActive": "<PERSON><PERSON> ho<PERSON>t động", "hasError": "<PERSON><PERSON> lỗi"}, "accounts": {"title": "<PERSON><PERSON><PERSON> k<PERSON>n liên kết", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài khoản đã liên kết với hệ thống", "addAccount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "removeAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "linkedDate": "<PERSON><PERSON><PERSON> li<PERSON>", "noAccounts": "<PERSON><PERSON><PERSON> có tài khoản nào đ<PERSON><PERSON><PERSON> liên kết", "confirmRemove": "Bạn có chắc chắn muốn xóa tài khoản này?", "removeSuccess": "<PERSON><PERSON><PERSON> tài kho<PERSON>n thành công", "removeError": "Lỗi khi xóa tài k<PERSON>n", "defaultAccount": "<PERSON><PERSON><PERSON> k<PERSON>n mặc định", "failedToLoad": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách tài k<PERSON>n"}, "website": {"title": "<PERSON><PERSON><PERSON> Website", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp với website", "domain": "<PERSON><PERSON><PERSON>", "apiKey": "API Key", "secretKey": "Secret Key", "webhookUrl": "Webhook URL", "generateKey": "Tạo key mới", "copyKey": "Sao chép", "keyCopied": "Đã sao chép vào clipboard", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình website thành công", "saveError": "Lỗi khi lưu cấu hình website", "confirmActivate": "Bạn có chắc chắn muốn bật website này?", "confirmDeactivate": "Bạn có chắc chắn muốn tắt website này?", "host": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "verified": "<PERSON><PERSON> xác thực", "notVerified": "<PERSON><PERSON><PERSON> x<PERSON> thực", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "agent": "Agent", "noAgent": "<PERSON><PERSON><PERSON> k<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "widgetScript": "Widge<PERSON>", "widgetScriptDesc": "Sao chép và dán đoạn script này vào website của bạn để tích hợp chat widget.", "createTitle": "Thêm Website Mới", "createSuccess": "Tạo website thành công!", "createSuccessDesc": "Website đã đ<PERSON><PERSON><PERSON> thêm vào danh sách.", "createError": "Tạo website thất bại!", "createErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "creating": "<PERSON><PERSON> tạo...", "create": "Tạo Website", "deleteSuccess": "Xóa website thành công!", "deleteSuccessDesc": "Website đã đư<PERSON>c xóa khỏi danh sách.", "deleteError": "Xóa website thất bại!", "deleteErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "copySuccess": "Đã sao chép!", "copySuccessDesc": "Script đã đư<PERSON>c sao chép vào clipboard.", "copyScript": "Sao chép", "noWebsites": "Chưa có Website nào", "noWebsitesDescription": "Bạn chưa thêm Website nào. Hãy thêm Website để bắt đầu.", "addWebsite": "Thêm Website", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noSearchResultsDescription": "Không có website nào phù hợp với tiêu chí tìm kiếm của bạn.", "clearFilters": "Xóa bộ lọc", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirmDeleteDesc": "Bạn có chắc chắn muốn xóa website này? Hành động này không thể hoàn tác.", "deleting": "Đang xóa...", "form": {"websiteName": "Tên Website", "websiteNamePlaceholder": "Nhập tên website", "host": "Host/Domain", "hostPlaceholder": "redai.vn hoặc https://www.redai.vn", "hostDescription": "Nhập domain hoặc URL đầy đủ. <PERSON><PERSON> thống sẽ tự động chuẩn hóa."}, "filter": {"websiteName": "Tên Website", "host": "Host", "createdAt": "<PERSON><PERSON><PERSON>", "verify": "<PERSON>r<PERSON><PERSON> thái xác thực", "asc": "<PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "verified": "<PERSON><PERSON> xác thực", "unverified": "<PERSON><PERSON><PERSON> x<PERSON> thực"}}, "bankAccount": {"title": "<PERSON><PERSON><PERSON> hợp tài k<PERSON>n ngân hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài khoản ngân hàng đã tích hợp với hệ thống", "createTitle": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng để tích hợp với hệ thống", "bankName": "<PERSON><PERSON> h<PERSON>", "selectBank": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>n", "accountNumberHelp": "<PERSON><PERSON><PERSON><PERSON> số tài k<PERSON>n ngân hàng (6-20 ký tự số)", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "idNumber": "Số CMND/CCCD", "idNumberPlaceholder": "Nhập số CMND/CCCD", "idNumberHelp": "Nhập số CMND/CCCD đã đăng ký với ngân hàng", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "phoneNumberHelp": "Số điện thoại đã đăng ký với ngân hàng để nhận OTP", "storeName": "<PERSON><PERSON><PERSON> đi<PERSON> bán", "storeNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên điểm bán", "storeNameHelp": "<PERSON><PERSON><PERSON> cửa hàng/đi<PERSON><PERSON> bán hàng của bạn", "storeAddress": "Đ<PERSON>a chỉ điểm bán", "storeAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ điểm bán", "storeAddressHelp": "Đ<PERSON>a chỉ chi tiết của cửa hàng/điểm bán hàng", "create": "<PERSON><PERSON><PERSON> t<PERSON>", "bankInfo": "Thông tin ngân hàng", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "pending": "<PERSON><PERSON> xác thực", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "suspended": "<PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>"}, "virtualAccount": {"title": "<PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>"}, "createVA": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>o", "createSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n thành công", "createSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> tạo thành công", "createError": "<PERSON><PERSON><PERSON> tài k<PERSON>n thất bại", "createErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài khoản ngân hàng. <PERSON><PERSON> lòng kiểm tra lại thông tin.", "createCompleteSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n hoàn tất", "createCompleteSuccessDescription": "<PERSON><PERSON><PERSON> kho<PERSON>n ngân hàng đã được tạo và kích hoạt thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON>c xóa thành công", "deleteError": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "deleteErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài khoản ngân hàng. <PERSON><PERSON> lòng thử lại.", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a tài <PERSON>n", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa tài khoản ngân hàng này? Hành động này không thể hoàn tác.", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công", "bulkDeleteSuccessDescription": "<PERSON><PERSON> xóa {{count}} tài kho<PERSON>n ngân hàng", "bulkDeleteError": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "bulkDeleteErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể xóa các tài khoản ngân hàng. <PERSON><PERSON> lòng thử lại.", "bulkDeleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON>a nhiều tài k<PERSON>n", "bulkDeleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} tài khoản ngân hàng đã chọn? Hành động này không thể hoàn tác.", "selectAccountsToDelete": "<PERSON><PERSON> lòng chọn ít nhất một tài khoản để xóa", "createVASuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thành công", "createVASuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ảo đã đư<PERSON><PERSON> tạo thành công", "createVAError": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thất bại", "createVAErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài kho<PERSON>n ảo. <PERSON><PERSON> lòng thử lại.", "activateSuccess": "<PERSON><PERSON><PERSON> ho<PERSON>t thành công", "activateSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> kích hoạt thành công", "otpVerification": "<PERSON><PERSON><PERSON>", "otpVerificationDescription": "<PERSON><PERSON><PERSON><PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với {{bankName}}", "enterOtp": "<PERSON>hậ<PERSON> mã OTP", "otpSent": "Mã OTP đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "otpSentDescription": "<PERSON><PERSON> lòng kiểm tra tin nhắn SMS trên điện thoại của bạn", "otpSendError": "Gửi OTP thất bại", "otpSendErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể gửi mã OTP. <PERSON><PERSON> lòng thử lại sau.", "resendOtp": "Gửi lại mã OTP", "resendOtpCountdown": "G<PERSON>i lại mã sau {{countdown}} giây", "verify": "<PERSON><PERSON><PERSON> th<PERSON>c", "otpVerifySuccess": "<PERSON><PERSON><PERSON> thực thành công", "otpVerifySuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> kích hoạt thành công", "otpVerifyError": "<PERSON><PERSON><PERSON> thực thất bại", "otpVerifyErrorDescription": "Mã OTP không đúng hoặc đã hết hạn. <PERSON><PERSON> lòng thử lại.", "invalidOtpLength": "Mã OTP không đúng độ dài", "invalidOtpLengthDescription": "Mã OTP phải có {{length}} ký tự"}, "banking": {"bankAccount": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng", "bank": "<PERSON><PERSON> h<PERSON>", "connectionMethod": "<PERSON><PERSON><PERSON><PERSON> thức kết n<PERSON>i", "estimatedBalance": "Số dư tạm t<PERSON>h", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountNumber": "Số tài <PERSON>n", "connectApi": "Kết nối API Banking"}, "ai": {"title": "<PERSON><PERSON><PERSON><PERSON> lý nhà cung cấp AI", "description": "<PERSON><PERSON>", "id": "ID", "name": "<PERSON><PERSON><PERSON> nhà cung cấp", "baseUrl": "URL cơ sở", "apiVersion": "<PERSON><PERSON><PERSON> b<PERSON>", "models": "<PERSON><PERSON> hình hỗ trợ", "maxTokens": "<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà cung cấp AI", "editProvider": "<PERSON><PERSON><PERSON> nhà cung cấp AI", "providersList": "<PERSON><PERSON> s<PERSON>ch nh<PERSON> cung cấp <PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên nhà cung cấp AI", "baseUrlPlaceholder": "Nhập URL cơ sở API", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả", "apiVersionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> bản <PERSON>", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm nhà cung cấp...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "enable": "<PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa nhà cung cấp AI {{name}}?"}, "apiKeys": {"title": "<PERSON><PERSON><PERSON>n lý <PERSON>", "description": "<PERSON><PERSON>", "id": "ID", "apiKey": "API Key", "scope": "<PERSON><PERSON><PERSON><PERSON> truy cập", "environment": "<PERSON>ô<PERSON> trường", "expiredAt": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON>", "addNew": "Tạo API Key mới", "createNew": "Tạo API Key mới", "list": "<PERSON>h sách API Keys", "descriptionPlaceholder": "<PERSON>hập mô tả cho API Key", "selectDate": "<PERSON><PERSON><PERSON> ng<PERSON> hết hạn", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo mô tả...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "enable": "<PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa API Key này?", "createSuccess": "Đã tạo API Key mới với mô tả: {{description}}", "deleteSuccess": "Đã xóa API Key: {{apiKey}}", "toggleSuccess": "Đã {{action}} API Key: {{apiKey}}"}}}