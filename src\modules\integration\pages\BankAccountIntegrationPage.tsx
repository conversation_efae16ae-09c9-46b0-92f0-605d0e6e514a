import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
  Card,
  Table,
  ConfirmDeleteModal,
  Container,
  Chip,
  ActionMenu,
} from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';

// Import components và hooks
import BankAccountForm from '../components/BankAccountForm';
import OTPVerificationForm from '../components/OTPVerificationForm';
import {
  useBankAccounts,
  useDeleteBankAccount,
  useDeleteManyBankAccounts,
  useCreateVirtualAccount,
} from '../hooks/useBankAccount';
import {
  BankAccountDto,
  BankAccountQueryDto,
  BankAccountStatus,
  BankAccountSortBy,
  BankCode,
} from '../types/bank-account.types';

/**
 * Trang tích hợp tài khoản ngân hàng
 */
const BankAccountIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);

  // State management
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState<BankAccountDto | null>(null);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [currentStep, setCurrentStep] = useState<'form' | 'otp'>('form');
  const [pendingAccount, setPendingAccount] = useState<BankAccountDto | null>(null);

  // Form hooks
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();

  // API hooks
  const deleteBankAccountMutation = useDeleteBankAccount();
  const deleteManyBankAccountsMutation = useDeleteManyBankAccounts();
  const createVirtualAccountMutation = useCreateVirtualAccount();

  // Định nghĩa cột cho bảng
  const columns: TableColumn<BankAccountDto>[] = [
    {
      key: 'bankInfo',
      title: t('integration:bankAccount.bankInfo', 'Thông tin ngân hàng'),
      dataIndex: 'bankName',
      width: '25%',
      render: (_, record) => {
        return (
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-md bg-primary/10 flex items-center justify-center mr-3">
              <Icon name="credit-card" size="md" className="text-primary" />
            </div>
            <div>
              <Typography variant="subtitle1">{record.bankName}</Typography>
              <Typography variant="caption" className="text-gray-500">
                {record.accountNumber}
              </Typography>
            </div>
          </div>
        );
      },
    },
    {
      key: 'accountName',
      title: t('integration:bankAccount.accountName', 'Tên tài khoản'),
      dataIndex: 'accountName',
      width: '20%',
    },
    {
      key: 'storeName',
      title: t('integration:bankAccount.storeName', 'Tên điểm bán'),
      dataIndex: 'storeName',
      width: '20%',
    },
    {
      key: 'status',
      title: t('integration:bankAccount.status', 'Trạng thái'),
      dataIndex: 'status',
      width: '15%',
      render: (_, record) => {
        const statusConfig = {
          [BankAccountStatus.ACTIVE]: { color: 'success', label: 'Hoạt động' },
          [BankAccountStatus.PENDING]: { color: 'warning', label: 'Chờ xác thực' },
          [BankAccountStatus.INACTIVE]: { color: 'default', label: 'Không hoạt động' },
          [BankAccountStatus.SUSPENDED]: { color: 'error', label: 'Tạm khóa' },
          [BankAccountStatus.EXPIRED]: { color: 'error', label: 'Hết hạn' },
        };

        const config = statusConfig[record.status] || statusConfig[BankAccountStatus.INACTIVE];

        return (
          <Chip
            variant={config.color as 'success' | 'warning' | 'default' | 'danger'}
            size="sm"
          >
            {t(`integration:bankAccount.status.${record.status.toLowerCase()}`, config.label)}
          </Chip>
        );
      },
    },
    {
      key: 'virtualAccount',
      title: t('integration:bankAccount.virtualAccount', 'Tài khoản ảo'),
      dataIndex: 'hasVirtualAccount',
      width: '15%',
      render: (_, record) => (
        <div>
          {record.hasVirtualAccount ? (
            <div>
              <Chip variant="success" size="sm">Có</Chip>
              {record.virtualAccountNumber && (
                <Typography variant="caption" className="block text-gray-500 mt-1">
                  {record.virtualAccountNumber}
                </Typography>
              )}
            </div>
          ) : (
            <Chip variant="default" size="sm">Không</Chip>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions', 'Thao tác'),
      width: '5%',
      render: (_, record) => {
        const menuItems: ActionMenuItem[] = [
          {
            id: 'edit',
            label: t('common:edit', 'Chỉnh sửa'),
            icon: 'edit' as const,
            onClick: () => handleEdit(record),
          },
          {
            id: 'delete',
            label: t('common:delete', 'Xóa'),
            icon: 'trash' as const,
            onClick: () => handleDelete(record),
          },
        ];

        // Thêm action tạo VA chỉ cho OCB
        if (record.isOcb && !record.hasVirtualAccount && record.status === BankAccountStatus.ACTIVE) {
          menuItems.unshift({
            id: 'createVA',
            label: t('integration:bankAccount.createVA', 'Tạo tài khoản ảo'),
            icon: 'plus',
            onClick: () => handleCreateVirtualAccount(record),
          });
        }

        return <ActionMenu items={menuItems} />;
      },
    },
  ];

  // Filter options cho MenuIconBar
  const filterOptions = [
    {
      id: 'all',
      label: t('common:all', 'Tất cả'),
      icon: 'list',
      value: 'all',
    },
    {
      id: 'active',
      label: t('integration:bankAccount.status.active', 'Hoạt động'),
      icon: 'check',
      value: BankAccountStatus.ACTIVE,
    },
    {
      id: 'pending',
      label: t('integration:bankAccount.status.pending', 'Chờ xác thực'),
      icon: 'calendar',
      value: BankAccountStatus.PENDING,
    },
  ];

  // Tạo query params
  const createQueryParams = (filters: Record<string, unknown>): BankAccountQueryDto => ({
    page: (filters.page as number) || 1,
    limit: (filters.pageSize as number) || 10,
    search: (filters.search as string) || undefined,
    sortBy: (filters.sortField as BankAccountSortBy) || undefined,
    sortDirection: filters.sortOrder === 'ascend' ? SortDirection.ASC : SortDirection.DESC,
    status: (filters.status as BankAccountStatus) || undefined,
    bankCode: (filters.bankCode as BankCode) || undefined,
  });

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<BankAccountDto, BankAccountQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: bankAccountsData, isLoading: isLoadingBankAccounts } = useBankAccounts(dataTable.queryParams);

  // Active filters hook
  const activeFilters = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      all: t('common:all', 'Tất cả'),
      active: t('integration:bankAccount.status.active', 'Hoạt động'),
      pending: t('integration:bankAccount.status.pending', 'Chờ xác thực'),
    },
    t,
  });

  /**
   * Xử lý thêm mới
   */
  const handleAdd = () => {
    setCurrentStep('form');
    setPendingAccount(null);
    showForm();
  };

  /**
   * Xử lý chỉnh sửa
   */
  const handleEdit = (account: BankAccountDto) => {
    console.log('Edit account:', account);
    // TODO: Implement edit functionality
  };

  /**
   * Xử lý xóa
   */
  const handleDelete = (account: BankAccountDto) => {
    setAccountToDelete(account);
    setShowDeleteConfirm(true);
  };

  /**
   * Xác nhận xóa
   */
  const handleConfirmDelete = async () => {
    if (!accountToDelete) return;

    try {
      await deleteBankAccountMutation.mutateAsync(accountToDelete.id);

      NotificationUtil.success({
        title: t('integration:bankAccount.deleteSuccess', 'Xóa thành công'),
        message: t(
          'integration:bankAccount.deleteSuccessDescription',
          'Tài khoản ngân hàng đã được xóa thành công'
        ),
      });

      setShowDeleteConfirm(false);
      setAccountToDelete(null);
    } catch (error) {
      console.error('Error deleting bank account:', error);
      NotificationUtil.error({
        title: t('integration:bankAccount.deleteError', 'Xóa thất bại'),
        message: t(
          'integration:bankAccount.deleteErrorDescription',
          'Không thể xóa tài khoản ngân hàng. Vui lòng thử lại.'
        ),
      });
    }
  };

  /**
   * Xử lý xóa nhiều
   */
  const handleBulkDelete = async () => {
    try {
      await deleteManyBankAccountsMutation.mutateAsync(selectedRowKeys as string[]);

      NotificationUtil.success({
        title: t('integration:bankAccount.bulkDeleteSuccess', 'Xóa thành công'),
        message: t(
          'integration:bankAccount.bulkDeleteSuccessDescription',
          `Đã xóa ${selectedRowKeys.length} tài khoản ngân hàng`
        ),
      });

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error bulk deleting bank accounts:', error);
      NotificationUtil.error({
        title: t('integration:bankAccount.bulkDeleteError', 'Xóa thất bại'),
        message: t(
          'integration:bankAccount.bulkDeleteErrorDescription',
          'Không thể xóa các tài khoản ngân hàng. Vui lòng thử lại.'
        ),
      });
    }
  };

  /**
   * Xử lý tạo tài khoản ảo
   */
  const handleCreateVirtualAccount = async (account: BankAccountDto) => {
    try {
      await createVirtualAccountMutation.mutateAsync({ bankAccountId: account.id });

      NotificationUtil.success({
        title: t('integration:bankAccount.createVASuccess', 'Tạo tài khoản ảo thành công'),
        message: t(
          'integration:bankAccount.createVASuccessDescription',
          'Tài khoản ảo đã được tạo thành công'
        ),
      });
    } catch (error) {
      console.error('Error creating virtual account:', error);
      NotificationUtil.error({
        title: t('integration:bankAccount.createVAError', 'Tạo tài khoản ảo thất bại'),
        message: t(
          'integration:bankAccount.createVAErrorDescription',
          'Không thể tạo tài khoản ảo. Vui lòng thử lại.'
        ),
      });
    }
  };

  /**
   * Xử lý thành công tạo tài khoản
   */
  const handleCreateSuccess = (bankAccount: BankAccountDto, needsOTP: boolean) => {
    if (needsOTP) {
      setPendingAccount(bankAccount);
      setCurrentStep('otp');
    } else {
      hideForm();
      NotificationUtil.success({
        title: t('integration:bankAccount.createCompleteSuccess', 'Tạo tài khoản hoàn tất'),
        message: t(
          'integration:bankAccount.createCompleteSuccessDescription',
          'Tài khoản ngân hàng đã được tạo và kích hoạt thành công'
        ),
      });
    }
  };

  /**
   * Xử lý thành công xác thực OTP
   */
  const handleOTPSuccess = () => {
    hideForm();
    setPendingAccount(null);
    setCurrentStep('form');

    NotificationUtil.success({
      title: t('integration:bankAccount.activateSuccess', 'Kích hoạt thành công'),
      message: t(
        'integration:bankAccount.activateSuccessDescription',
        'Tài khoản ngân hàng đã được kích hoạt thành công'
      ),
    });
  };

  /**
   * Xử lý hủy form
   */
  const handleCancel = () => {
    hideForm();
    setPendingAccount(null);
    setCurrentStep('form');
  };

  return (
    <Container>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.history.back()}
            className="flex items-center space-x-2"
          >
            <Icon name="arrow-left" size="sm" />
            <span>{t('common:back', 'Quay lại')}</span>
          </Button>
          <Icon name="credit-card" size="lg" className="text-primary" />
          <Typography variant="h4">
            {t('integration:bankAccount.title', 'Tích hợp tài khoản ngân hàng')}
          </Typography>
        </div>
        <Typography variant="body1" color="muted" className="mt-2">
          {t('integration:bankAccount.description', 'Quản lý các tài khoản ngân hàng đã tích hợp với hệ thống')}
        </Typography>
      </div>

      <div className="space-y-4">
        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={handleAdd}
          items={dataTable.menuItems}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
          additionalIcons={[
            {
              icon: 'trash',
              tooltip: t('common:bulkDelete', 'Xóa nhiều'),
              variant: 'primary',
              onClick: () => {
                if (selectedRowKeys.length > 0) {
                  setShowBulkDeleteConfirm(true);
                } else {
                  NotificationUtil.info({
                    title: t('common:info', 'Thông báo'),
                    message: t('integration:bankAccount.selectAccountsToDelete', 'Vui lòng chọn ít nhất một tài khoản để xóa'),
                  });
                }
              },
              condition: selectedRowKeys.length > 0,
            },
          ]}
          isLoading={isLoadingBankAccounts}
        />

        {/* Active Filters */}
        {(dataTable.tableData.searchTerm || dataTable.filter.selectedValue !== 'all') && (
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={activeFilters.handleClearSearch}
            onClearAll={activeFilters.handleClearAll}
          />
        )}

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<BankAccountDto>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={bankAccountsData?.result?.items || []}
            rowKey="id"
            loading={isLoadingBankAccounts}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: bankAccountsData?.result?.meta?.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: bankAccountsData?.result?.meta?.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* SlideInForm */}
      <SlideInForm isVisible={isFormVisible}>
        {currentStep === 'form' ? (
          <BankAccountForm
            onSuccess={handleCreateSuccess}
            onCancel={handleCancel}
          />
        ) : (
          pendingAccount && (
            <OTPVerificationForm
              bankAccount={pendingAccount}
              onSuccess={handleOTPSuccess}
              onCancel={handleCancel}
            />
          )
        )}
      </SlideInForm>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onConfirm={handleConfirmDelete}
        onClose={() => {
          setShowDeleteConfirm(false);
          setAccountToDelete(null);
        }}
        title={t('integration:bankAccount.deleteConfirmTitle', 'Xác nhận xóa tài khoản')}
        message={t(
          'integration:bankAccount.deleteConfirmMessage',
          'Bạn có chắc chắn muốn xóa tài khoản ngân hàng này? Hành động này không thể hoàn tác.'
        )}
        isSubmitting={deleteBankAccountMutation.isPending}
      />

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onConfirm={handleBulkDelete}
        onClose={() => {
          setShowBulkDeleteConfirm(false);
        }}
        title={t('integration:bankAccount.bulkDeleteConfirmTitle', 'Xác nhận xóa nhiều tài khoản')}
        message={t(
          'integration:bankAccount.bulkDeleteConfirmMessage',
          `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} tài khoản ngân hàng đã chọn? Hành động này không thể hoàn tác.`
        )}
        isSubmitting={deleteManyBankAccountsMutation.isPending}
      />
    </Container>
  );
};

export default BankAccountIntegrationPage;
