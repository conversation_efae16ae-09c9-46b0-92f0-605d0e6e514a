import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';

/**
 * Provider Model Schemas
 */

export const providerModelSchema = z.object({
  name: z
    .string()
    .min(1, 'admin:integration.providerModel.validation.name.required')
    .max(255, 'admin:integration.providerModel.validation.name.maxLength'),
  
  type: z
    .nativeEnum(TypeProviderEnum, {
      errorMap: () => ({ message: 'admin:integration.providerModel.validation.type.required' })
    }),
  
  apiKey: z
    .string()
    .min(1, 'admin:integration.providerModel.validation.apiKey.required')
    .min(10, 'admin:integration.providerModel.validation.apiKey.minLength'),
});

export const updateProviderModelSchema = z.object({
  name: z
    .string()
    .min(1, 'admin:integration.providerModel.validation.name.required')
    .max(255, 'admin:integration.providerModel.validation.name.maxLength')
    .optional(),
  
  apiKey: z
    .string()
    .min(1, 'admin:integration.providerModel.validation.apiKey.required')
    .min(10, 'admin:integration.providerModel.validation.apiKey.minLength')
    .optional(),
});

export const providerModelQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  type: z.nativeEnum(TypeProviderEnum).optional(),
});

export type ProviderModelFormData = z.infer<typeof providerModelSchema>;
export type UpdateProviderModelFormData = z.infer<typeof updateProviderModelSchema>;
export type ProviderModelQueryFormData = z.infer<typeof providerModelQuerySchema>;
