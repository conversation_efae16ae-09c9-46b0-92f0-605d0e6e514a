import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import UserIntegrationManagementPage from '../pages/UserIntegrationManagementPage';
import { FacebookIntegrationPage, WebsiteIntegrationPage } from '../pages';
import EmailServerManagementPage from '../pages/EmailServerManagementPage';
import ProviderModelManagementPage from '../pages/ProviderModelManagementPage';

const t = (key: string, defaultValue?: string) => i18n.t(key, { defaultValue });

const integrationRoutes: RouteObject[] = [
  // Trang chính - Hiển thị tất cả các mạng xã hội
  {
    path: '/integrations',
    element: (
      <MainLayout title={t('integration.social.title')}>
        <Suspense fallback={<Loading />}>
          <UserIntegrationManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/email',
    element: (
      <MainLayout title={t('integration.accounts.title')}>
        <Suspense fallback={<Loading />}>
          <EmailServerManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/facebook',
    element: (
      <MainLayout title={t('integration.facebook.title')}>
        <Suspense fallback={<Loading />}>
          <FacebookIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/website',
    element: (
      <MainLayout title={t('integration.website.title')}>
        <Suspense fallback={<Loading />}>
          <WebsiteIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/user/provider-model',
    element: (
      <MainLayout title={t('integration.providerModel.title')}>
        <Suspense fallback={<Loading />}>
          <ProviderModelManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default integrationRoutes;
