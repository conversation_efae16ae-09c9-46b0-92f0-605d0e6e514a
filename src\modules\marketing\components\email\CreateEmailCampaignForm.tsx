import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Mail } from 'lucide-react';
import {
  Button,
  Input,
  Alert,
  FormItem,
  Typography,
  Card,
  Select,
  DateTimePicker
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks';
// import { createEmailCampaignSchema, type CreateEmailCampaignFormData } from '../../schemas/email.schema';
import { useCreateEmailCampaign } from '../../hooks/email/useEmailCampaigns';
import { useEmailTemplates } from '../../hooks/email/useEmailTemplates';
import type { CreateEmailCampaignDto } from '../../types/email.types';
import { EmailTemplateStatus } from '../../types/email.types';

interface CreateEmailCampaignFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo Email Campaign theo quy tắc RedAI
 */
export function CreateEmailCampaignForm({ onSuccess, onCancel }: CreateEmailCampaignFormProps) {
  const { t } = useTranslation(['marketing', 'common']);
  const createCampaign = useCreateEmailCampaign();

  // Sử dụng useFormErrors theo quy tắc RedAI
  const { setFormErrors } = useFormErrors<CreateEmailCampaignDto>();

  // State cho form data
  const [formData, setFormData] = useState<CreateEmailCampaignDto>({
    name: '',
    subject: '',
    templateId: '',
    audienceIds: [],
    segmentIds: [],
    scheduledAt: undefined,
  });

  // Lấy danh sách templates
  const { data: templatesData, isLoading: templatesLoading } = useEmailTemplates({
    limit: 100,
    status: EmailTemplateStatus.ACTIVE
  });

  // Handle form submission
  const handleSubmit = async (data: CreateEmailCampaignDto) => {
    // Validate form data
    const errors: Partial<Record<keyof CreateEmailCampaignDto, string>> = {};

    if (!data.name.trim()) {
      errors.name = t('marketing:email.campaigns.form.validation.nameRequired', 'Tên chiến dịch là bắt buộc');
    }

    if (!data.subject.trim()) {
      errors.subject = t('marketing:email.campaigns.form.validation.subjectRequired', 'Tiêu đề email là bắt buộc');
    }

    if (!data.templateId) {
      errors.templateId = t('marketing:email.campaigns.form.validation.templateRequired', 'Vui lòng chọn template');
    }

    if ((data.audienceIds?.length || 0) === 0 && (data.segmentIds?.length || 0) === 0) {
      errors.audienceIds = t('marketing:email.campaigns.form.validation.audienceRequired', 'Vui lòng chọn ít nhất một audience hoặc segment');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      await createCampaign.mutateAsync(data);
      onSuccess?.();
    } catch {
      // Error được handle trong hook
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof CreateEmailCampaignDto, value: string | string[] | Date | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Template options
  const templateOptions = templatesData?.items.map(template => ({
    value: template.id,
    label: `${template.name} - ${template.subject}`,
  })) || [];

  // Mock audience options (sẽ được thay thế bằng API thực)
  const audienceOptions = [
    { value: 'audience-1', label: 'Khách hàng VIP' },
    { value: 'audience-2', label: 'Khách hàng mới' },
    { value: 'audience-3', label: 'Khách hàng tiềm năng' },
  ];

  // Mock segment options (sẽ được thay thế bằng API thực)
  const segmentOptions = [
    { value: 'segment-1', label: 'Đã mua trong 30 ngày' },
    { value: 'segment-2', label: 'Chưa mua lần nào' },
    { value: 'segment-3', label: 'Quan tâm sản phẩm A' },
  ];

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Instructions */}
      <Alert
        type="info"
        message={t('marketing:email.campaigns.form.instructions.title', 'Hướng dẫn tạo Email Campaign')}
        description={t('marketing:email.campaigns.form.instructions.description', 'Tạo chiến dịch email marketing để gửi đến audience hoặc segment cụ thể. Bạn có thể lên lịch gửi hoặc gửi ngay lập tức.')}
      />

      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(formData); }}>
        <div className="space-y-6">
          {/* Basic Info */}
          <Card title={t('marketing:email.campaigns.form.basicInfo.title', 'Thông tin chiến dịch')}>
            <div className="space-y-4">
              <FormItem
                label={t('marketing:email.campaigns.form.name.label', 'Tên chiến dịch')}
                name="name"
                required
              >
                <Input
                  placeholder={t('marketing:email.campaigns.form.name.placeholder', 'Ví dụ: Khuyến mãi Black Friday 2024')}
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  leftIcon={<Mail className="h-4 w-4" />}
                  className="w-full"
                />
              </FormItem>

              <FormItem
                label={t('marketing:email.campaigns.form.subject.label', 'Tiêu đề email')}
                name="subject"
                required
              >
                <Input
                  placeholder={t('marketing:email.campaigns.form.subject.placeholder', 'Ví dụ: 🔥 Giảm giá lên đến 70% - Chỉ 3 ngày!')}
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  className="w-full"
                />
              </FormItem>

              <FormItem
                label={t('marketing:email.campaigns.form.template.label', 'Email Template')}
                name="templateId"
                required
              >
                <div className="relative z-50">
                  <Select
                    value={formData.templateId}
                    onChange={(value) => handleInputChange('templateId', value as string)}
                    options={templateOptions}
                    placeholder={t('marketing:email.campaigns.form.template.placeholder', 'Chọn template email')}
                    loading={templatesLoading}
                    fullWidth
                  />
                </div>
              </FormItem>
            </div>
          </Card>

          {/* Target Audience */}
          <Card
            title={t('marketing:email.campaigns.form.audience.title', 'Đối tượng nhận email')}
            subtitle={t('marketing:email.campaigns.form.audience.description', 'Chọn audience hoặc segment để gửi email')}
          >
            <div className="space-y-4">
              <FormItem
                label={t('marketing:email.campaigns.form.audiences.label', 'Audiences')}
                name="audienceIds"
              >
                <div className="relative z-[100]">
                  <Select
                    value={formData.audienceIds}
                    onChange={(value) => handleInputChange('audienceIds', Array.isArray(value) ? value as string[] : [value as string])}
                    options={audienceOptions}
                    placeholder={t('marketing:email.campaigns.form.audiences.placeholder', 'Chọn audiences')}
                    multiple
                    fullWidth
                  />
                </div>
              </FormItem>

              <FormItem
                label={t('marketing:email.campaigns.form.segments.label', 'Segments')}
                name="segmentIds"
              >
                <div className="relative z-[90]">
                  <Select
                    value={formData.segmentIds}
                    onChange={(value) => handleInputChange('segmentIds', Array.isArray(value) ? value as string[] : [value as string])}
                    options={segmentOptions}
                    placeholder={t('marketing:email.campaigns.form.segments.placeholder', 'Chọn segments')}
                    multiple
                    fullWidth
                  />
                </div>
              </FormItem>
            </div>

            {(formData.audienceIds?.length === 0 && formData.segmentIds?.length === 0) && (
              <div className="mt-4">
                <Alert
                  type="warning"
                  message={t('marketing:email.campaigns.form.audience.warning', 'Chưa chọn đối tượng nhận email')}
                  description={t('marketing:email.campaigns.form.audience.warningDescription', 'Vui lòng chọn ít nhất một audience hoặc segment để gửi email.')}
                />
              </div>
            )}
          </Card>

          {/* Schedule */}
          <Card title={t('marketing:email.campaigns.form.schedule.title', 'Lên lịch gửi')}>
            <div className="space-y-4">
              <FormItem
                label={t('marketing:email.campaigns.form.scheduledAt.label', 'Thời gian gửi (tùy chọn)')}
                name="scheduledAt"
              >
                <div className="relative z-20">
                  <DateTimePicker
                    value={formData.scheduledAt ? new Date(formData.scheduledAt) : null}
                    onChange={(dateTime) => handleInputChange('scheduledAt', dateTime || undefined)}
                    placeholder="Chọn ngày và giờ"
                    minDate={new Date()}
                    fullWidth
                    size="md"
                    format="dd/MM/yyyy HH:mm"
                    timeFormat="24h"
                  />
                </div>
              </FormItem>
            </div>

            <Typography variant="caption" color="muted">
              {t('marketing:email.campaigns.form.schedule.note', 'Để trống để gửi ngay lập tức sau khi tạo chiến dịch')}
            </Typography>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onCancel?.()}
              disabled={createCampaign.isPending}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              disabled={createCampaign.isPending}
            >
              {createCampaign.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('marketing:email.campaigns.form.submitButton', 'Tạo chiến dịch')}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

export default CreateEmailCampaignForm;
