import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
  IconCard,
  Icon,
} from '@/shared/components/common';
import { SegmentCondition, SegmentGroup, ConditionOperator, Segment } from '../../types/segment.types';
import { useFormErrors } from '@/shared/hooks/form';

// Operators cho điều kiện
const CONDITION_OPERATORS = [
  { value: ConditionOperator.EQUALS, label: 'Là' },
  { value: ConditionOperator.NOT_EQUALS, label: 'Không phải là' },
  { value: ConditionOperator.CONTAINS, label: 'Chứa' },
  { value: ConditionOperator.NOT_CONTAINS, label: 'Không chứa' },
  { value: ConditionOperator.GREATER_THAN, label: 'Bắt đầu với' },
  { value: ConditionOperator.LESS_THAN, label: '<PERSON><PERSON> bất kì giá trị nào' },
  { value: ConditionOperator.BETWEEN, label: '<PERSON>hông có giá trị' },
];

// Available fields cho điều kiện
const AVAILABLE_FIELDS = [
  { value: 'email', label: 'Email' },
  { value: 'name', label: 'Tên' },
  { value: 'phone', label: 'Số điện thoại' },
  { value: 'age', label: 'Tuổi' },
  { value: 'city', label: 'Thành phố' },
  { value: 'country', label: 'Quốc gia' },
  { value: 'created_at', label: 'Ngày tạo' },
  { value: 'last_activity', label: 'Hoạt động cuối' },
];

export interface SegmentFormValues {
  name: string;
  description?: string;
  groups: SegmentGroup[];
}

interface SegmentFormProps {
  initialData?: Segment | null;
  onSubmit: (values: SegmentFormValues) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa phân đoạn
 */
const SegmentForm: React.FC<SegmentFormProps> = ({ initialData, onSubmit, onCancel }) => {
  const { t } = useTranslation('marketing');
  const { setFormErrors } = useFormErrors<SegmentFormValues>();

  // State cho form data
  const [formData, setFormData] = useState<SegmentFormValues>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    groups: initialData?.groups || [
      {
        id: `group-${Date.now()}`,
        conditions: [
          {
            id: `condition-${Date.now()}`,
            field: '',
            operator: ConditionOperator.EQUALS,
            value: '',
          },
        ],
        logicalOperator: 'AND',
      },
    ],
  });

  // Thêm điều kiện mới vào group
  const addCondition = useCallback((groupId: string) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.map(group =>
        group.id === groupId
          ? {
              ...group,
              conditions: [
                ...group.conditions,
                {
                  id: `condition-${Date.now()}`,
                  field: '',
                  operator: ConditionOperator.EQUALS,
                  value: '',
                },
              ],
            }
          : group
      ),
    }));
  }, []);

  // Xóa điều kiện
  const removeCondition = useCallback((groupId: string, conditionId: string) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.map(group =>
        group.id === groupId
          ? {
              ...group,
              conditions: group.conditions.filter(condition => condition.id !== conditionId),
            }
          : group
      ),
    }));
  }, []);

  // Cập nhật điều kiện
  const updateCondition = useCallback((groupId: string, conditionId: string, updates: Partial<SegmentCondition>) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.map(group =>
        group.id === groupId
          ? {
              ...group,
              conditions: group.conditions.map(condition =>
                condition.id === conditionId ? { ...condition, ...updates } : condition
              ),
            }
          : group
      ),
    }));
  }, []);

  // Thêm nhóm điều kiện mới
  const addGroup = useCallback(() => {
    setFormData(prev => ({
      ...prev,
      groups: [
        ...prev.groups,
        {
          id: `group-${Date.now()}`,
          conditions: [
            {
              id: `condition-${Date.now()}`,
              field: '',
              operator: ConditionOperator.EQUALS,
              value: '',
            },
          ],
          logicalOperator: 'AND',
        },
      ],
    }));
  }, []);

  // Xóa nhóm điều kiện
  const removeGroup = useCallback((groupId: string) => {
    setFormData(prev => ({
      ...prev,
      groups: prev.groups.filter(group => group.id !== groupId),
    }));
  }, []);

  // Xử lý submit
  const handleSubmit = useCallback((event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Tên phân đoạn là bắt buộc';
    }

    // Validate groups
    if (formData.groups.length === 0) {
      errors.groups = 'Phải có ít nhất một nhóm điều kiện';
      setFormErrors(errors);
      return;
    }

    // Validate conditions in each group
    for (const group of formData.groups) {
      if (group.conditions.length === 0) {
        errors.groups = 'Mỗi nhóm phải có ít nhất một điều kiện';
        setFormErrors(errors);
        return;
      }

      for (const condition of group.conditions) {
        if (!condition.field || !condition.operator) {
          errors.groups = 'Tất cả điều kiện phải được cấu hình đầy đủ';
          setFormErrors(errors);
          return;
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    onSubmit(formData);
  }, [formData, onSubmit, setFormErrors]);

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {initialData ? t('segment.edit', 'Chỉnh sửa phân đoạn') : t('segment.addNew', 'Thêm phân đoạn mới')}
      </Typography>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Tên Segment */}
        <FormItem label="Tên Segment" name="name" required>
          <Input
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Tên Segment"
            fullWidth
          />
        </FormItem>

        {/* Mô tả */}
        <FormItem label="Mô tả" name="description">
          <Input
            value={formData.description || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Mô tả phân đoạn"
            fullWidth
          />
        </FormItem>

        {/* Điều kiện */}
        <div className="space-y-4">
          <Typography variant="h6">Điều kiện</Typography>

          {formData.groups.map((group, groupIndex) => (
            <div key={group.id} className="bg-card rounded-lg p-4 space-y-4">
              {/* Header nhóm điều kiện */}
              <div className="flex items-center justify-between">
                <Typography variant="body2" className="text-muted-foreground">
                  {groupIndex > 0 && (
                    <span className="mr-2 px-2 py-1 bg-primary/10 text-primary rounded text-xs font-medium">
                      HOẶC
                    </span>
                  )}
                  Kích hoạt khi đáp ứng các điều kiện sau
                </Typography>

                {formData.groups.length > 1 && (
                  <IconCard
                    icon="x"
                    size="sm"
                    variant="default"
                    onClick={() => removeGroup(group.id)}
                    className="text-destructive hover:text-destructive/80"
                  />
                )}
              </div>

              {/* Điều kiện trong nhóm */}
              <div className="space-y-3">
                {group.conditions.map((condition, conditionIndex) => (
                  <div key={condition.id} className="grid grid-cols-12 gap-3 items-center">
                    {/* Cột VÀ - cố định width */}
                    <div className="col-span-1 flex justify-center">
                      {conditionIndex > 0 && (
                        <Typography variant="body2" className="text-muted-foreground font-medium">
                          VÀ
                        </Typography>
                      )}
                    </div>

                    {/* Chọn trường */}
                    <div className="col-span-3">
                      <Select
                        value={condition.field}
                        onChange={(value) => updateCondition(group.id, condition.id, { field: value as string })}
                        options={AVAILABLE_FIELDS}
                        placeholder="Chọn Trường"
                        fullWidth
                      />
                    </div>

                    {/* Chọn toán tử */}
                    <div className="col-span-3">
                      <Select
                        value={condition.operator}
                        onChange={(value) => updateCondition(group.id, condition.id, { operator: value as ConditionOperator })}
                        options={CONDITION_OPERATORS}
                        placeholder="Tìm kiếm"
                        fullWidth
                      />
                    </div>

                    {/* Nhập giá trị */}
                    <div className="col-span-4">
                      <Input
                        value={condition.value as string}
                        onChange={(e) => updateCondition(group.id, condition.id, { value: e.target.value })}
                        placeholder="Nhập giá trị"
                        fullWidth
                      />
                    </div>

                    {/* Xóa điều kiện */}
                    <div className="col-span-1 flex justify-center">
                      {group.conditions.length > 1 && (
                        <IconCard
                          icon="trash"
                          size="sm"
                          variant="default"
                          onClick={() => removeCondition(group.id, condition.id)}
                          className="text-destructive hover:text-destructive/80"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Thêm điều kiện */}
              <div className="flex justify-start">
                <Button
                  type="button"
                  variant="primary"
                  size="sm"
                  onClick={() => addCondition(group.id)}
                  leftIcon={<Icon name="plus" size="sm" />}
                >
                  Thêm điều kiện
                </Button>
              </div>
            </div>
          ))}

          {/* Thêm nhóm điều kiện */}
          <div className="flex justify-start">
            <Button
              type="button"
              variant="primary"
              size="sm"
              onClick={addGroup}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              Thêm nhóm điều kiện
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-border">
          <Button type="button" variant="outline" onClick={onCancel}>
            Đóng
          </Button>
          <Button type="submit" variant="primary">
            Lưu
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default SegmentForm;
