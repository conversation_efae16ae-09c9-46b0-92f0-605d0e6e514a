/**
 * Email Templates Adapter Hooks - Bridge between backend template-email API and frontend expectations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EmailTemplateAdapterService } from '../../services/email-template-adapter.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { EmailTemplateQueryDto, CreateEmailTemplateDto, UpdateEmailTemplateDto } from '../../types/email.types';

/**
 * Query keys for Email templates using adapter
 */
export const EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS = {
  all: ['email', 'templates', 'adapter'] as const,
  lists: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'list'] as const,
  list: (query: EmailTemplateQueryDto) => {
    // Tạo stable query key bằng cách serialize các giá trị quan trọng
    const stableQuery = {
      page: query.page || 1,
      limit: query.limit || 10,
      search: query.search || '',
      sortBy: query.sortBy || '',
      sortDirection: query.sortDirection || '',
      status: query.status || '',
    };
    return [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists(), stableQuery] as const;
  },
  details: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.details(), id] as const,
  statistics: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'statistics'] as const,
};

/**
 * Hook to get email templates using adapter
 */
export function useEmailTemplatesAdapter(query?: EmailTemplateQueryDto) {
  const queryKey = EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.list(query || {});

  console.log('🔍 [useEmailTemplatesAdapter] Called with query:', query);
  console.log('🔍 [useEmailTemplatesAdapter] Generated queryKey:', JSON.stringify(queryKey));

  return useQuery({
    queryKey,
    queryFn: () => {
      console.log('🚀 [API CALL] EmailTemplateAdapterService.getEmailTemplates');
      return EmailTemplateAdapterService.getEmailTemplates(query);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook to get email template by ID using adapter
 */
export function useEmailTemplateAdapter(id: string) {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id),
    queryFn: () => EmailTemplateAdapterService.getEmailTemplate(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

/**
 * Hook to create email template using adapter
 */
export function useCreateEmailTemplateAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmailTemplateDto) =>
      EmailTemplateAdapterService.createEmailTemplate({
        name: data.name,
        subject: data.subject,
        htmlContent: data.htmlContent,
        tags: data.tags,
        variables: data.variables,
      }),
    onSuccess: (result) => {
      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Tạo template thành công!',
        title: `Template "${result.name}" đã được tạo`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tạo template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to update email template using adapter
 */
export function useUpdateEmailTemplateAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmailTemplateDto }) =>
      EmailTemplateAdapterService.updateEmailTemplate(id, {
        name: data.name,
        subject: data.subject,
        htmlContent: data.htmlContent,
        tags: data.tags,
        variables: data.variables,
      }),
    onSuccess: (result, { id }) => {
      // Update cache for template detail
      queryClient.setQueryData(EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id), result);

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Cập nhật template thành công!',
        title: `Template "${result.name}" đã được cập nhật`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to delete email template using adapter
 */
export function useDeleteEmailTemplateAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => EmailTemplateAdapterService.deleteEmailTemplate(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id) });

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Xóa template thành công!',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to get email template statistics using adapter
 */
export function useEmailTemplateStatisticsAdapter() {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics(),
    queryFn: () => EmailTemplateAdapterService.getEmailTemplateStatistics(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook for email template management using adapter - combines all operations
 */
export function useEmailTemplateManagementAdapter() {
  const createTemplate = useCreateEmailTemplateAdapter();
  const updateTemplate = useUpdateEmailTemplateAdapter();
  const deleteTemplate = useDeleteEmailTemplateAdapter();

  return {
    createTemplate,
    updateTemplate,
    deleteTemplate,
    isLoading: createTemplate.isPending || updateTemplate.isPending || deleteTemplate.isPending,
  };
}
