/**
 * Hooks for template email API using TanStack Query - Layer 3: React Query Hooks
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { TemplateEmailBusinessService } from '../services/template-email-business.service';
import { NotificationUtil } from '@/shared/utils/notification';
import {
  CreateTemplateEmailRequest,
  TemplateEmailQueryParams,
  UpdateTemplateEmailRequest,
} from '../types/template-email.types';

/**
 * Query keys for template email API - following RedAI pattern
 */
export const TEMPLATE_EMAIL_QUERY_KEYS = {
  all: ['marketing', 'template-emails'] as const,
  lists: () => [...TEMPLATE_EMAIL_QUERY_KEYS.all, 'list'] as const,
  list: (params: TemplateEmailQueryParams) =>
    [...TEMPLATE_EMAIL_QUERY_KEYS.lists(), params] as const,
  details: () => [...TEMPLATE_EMAIL_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...TEMPLATE_EMAIL_QUERY_KEYS.details(), id] as const,
  statistics: () => [...TEMPLATE_EMAIL_QUERY_KEYS.all, 'statistics'] as const,
};

/**
 * Hook to get template emails with pagination and filtering
 */
export const useTemplateEmails = (params: TemplateEmailQueryParams = {}) => {
  return useQuery({
    queryKey: TEMPLATE_EMAIL_QUERY_KEYS.list(params),
    queryFn: () => TemplateEmailBusinessService.getTemplateEmails(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

/**
 * Hook to get template email by ID
 */
export const useTemplateEmail = (id: number) => {
  return useQuery({
    queryKey: TEMPLATE_EMAIL_QUERY_KEYS.detail(id),
    queryFn: () => TemplateEmailBusinessService.getTemplateEmailById(id),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

/**
 * Hook to create template email
 */
export const useCreateTemplateEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTemplateEmailRequest) =>
      TemplateEmailBusinessService.createTemplateEmail(data),
    onSuccess: (result) => {
      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Tạo template thành công!',
        title: `Template "${result.name}" đã được tạo`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tạo template thất bại',
        title: error.message,
      });
    },
  });
};

/**
 * Hook to update template email
 */
export const useUpdateTemplateEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTemplateEmailRequest }) =>
      TemplateEmailBusinessService.updateTemplateEmail(id, data),
    onSuccess: (result, { id }) => {
      // Update cache for template detail
      queryClient.setQueryData(TEMPLATE_EMAIL_QUERY_KEYS.detail(id), result);

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Cập nhật template thành công!',
        title: `Template "${result.name}" đã được cập nhật`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật template thất bại',
        title: error.message,
      });
    },
  });
};

/**
 * Hook to delete template email
 */
export const useDeleteTemplateEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => TemplateEmailBusinessService.deleteTemplateEmail(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.detail(id) });

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TEMPLATE_EMAIL_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Xóa template thành công!',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa template thất bại',
        title: error.message,
      });
    },
  });
};

/**
 * Hook to get template email statistics
 */
export const useTemplateEmailStatistics = () => {
  return useQuery({
    queryKey: TEMPLATE_EMAIL_QUERY_KEYS.statistics(),
    queryFn: () => TemplateEmailBusinessService.getTemplateStatistics(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

/**
 * Hook for template email management - combines all operations
 */
export const useTemplateEmailManagement = () => {
  const createTemplate = useCreateTemplateEmail();
  const updateTemplate = useUpdateTemplateEmail();
  const deleteTemplate = useDeleteTemplateEmail();

  return {
    createTemplate,
    updateTemplate,
    deleteTemplate,
    isLoading: createTemplate.isPending || updateTemplate.isPending || deleteTemplate.isPending,
  };
};