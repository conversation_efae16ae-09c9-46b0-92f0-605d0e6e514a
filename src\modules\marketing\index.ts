/**
 * Marketing Module
 *
 * Module quản lý marketing, bao gồm:
 * - Quản lý Audience
 * - Quản lý Segment
 * - Quản lý Chiến dịch
 */

// Export components
export * from './components';

// Export pages
export * from './pages';

// Export hooks
export * from './hooks';

// Export types
export * from './types';

// Export schemas
export * from './schemas';

// Export services
export * from './services';

// Export routes
export { default as marketingRoutes } from './marketingRoutes';

// Export pages
export { MarketingDashboardPage } from './pages/MarketingDashboardPage';
export { ZaloOverviewPage } from './pages/zalo/ZaloOverviewPage';
export { ZaloAccountsPage } from './pages/zalo/ZaloAccountsPage';
export { ZaloFollowersPage } from './pages/zalo/ZaloFollowersPage';
export { ZaloZnsPage } from './pages/zalo/ZaloZnsPage';
export { EmailOverviewPage } from './pages/email/EmailOverviewPage';
export { EmailTemplatesPage } from './pages/email/EmailTemplatesPage';
export { EmailCampaignsPage } from './pages/email/EmailCampaignsPage';

// Export components
export { ConnectZaloAccountForm } from './components/zalo/ConnectZaloAccountForm';
export { CreateZnsTemplateForm } from './components/zalo/CreateZnsTemplateForm';
export { CreateEmailTemplateForm } from './components/email/CreateEmailTemplateForm';
export { CreateEmailCampaignForm } from './components/email/CreateEmailCampaignForm';
export { MarketingViewHeader } from './components/common/MarketingViewHeader';

// Export hooks
export { useZaloAccounts, useZaloAccount, useZaloAccountManagement } from './hooks/zalo/useZaloAccounts';
export { useZaloFollowers, useZaloFollowerManagement } from './hooks/zalo/useZaloFollowers';
export { useEmailTemplates, useEmailTemplateManagement, useCreateEmailTemplate } from './hooks/email/useEmailTemplates';
export { useEmailCampaigns, useEmailCampaignManagement, useCreateEmailCampaign } from './hooks/email/useEmailCampaigns';
export {
  useTemplateEmails,
  useTemplateEmail,
  useCreateTemplateEmail,
  useUpdateTemplateEmail,
  useDeleteTemplateEmail,
  useTemplateEmailStatistics,
  useTemplateEmailManagement,
  TEMPLATE_EMAIL_QUERY_KEYS
} from './hooks/useTemplateEmailQuery';
export {
  useEmailTemplatesAdapter,
  useEmailTemplateAdapter,
  useCreateEmailTemplateAdapter,
  useUpdateEmailTemplateAdapter,
  useDeleteEmailTemplateAdapter,
  useEmailTemplateStatisticsAdapter,
  useEmailTemplateManagementAdapter,
  EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS
} from './hooks/email/useEmailTemplatesAdapter';

// Export services
export { ZaloService } from './services/zalo.service';
export { EmailService } from './services/email.service';
export { TemplateEmailService } from './services/template-email.service';
export { TemplateEmailBusinessService } from './services/template-email-business.service';
export { EmailTemplateAdapterService } from './services/email-template-adapter.service';

// Export types
export type * from './types/zalo.types';
export type * from './types/email.types';
export type * from './types/template-email.types';

// Export schemas
export * from './schemas/zalo.schema';
export * from './schemas/email.schema';

// TikTok Ads
export * from './hooks/tiktok-ads';
export * from './services/tiktok-ads.service';
export { default as TikTokAdsOverviewPage } from './pages/tiktok-ads/TikTokAdsOverviewPage';
export { default as TikTokAdsAccountsPage } from './pages/tiktok-ads/TikTokAdsAccountsPage';
export { default as TikTokAdsCampaignsPage } from './pages/tiktok-ads/TikTokAdsCampaignsPage';
export { default as TikTokAdsCreativesPage } from './pages/tiktok-ads/TikTokAdsCreativesPage';
export { default as TikTokAdsAudiencesPage } from './pages/tiktok-ads/TikTokAdsAudiencesPage';
export { default as TikTokAdsReportsPage } from './pages/tiktok-ads/TikTokAdsReportsPage';
export { default as TikTokAdsSettingsPage } from './pages/tiktok-ads/TikTokAdsSettingsPage';
