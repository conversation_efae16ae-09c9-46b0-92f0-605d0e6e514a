import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';

// Lazy load pages
const MarketingPage = lazy(() => import('./pages/MarketingPage'));
const AudiencePage = lazy(() => import('./pages/AudiencePage'));
const SegmentPage = lazy(() => import('./pages/SegmentPage'));
const CampaignPage = lazy(() => import('./pages/CampaignPage'));
const CustomFieldsPage = lazy(() => import('./pages/CustomFieldsPage'));
const TagManagementPage = lazy(() => import('./pages/TagManagementPage'));
const ReportsPage = lazy(() => import('./pages/ReportsPage'));
const TemplateEmailPage = lazy(() => import('./pages/TemplateEmailPage'));
const SmsMarketingPage = lazy(() => import('./pages/SmsMarketingPage'));
const GoogleAdsPage = lazy(() => import('./pages/GoogleAdsPage'));
const GoogleAdsOverviewPage = lazy(() => import('./pages/google-ads/GoogleAdsOverviewPage'));
const GoogleAdsAccountsPage = lazy(() => import('./pages/google-ads/GoogleAdsAccountsPage'));
const GoogleAdsCampaignsPage = lazy(() => import('./pages/google-ads/GoogleAdsCampaignsPage'));
const GoogleAdsKeywordsPage = lazy(() => import('./pages/google-ads/GoogleAdsKeywordsPage'));
const GoogleAdsAdsPage = lazy(() => import('./pages/google-ads/GoogleAdsAdsPage'));
const GoogleAdsReportsPage = lazy(() => import('./pages/google-ads/GoogleAdsReportsPage'));
const GoogleAdsSettingsPage = lazy(() => import('./pages/google-ads/GoogleAdsSettingsPage'));
const FacebookAdsPage = lazy(() => import('./pages/FacebookAdsPage'));

// Zalo pages
const ZaloOverviewPage = lazy(() => import('./pages/zalo/ZaloOverviewPage'));
const ZaloAccountsPage = lazy(() => import('./pages/zalo/ZaloAccountsPage'));
const ZaloFollowersPage = lazy(() => import('./pages/zalo/ZaloFollowersPage'));
const ZaloZnsPage = lazy(() => import('./pages/zalo/ZaloZnsPage'));

// Email pages
const EmailOverviewPage = lazy(() => import('./pages/email/EmailOverviewPage'));
const EmailTemplatesPage = lazy(() => import('./pages/email/EmailTemplatesPage'));
const EmailCampaignsPage = lazy(() => import('./pages/email/EmailCampaignsPage'));
const EmailAnalyticsPage = lazy(() => import('./pages/email/EmailAnalyticsPage'));

// Zalo Ads pages
const ZaloAdsOverviewPage = lazy(() => import('./pages/zalo-ads/ZaloAdsOverviewPage'));
const ZaloAdsAccountsPage = lazy(() => import('./pages/zalo-ads/ZaloAdsAccountsPage'));
const ZaloAdsCampaignsPage = lazy(() => import('./pages/zalo-ads/ZaloAdsCampaignsPage'));
const ZaloAdsReportsPage = lazy(() => import('./pages/zalo-ads/ZaloAdsReportsPage'));

// TikTok Ads pages
const TikTokAdsOverviewPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsOverviewPage'));
const TikTokAdsAccountsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsAccountsPage'));
const TikTokAdsCampaignsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsCampaignsPage'));
const TikTokAdsCreativesPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsCreativesPage'));
const TikTokAdsAudiencesPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsAudiencesPage'));
const TikTokAdsReportsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsReportsPage'));
const TikTokAdsSettingsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsSettingsPage'));

// Zalo Ecosystem
const ZaloEcosystemPage = lazy(() => import('./pages/zalo/ZaloEcosystemPage'));

// Dashboard
const MarketingDashboardPage = lazy(() => import('./pages/MarketingDashboardPage'));

// Test page
const MarketingTestPage = lazy(() => import('./pages/MarketingTestPage'));

/**
 * Marketing module routes
 */
const marketingRoutes: RouteObject[] = [
  // Trang tổng quan
  {
    path: '/marketing',
    element: (
      <MainLayout title={i18n.t('marketing:title', 'Marketing')}>
        <Suspense fallback={<Loading />}>
          <MarketingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Marketing Dashboard
  {
    path: '/marketing/dashboard',
    element: (
      <MainLayout title={i18n.t('marketing:dashboard.title', 'Marketing Dashboard')}>
        <Suspense fallback={<Loading />}>
          <MarketingDashboardPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Marketing Test Page
  {
    path: '/marketing/test',
    element: (
      <MainLayout title="Marketing Test">
        <Suspense fallback={<Loading />}>
          <MarketingTestPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Audience routes
  {
    path: '/marketing/audience',
    element: (
      <MainLayout title={i18n.t('marketing:audience.title', 'Quản lý đối tượng')}>
        <Suspense fallback={<Loading />}>
          <AudiencePage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Segment routes
  {
    path: '/marketing/segment',
    element: (
      <MainLayout title={i18n.t('marketing:segment.title', 'Quản lý phân đoạn')}>
        <Suspense fallback={<Loading />}>
          <SegmentPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Campaign routes
  {
    path: '/marketing/campaign',
    element: (
      <MainLayout title={i18n.t('marketing:campaign.title', 'Quản lý chiến dịch')}>
        <Suspense fallback={<Loading />}>
          <CampaignPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Custom Fields routes
  {
    path: '/marketing/custom-fields',
    element: (
      <MainLayout title={i18n.t('marketing:customFields.title', 'Quản lý trường tùy chỉnh')}>
        <Suspense fallback={<Loading />}>
          <CustomFieldsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Tag Management routes
  {
    path: '/marketing/tags',
    element: (
      <MainLayout title={i18n.t('marketing:tags.title', 'Quản lý thẻ')}>
        <Suspense fallback={<Loading />}>
          <TagManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Reports routes
  {
    path: '/marketing/reports',
    element: (
      <MainLayout title={i18n.t('marketing:reports.title', 'Báo cáo')}>
        <Suspense fallback={<Loading />}>
          <ReportsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Template Email routes
  {
    path: '/marketing/template-emails',
    element: (
      <MainLayout title={i18n.t('marketing:templateEmail.title', 'Quản lý mẫu email')}>
        <Suspense fallback={<Loading />}>
          <TemplateEmailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // SMS Marketing routes
  {
    path: '/marketing/sms',
    element: (
      <MainLayout title={i18n.t('marketing:smsMarketing.title', 'SMS Marketing')}>
        <Suspense fallback={<Loading />}>
          <SmsMarketingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads routes
  {
    path: '/marketing/google-ads',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.title', 'Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Detail routes
  {
    path: '/marketing/google-ads/detail',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.detail.title', 'Chi tiết Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Accounts routes
  {
    path: '/marketing/google-ads/accounts',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.accounts.title', 'Tài khoản Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsAccountsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Campaigns routes
  {
    path: '/marketing/google-ads/campaigns',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.campaigns.title', 'Chiến dịch Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsCampaignsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Keywords routes
  {
    path: '/marketing/google-ads/keywords',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.keywords.title', 'Từ khóa Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsKeywordsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Ads routes
  {
    path: '/marketing/google-ads/ads',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.ads.title', 'Quảng cáo Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsAdsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Reports routes
  {
    path: '/marketing/google-ads/reports',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.reports.title', 'Báo cáo Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsReportsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Settings routes
  {
    path: '/marketing/google-ads/settings',
    element: (
      <MainLayout title={i18n.t('marketing:googleAds.settings.title', 'Cài đặt Google Ads')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsSettingsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Facebook Ads routes
  {
    path: '/marketing/facebook-ads',
    element: (
      <MainLayout title={i18n.t('marketing:facebookAds.title', 'Facebook Ads')}>
        <Suspense fallback={<Loading />}>
          <FacebookAdsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ecosystem routes
  {
    path: '/marketing/zalo',
    element: (
      <MainLayout title={i18n.t('marketing:zalo.ecosystem.title', 'Zalo Ecosystem')}>
        <Suspense fallback={<Loading />}>
          <ZaloEcosystemPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ecosystem (alternative path)
  {
    path: '/marketing/zalo/ecosystem',
    element: (
      <MainLayout title={i18n.t('marketing:zalo.ecosystem.title', 'Zalo Ecosystem')}>
        <Suspense fallback={<Loading />}>
          <ZaloEcosystemPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Overview
  {
    path: '/marketing/zalo/overview',
    element: (
      <MainLayout title={i18n.t('marketing:zalo.overview.title', 'Zalo Marketing')}>
        <Suspense fallback={<Loading />}>
          <ZaloOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Accounts
  {
    path: '/marketing/zalo/accounts',
    element: (
      <MainLayout title={i18n.t('marketing:zalo.accounts.title', 'Quản lý Zalo OA')}>
        <Suspense fallback={<Loading />}>
          <ZaloAccountsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Followers
  {
    path: '/marketing/zalo/accounts/:oaId/followers',
    element: (
      <MainLayout title={i18n.t('marketing:zalo.followers.title', 'Quản lý Followers')}>
        <Suspense fallback={<Loading />}>
          <ZaloFollowersPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo ZNS
  {
    path: '/marketing/zalo/zns',
    element: (
      <MainLayout title={i18n.t('marketing:zalo.zns.title', 'ZNS Templates')}>
        <Suspense fallback={<Loading />}>
          <ZaloZnsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Email Overview
  {
    path: '/marketing/email',
    element: (
      <MainLayout title={i18n.t('marketing:email.title', 'Email Marketing')}>
        <Suspense fallback={<Loading />}>
          <EmailOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Email Overview (alternative path)
  {
    path: '/marketing/email/overview',
    element: (
      <MainLayout title={i18n.t('marketing:email.overview.title', 'Email Marketing')}>
        <Suspense fallback={<Loading />}>
          <EmailOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Email Templates
  {
    path: '/marketing/email/templates',
    element: (
      <MainLayout title={i18n.t('marketing:email.templates.title', 'Email Templates')}>
        <Suspense fallback={<Loading />}>
          <EmailTemplatesPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Email Campaigns
  {
    path: '/marketing/email/campaigns',
    element: (
      <MainLayout title={i18n.t('marketing:email.campaigns.title', 'Email Campaigns')}>
        <Suspense fallback={<Loading />}>
          <EmailCampaignsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Email Analytics
  {
    path: '/marketing/email/analytics',
    element: (
      <MainLayout title={i18n.t('marketing:email.analytics.title', 'Analytics')}>
        <Suspense fallback={<Loading />}>
          <EmailAnalyticsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ads Overview
  {
    path: '/marketing/zalo-ads',
    element: (
      <MainLayout title={i18n.t('marketing:zaloAds.title', 'Zalo Ads')}>
        <Suspense fallback={<Loading />}>
          <ZaloAdsOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ads Overview (alternative path)
  {
    path: '/marketing/zalo-ads/overview',
    element: (
      <MainLayout title={i18n.t('marketing:zaloAds.overview.title', 'Zalo Ads Overview')}>
        <Suspense fallback={<Loading />}>
          <ZaloAdsOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ads Accounts
  {
    path: '/marketing/zalo-ads/accounts',
    element: (
      <MainLayout title={i18n.t('marketing:zaloAds.accounts.title', 'Zalo Ads Accounts')}>
        <Suspense fallback={<Loading />}>
          <ZaloAdsAccountsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ads Campaigns
  {
    path: '/marketing/zalo-ads/campaigns',
    element: (
      <MainLayout title={i18n.t('marketing:zaloAds.campaigns.title', 'Zalo Ads Campaigns')}>
        <Suspense fallback={<Loading />}>
          <ZaloAdsCampaignsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Ads Reports
  {
    path: '/marketing/zalo-ads/reports',
    element: (
      <MainLayout title={i18n.t('marketing:zaloAds.reports.title', 'Zalo Ads Reports')}>
        <Suspense fallback={<Loading />}>
          <ZaloAdsReportsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Overview
  {
    path: '/marketing/tiktok-ads',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.title', 'TikTok Ads')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Overview (alternative path)
  {
    path: '/marketing/tiktok-ads/overview',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.overview.title', 'TikTok Ads Overview')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Accounts
  {
    path: '/marketing/tiktok-ads/accounts',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.accounts.title', 'TikTok Ads Accounts')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsAccountsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Campaigns
  {
    path: '/marketing/tiktok-ads/campaigns',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.campaigns.title', 'TikTok Ads Campaigns')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsCampaignsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Creatives
  {
    path: '/marketing/tiktok-ads/creatives',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.creatives.title', 'TikTok Ads Creatives')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsCreativesPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Audiences
  {
    path: '/marketing/tiktok-ads/audiences',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.audiences.title', 'TikTok Ads Audiences')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsAudiencesPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Reports
  {
    path: '/marketing/tiktok-ads/reports',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.reports.title', 'TikTok Ads Reports')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsReportsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // TikTok Ads Settings
  {
    path: '/marketing/tiktok-ads/settings',
    element: (
      <MainLayout title={i18n.t('marketing:tiktokAds.settings.title', 'TikTok Ads Settings')}>
        <Suspense fallback={<Loading />}>
          <TikTokAdsSettingsPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default marketingRoutes;
