import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  AudienceStatus,
  AudienceQueryParams,
  AudienceType,
  AudienceAttribute,
} from '../types/audience.types';
import AudienceForm from '../components/forms/AudienceForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useAudiences, useCreateAudience, useDeleteAudience } from '../hooks';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Interface cho dữ liệu audience từ API
 */
interface AudienceData {
  id: string;
  name: string;
  description: string;
  type: AudienceType;
  status: AudienceStatus;
  totalContacts: number;
  attributes: AudienceAttribute[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Trang quản lý đối tượng sử dụng các hooks tối ưu
 */
const AudiencePage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // Sử dụng hooks từ API
  const { data: apiAudienceData, isLoading } = useAudiences({} as AudienceQueryParams);

  // Chuyển đổi từ API response sang AudienceData
  const audienceData = useMemo(() => {
    console.log('API Audience Data:', apiAudienceData); // Debug log

    // Lấy dữ liệu từ API response
    const rawData = apiAudienceData?.items || [];

    if (!rawData || rawData.length === 0) {
      return { items: [], meta: { currentPage: 1, totalItems: 0 } };
    }

    // Chuyển đổi mỗi item từ API response sang AudienceData
    const items = rawData.map((audience) => ({
      id: audience.id?.toString() || '',
      name: audience.name || '',
      description: audience.description || '',
      type: audience.type,
      status: audience.status,
      totalContacts: audience.totalContacts || 0,
      attributes: audience.attributes || [],
      createdAt: audience.createdAt,
      updatedAt: audience.updatedAt,
    }));

    console.log('Processed items:', items); // Debug log

    return {
      items,
      meta: apiAudienceData?.meta || { currentPage: 1, totalItems: items.length },
    };
  }, [apiAudienceData]);
  const createAudienceMutation = useCreateAudience();
  const deleteAudienceMutation = useDeleteAudience();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: number | string) => {
    console.log('Edit audience', id);
    // Thực hiện logic chỉnh sửa
  }, []);

  // Xử lý xóa
  const handleDelete = useCallback(
    (id: number | string) => {
      if (typeof id === 'string') {
        id = parseInt(id, 10);
      }
      if (!isNaN(id)) {
        deleteAudienceMutation.mutate(id);
      }
    },
    [deleteAudienceMutation]
  );

  // Schema đã được chuyển vào component AudienceForm

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<AudienceData>[]>(
    () => [
      { key: 'id', title: t('common:id', 'ID'), dataIndex: 'id', width: '10%', sortable: true },
      {
        key: 'name',
        title: t('marketing:audience.name', 'Tên đối tượng'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('common:description', 'Mô tả'),
        dataIndex: 'description',
        width: '20%',
        sortable: true,
      },
      {
        key: 'type',
        title: t('marketing:audience.type', 'Loại'),
        dataIndex: 'type',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const type = value as AudienceType;
          switch (type) {
            case AudienceType.CUSTOMER:
              return t('marketing:audience.types.customer', 'Khách hàng');
            case AudienceType.LEAD:
              return t('marketing:audience.types.lead', 'Tiềm năng');
            case AudienceType.SUBSCRIBER:
              return t('marketing:audience.types.subscriber', 'Người đăng ký');
            case AudienceType.CUSTOM:
              return t('marketing:audience.types.custom', 'Tùy chỉnh');
            default:
              return type;
          }
        },
      },
      {
        key: 'status',
        title: t('common:status', 'Trạng thái'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as AudienceStatus;
          const statusConfig = {
            [AudienceStatus.ACTIVE]: { label: t('common:active', 'Hoạt động'), variant: 'success' as const },
            [AudienceStatus.INACTIVE]: { label: t('common:inactive', 'Không hoạt động'), variant: 'danger' as const },
            [AudienceStatus.DRAFT]: { label: t('common:draft', 'Nháp'), variant: 'warning' as const },
          };

          const config = statusConfig[status] || { label: status, variant: 'default' as const };

          return (
            <Chip variant={config.variant} size="sm">
              {config.label}
            </Chip>
          );
        },
      },
      {
        key: 'totalContacts',
        title: t('marketing:audience.totalContacts', 'Số liên hệ'),
        dataIndex: 'totalContacts',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const count = value as number;
          return <span className="font-medium">{count.toLocaleString()}</span>;
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          // Chuyển đổi timestamp thành ngày
          const date = new Date(Number(value) * 1000);
          return date.toLocaleDateString('vi-VN');
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: AudienceData) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDelete(record.id),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit, handleDelete]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: AudienceStatus.ACTIVE },
      { id: 'draft', label: t('common:draft'), icon: 'file', value: AudienceStatus.DRAFT },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: AudienceStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AudienceQueryParams => {
    const queryParams: AudienceQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AudienceStatus;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AudienceData, AudienceQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [AudienceStatus.ACTIVE]: t('common:active'),
      [AudienceStatus.DRAFT]: t('common:draft'),
      [AudienceStatus.INACTIVE]: t('common:inactive'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateAudienceRequest
    const audienceData = {
      name: values.name as string,
      description: values.description as string | undefined,
      type: values.type as AudienceType,
      status: values.status as AudienceStatus | undefined,
      attributes: values.attributes as Omit<AudienceAttribute, 'id'>[] | undefined,
    };

    createAudienceMutation.mutate(audienceData);
    hideAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AudienceForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={audienceData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: audienceData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: audienceData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default AudiencePage;
