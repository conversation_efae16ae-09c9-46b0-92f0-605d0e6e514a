import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import CampaignForm from '../components/forms/CampaignForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  Campaign,
  CampaignStatus,
  CampaignType,
  CampaignQueryParams,
} from '../types/campaign.types';
import { useCampaigns, useCreateCampaign, useDeleteCampaign } from '../hooks';
import { CampaignService } from '../services/campaign.service';
import { useQueryClient } from '@tanstack/react-query';
import { CAMPAIGN_QUERY_KEYS } from '../hooks/useCampaignQuery';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Trang quản lý chiến dịch sử dụng các hooks tối ưu
 */
const CampaignPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const queryClient = useQueryClient();

  // Sử dụng hooks từ API
  const { data: campaignData, isLoading } = useCampaigns({} as CampaignQueryParams);
  const createCampaignMutation = useCreateCampaign();
  const deleteCampaignMutation = useDeleteCampaign();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: number | string) => {
    console.log('Edit campaign', id);
    // Thực hiện logic chỉnh sửa
  }, []);

  // Xử lý xóa
  const handleDelete = useCallback(
    (id: number | string) => {
      if (typeof id === 'string') {
        id = parseInt(id, 10);
      }
      if (!isNaN(id)) {
        deleteCampaignMutation.mutate(id);
      }
    },
    [deleteCampaignMutation]
  );

  // Xử lý chạy chiến dịch
  const handleRun = useCallback(
    (id: number | string) => {
      if (typeof id === 'string') {
        id = parseInt(id, 10);
      }
      if (!isNaN(id)) {
        // Sử dụng CampaignService trực tiếp
        CampaignService.updateCampaign(id, { status: CampaignStatus.RUNNING })
          .then(() => {
            // Refresh data sau khi cập nhật
            queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.all });
          })
          .catch((error: Error) => {
            console.error('Error running campaign:', error);
          });
      }
    },
    [queryClient]
  );

  // Xử lý tạm dừng chiến dịch
  const handlePause = useCallback(
    (id: number | string) => {
      if (typeof id === 'string') {
        id = parseInt(id, 10);
      }
      if (!isNaN(id)) {
        // Sử dụng CampaignService trực tiếp
        CampaignService.updateCampaign(id, { status: CampaignStatus.PAUSED })
          .then(() => {
            // Refresh data sau khi cập nhật
            queryClient.invalidateQueries({ queryKey: CAMPAIGN_QUERY_KEYS.all });
          })
          .catch((error: Error) => {
            console.error('Error pausing campaign:', error);
          });
      }
    },
    [queryClient]
  );

  // Schema đã được chuyển vào component CampaignForm

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<Campaign>[]>(
    () => [
      { key: 'name', title: 'Tên chiến dịch', dataIndex: 'name', width: '20%', sortable: true },
      { key: 'type', title: 'Loại', dataIndex: 'type', width: '10%', sortable: true },
      {
        key: 'status',
        title: 'Trạng thái',
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as CampaignStatus;
          let bgColor = '';
          let textColor = '';
          let statusText = '';

          switch (status) {
            case CampaignStatus.RUNNING:
              bgColor = 'bg-green-100 dark:bg-green-900';
              textColor = 'text-green-800 dark:text-green-200';
              statusText = t('campaign.status.running', 'Đang chạy');
              break;
            case CampaignStatus.PAUSED:
              bgColor = 'bg-yellow-100 dark:bg-yellow-900';
              textColor = 'text-yellow-800 dark:text-yellow-200';
              statusText = t('campaign.status.paused', 'Tạm dừng');
              break;
            case CampaignStatus.COMPLETED:
              bgColor = 'bg-blue-100 dark:bg-blue-900';
              textColor = 'text-blue-800 dark:text-blue-200';
              statusText = t('campaign.status.completed', 'Hoàn thành');
              break;
            case CampaignStatus.SCHEDULED:
              bgColor = 'bg-purple-100 dark:bg-purple-900';
              textColor = 'text-purple-800 dark:text-purple-200';
              statusText = t('campaign.status.scheduled', 'Đã lên lịch');
              break;
            case CampaignStatus.DRAFT:
              bgColor = 'bg-gray-100 dark:bg-gray-700';
              textColor = 'text-gray-800 dark:text-gray-200';
              statusText = t('campaign.status.draft', 'Bản nháp');
              break;
            case CampaignStatus.CANCELLED:
              bgColor = 'bg-red-100 dark:bg-red-900';
              textColor = 'text-red-800 dark:text-red-200';
              statusText = t('campaign.status.cancelled', 'Đã hủy');
              break;
          }

          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${bgColor} ${textColor}`}
            >
              {statusText}
            </div>
          );
        },
      },
      {
        key: 'audienceName',
        title: 'Đối tượng',
        dataIndex: 'audienceName',
        width: '15%',
        sortable: true,
      },
      {
        key: 'startDate',
        title: 'Ngày bắt đầu',
        dataIndex: 'startDate',
        width: '10%',
        sortable: true,
      },
      {
        key: 'metrics.sent',
        title: 'Đã gửi',
        dataIndex: 'metrics',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const metrics = value as Campaign['metrics'];
          return <div className="text-center font-medium">{metrics?.sent || 0}</div>;
        },
      },
      {
        key: 'metrics.openRate',
        title: 'Tỷ lệ mở',
        dataIndex: 'metrics',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const metrics = value as Campaign['metrics'];
          const openRate = metrics?.sent > 0 ? (metrics?.opened / metrics?.sent) * 100 : 0;
          return <div className="text-center font-medium">{`${openRate.toFixed(1)}%`}</div>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: Campaign) => {
          // Tạo danh sách các action items dựa trên status
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
            },
          ];

          // Thêm action pause/play dựa trên status
          if (record.status === CampaignStatus.RUNNING) {
            actionItems.push({
              id: 'pause',
              label: t('campaign.actions.pause', 'Tạm dừng'),
              icon: 'pause',
              onClick: () => handlePause(record.id),
            });
          } else if (record.status === CampaignStatus.PAUSED) {
            actionItems.push({
              id: 'play',
              label: t('campaign.actions.run', 'Tiếp tục'),
              icon: 'play',
              onClick: () => handleRun(record.id),
            });
          }

          // Thêm action delete
          actionItems.push({
            id: 'delete',
            label: t('common:delete', 'Xóa'),
            icon: 'trash',
            onClick: () => handleDelete(record.id),
          });

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit, handleDelete, handleRun, handlePause]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'running',
        label: t('campaign.status.running', 'Đang chạy'),
        icon: 'check',
        value: CampaignStatus.RUNNING,
      },
      {
        id: 'paused',
        label: t('campaign.status.paused', 'Tạm dừng'),
        icon: 'pause',
        value: CampaignStatus.PAUSED,
      },
      {
        id: 'completed',
        label: t('campaign.status.completed', 'Hoàn thành'),
        icon: 'check-circle',
        value: CampaignStatus.COMPLETED,
      },
      {
        id: 'scheduled',
        label: t('campaign.status.scheduled', 'Đã lên lịch'),
        icon: 'calendar',
        value: CampaignStatus.SCHEDULED,
      },
      {
        id: 'draft',
        label: t('campaign.status.draft', 'Bản nháp'),
        icon: 'file',
        value: CampaignStatus.DRAFT,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): CampaignQueryParams => {
    const queryParams: CampaignQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as CampaignStatus;
    }

    // Xử lý dateRange nếu có
    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.startDate = params.dateRange[0].toISOString();
      queryParams.endDate = params.dateRange[1].toISOString();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<Campaign, CampaignQueryParams>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [CampaignStatus.RUNNING]: t('campaign.status.running', 'Đang chạy'),
      [CampaignStatus.PAUSED]: t('campaign.status.paused', 'Tạm dừng'),
      [CampaignStatus.COMPLETED]: t('campaign.status.completed', 'Hoàn thành'),
      [CampaignStatus.SCHEDULED]: t('campaign.status.scheduled', 'Đã lên lịch'),
      [CampaignStatus.DRAFT]: t('campaign.status.draft', 'Bản nháp'),
      [CampaignStatus.CANCELLED]: t('campaign.status.cancelled', 'Đã hủy'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateCampaignRequest
    const campaignData = {
      name: values.name as string,
      description: values.description as string | undefined,
      type: values.type as CampaignType,
      segmentId: Number(values.segmentId),
      startDate: values.startDate as string | undefined,
      endDate: values.endDate as string | undefined,
      status: values.status as CampaignStatus | undefined,
      content: values.content as string | undefined,
      templateId: values.templateId ? Number(values.templateId) : undefined,
    };

    createCampaignMutation.mutate(campaignData);
    hideAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <CampaignForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={campaignData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: campaignData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: campaignData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default CampaignPage;
