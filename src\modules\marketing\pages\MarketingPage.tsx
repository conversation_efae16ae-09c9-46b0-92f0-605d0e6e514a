import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Interface mở rộng cho statistics để tránh dùng any
 */


/**
 * Trang tổng quan Marketing
 */
const MarketingPage: React.FC = () => {
  const { t } = useTranslation('marketing');

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Audience Card */}
        <ModuleCard
          title={t('audience.title')}
          description={t('audience.description')}
          icon="users"
          linkTo="/marketing/audience"
        />

        {/* Segment Card */}
        <ModuleCard
          title={t('segment.title')}
          description={t('segment.description')}
          icon="filter"
          linkTo="/marketing/segment"
        />

        {/* Campaign Card */}
        <ModuleCard
          title={t('campaign.title')}
          description={t('campaign.description')}
          icon="campaign"
          linkTo="/marketing/campaign"
        />

        {/* Custom Fields Card */}
        <ModuleCard
          title={t('customFields.title', 'Quản lý trường tùy chỉnh')}
          description={t('customFields.description', 'Tạo và quản lý các trường dữ liệu tùy chỉnh')}
          icon="database"
          linkTo="/marketing/custom-fields"
        />

        {/* Tags Card */}
        <ModuleCard
          title={t('tags.title', 'Quản lý tag')}
          description={t('tags.description', 'Tạo và quản lý các tag cho khách hàng')}
          icon="tag"
          linkTo="/marketing/tags"
        />

        {/* Reports Card */}
        <ModuleCard
          title={t('reports.title', 'Báo cáo')}
          description={t('reports.description', 'Xem các báo cáo về hoạt động marketing')}
          icon="bar-chart"
          linkTo="/marketing/reports"
        />

        {/* Email Marketing Card */}
        <ModuleCard
          title={t('emailMarketing.title', 'Email Marketing')}
          description={t('emailMarketing.description', 'Quản lý chiến dịch Email, mẫu Email')}
          icon="mail"
          linkTo="/marketing/email"
        />

        {/* SMS Marketing Card */}
        <ModuleCard
          title={t('smsMarketing.title', 'SMS Marketing')}
          description={t('smsMarketing.description', 'Gửi và quản lý chiến dịch SMS')}
          icon="sms"
          linkTo="/marketing/sms"
        />

        {/* Google Ads Card */}
        <ModuleCard
          title={t('googleAds.title', 'Google Ads')}
          description={t('googleAds.description', 'Tích hợp và quản lý chiến dịch Google Ads')}
          icon="google"
          linkTo="/marketing/google-ads"
        />

        {/* Facebook Ads Card */}
        <ModuleCard
          title={t('facebookAds.title', 'Facebook Ads')}
          description={t('facebookAds.description', 'Tích hợp và quản lý chiến dịch Facebook Ads')}
          icon="facebook"
          linkTo="/marketing/facebook-ads"
        />

        {/* TikTok Ads Card */}
        <ModuleCard
          title={t('tiktokAds.title', 'TikTok Ads')}
          description={t('tiktokAds.description', 'Tích hợp và quản lý chiến dịch TikTok Ads')}
          icon="video"
          linkTo="/marketing/tiktok-ads"
        />

        {/* Zalo OA Card */}
        <ModuleCard
          title={t('zalo.title', 'Zalo OA/ZNS')}
          description={t('zalo.description', 'Tích hợp Zalo OA, gửi ZNS tin nhắn chăm sóc')}
          icon="zap"
          linkTo="/marketing/zalo"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default MarketingPage;
