import { useNavigate } from 'react-router-dom';
import { Card, Button } from '@/shared/components/common';

/**
 * Trang test để kiểm tra giao diện Marketing
 */
export function MarketingTestPage() {
  const navigate = useNavigate();

  const testPages = [
    {
      title: 'Marketing Dashboard',
      description: 'Trang tổng quan marketing',
      path: '/marketing/dashboard',
    },
    {
      title: 'Zalo Overview',
      description: 'Tổng quan Zalo Marketing',
      path: '/marketing/zalo/overview',
    },
    {
      title: 'Zalo Accounts',
      description: 'Quản lý Zalo OA',
      path: '/marketing/zalo/accounts',
    },
    {
      title: 'Zalo ZNS Templates',
      description: 'Quản lý ZNS Templates',
      path: '/marketing/zalo/zns',
    },
    {
      title: 'Email Overview',
      description: 'Tổng quan Email Marketing',
      path: '/marketing/email/overview',
    },
    {
      title: 'Email Templates',
      description: 'Quản lý Email Templates',
      path: '/marketing/email/templates',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {testPages.map((page) => (
          <Card
            key={page.path}
            title={page.title}
            subtitle={page.description}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => navigate(page.path)}
            hoverable
          >
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(page.path);
                }}
              >
                Xem trang
              </Button>
            </div>
          </Card>
        ))}
      </div>

      <Card title="Thông tin Module">
        <div className="space-y-2 text-sm">
          <p><strong>Phase:</strong> 1 & 2 - Foundation & Core Features</p>
          <p><strong>Status:</strong> ✅ Hoàn thành</p>
          <p><strong>Features:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Zalo Marketing: OA management, Followers, ZNS Templates</li>
            <li>Email Marketing: Templates với CRUD operations</li>
            <li>Dashboard tổng quan multi-channel</li>
            <li>Responsive design & TypeScript strict mode</li>
          </ul>
        </div>
      </Card>
    </div>
  );
}

export default MarketingTestPage;
