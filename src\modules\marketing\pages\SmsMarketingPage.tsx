import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon } from '@/shared/components/common';

/**
 * Trang quản lý SMS Marketing – skeleton
 */
const SmsMarketingPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  return (
    <Card className="p-6">
      <div className="flex items-center mb-4">
        <Icon name="sms" size="lg" className="text-primary mr-2" />
        <Typography variant="h4">
          {t('marketing:smsMarketing.title', 'SMS Marketing')}
        </Typography>
      </div>
      <Typography variant="body1">
        {t(
          'marketing:smsMarketing.comingSoon',
          'Tính năng SMS Marketing đang được phát triển. Vui lòng quay lại sau!'
        )}
      </Typography>
    </Card>
  );
};

export default SmsMarketingPage;