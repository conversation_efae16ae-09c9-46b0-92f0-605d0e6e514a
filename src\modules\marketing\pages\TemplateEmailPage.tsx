import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  TemplateEmail,
  TemplateEmailStatus,
  TemplateEmailQueryParams,
  CreateTemplateEmailRequest,
} from '../types/template-email.types';
import { useTemplateEmails, useCreateTemplateEmail, useDeleteTemplateEmail } from '../hooks';
import { TemplateEmailForm } from '@/modules/marketing/components/forms';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Trang quản lý mẫu email sử dụng các hooks tối ưu
 */
const TemplateEmailPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // Sử dụng hooks từ API
  const { data: templateData, isLoading } = useTemplateEmails({} as TemplateEmailQueryParams);
  const createTemplateMutation = useCreateTemplateEmail();
  const deleteTemplateMutation = useDeleteTemplateEmail();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: number | string) => {
    console.log('Edit template', id);
    // Thực hiện logic chỉnh sửa
  }, []);

  // Xử lý xóa
  const handleDelete = useCallback(
    (id: number | string) => {
      if (typeof id === 'string') {
        id = parseInt(id, 10);
      }
      if (!isNaN(id)) {
        deleteTemplateMutation.mutate(id);
      }
    },
    [deleteTemplateMutation]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<TemplateEmail>[]>(
    () => [
      { key: 'id', title: t('common:id', 'ID'), dataIndex: 'id', width: '10%', sortable: true },
      {
        key: 'name',
        title: t('marketing:templateEmail.name', 'Tên mẫu'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'subject',
        title: t('marketing:templateEmail.subject', 'Tiêu đề email'),
        dataIndex: 'subject',
        width: '25%',
        sortable: true,
      },
      {
        key: 'type',
        title: t('common:type', 'Loại'),
        dataIndex: 'type',
        width: '10%',
        sortable: true,
      },
      {
        key: 'status',
        title: t('common:status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as TemplateEmailStatus;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                status === TemplateEmailStatus.ACTIVE
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : status === TemplateEmailStatus.DRAFT
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {status === TemplateEmailStatus.ACTIVE
                ? t('marketing:templateEmail.statuses.active', 'Hoạt động')
                : status === TemplateEmailStatus.DRAFT
                  ? t('marketing:templateEmail.statuses.draft', 'Bản nháp')
                  : t('marketing:templateEmail.statuses.inactive', 'Không hoạt động')}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '10%',
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: TemplateEmail) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDelete(record.id),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit, handleDelete]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: TemplateEmailStatus.ACTIVE },
      { id: 'draft', label: t('common:draft'), icon: 'file', value: TemplateEmailStatus.DRAFT },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: TemplateEmailStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): TemplateEmailQueryParams => {
    const queryParams: TemplateEmailQueryParams = {
      page: params.page,
      limit: params.pageSize,
      name: params.searchTerm || undefined, // Backend uses 'name' for search
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as TemplateEmailStatus;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<TemplateEmail, TemplateEmailQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [TemplateEmailStatus.ACTIVE]: t('marketing:templateEmail.statuses.active', 'Hoạt động'),
      [TemplateEmailStatus.DRAFT]: t('marketing:templateEmail.statuses.draft', 'Bản nháp'),
      [TemplateEmailStatus.INACTIVE]: t(
        'marketing:templateEmail.statuses.inactive',
        'Không hoạt động'
      ),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateTemplateEmailRequest
    const templateData: CreateTemplateEmailRequest = {
      name: values.name as string,
      description: values.description as string | undefined,
      subject: values.subject as string,
      content: values.content as string,
      // Note: type and status are not in the current CreateTemplateEmailRequest interface
      // They might be handled by the backend with default values
    };

    createTemplateMutation.mutate(templateData);
    hideAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <TemplateEmailForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={templateData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: templateData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: templateData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default TemplateEmailPage;
