import React, { useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Mail,
  BarChart3,
  Users,
  Calendar,
  Send
} from 'lucide-react';
import {
  Card,
  Table,
  Badge,
  IconCard,
  Tooltip,
  Button,
  Typography,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useEmailCampaigns, usePauseResumeEmailCampaign, useSendEmailCampaign } from '../../hooks/email/useEmailCampaigns';
import { CreateEmailCampaignForm } from '../../components/email/CreateEmailCampaignForm';
import type { EmailCampaignDto, EmailCampaignQueryDto, EmailCampaignStatus } from '../../types/email.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý Email Campaigns theo quy tắc RedAI
 */
export function EmailCampaignsPage() {
  const { t } = useTranslation(['marketing', 'common']);
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedCampaign, setSelectedCampaign] = React.useState<EmailCampaignDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible: isCreateVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();
  const { isVisible: isAnalyticsVisible, showForm: showAnalyticsForm, hideForm: hideAnalyticsForm } = useSlideForm();

  // Mutations
  const pauseResumeCampaign = usePauseResumeEmailCampaign();
  const sendCampaign = useSendEmailCampaign();

  // Kiểm tra nếu có action=create trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'create') {
      showCreateForm();
      setSearchParams({});
    }
  }, [searchParams, showCreateForm, setSearchParams]);

  const handleViewAnalytics = useCallback((campaign: EmailCampaignDto) => {
    setSelectedCampaign(campaign);
    showAnalyticsForm();
  }, [showAnalyticsForm]);

  const handlePauseResume = useCallback((campaign: EmailCampaignDto) => {
    const action = campaign.status === 'SENDING' ? 'PAUSE' : 'RESUME';
    pauseResumeCampaign.mutate({ id: campaign.id, action });
  }, [pauseResumeCampaign]);

  const handleSendCampaign = useCallback((campaign: EmailCampaignDto) => {
    sendCampaign.mutate(campaign.id);
  }, [sendCampaign]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<EmailCampaignDto>[]>(
    () => [
      {
        key: 'campaign',
        title: t('marketing:email.campaigns.table.campaign', 'Chiến dịch'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: EmailCampaignDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white">
              <Mail className="h-6 w-6" />
            </div>
            <div>
              <Typography variant="body2" weight="medium">{String(value || '')}</Typography>
              <Typography variant="caption" color="muted">
                {record.subject}
              </Typography>
            </div>
          </div>
        ),
      },
      {
        key: 'status',
        title: t('marketing:email.campaigns.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as EmailCampaignStatus;
          const statusConfig = {
            DRAFT: { variant: 'warning' as const, label: t('marketing:email.campaigns.status.draft', 'Bản nháp') },
            SCHEDULED: { variant: 'info' as const, label: t('marketing:email.campaigns.status.scheduled', 'Đã lên lịch') },
            SENDING: { variant: 'primary' as const, label: t('marketing:email.campaigns.status.sending', 'Đang gửi') },
            SENT: { variant: 'success' as const, label: t('marketing:email.campaigns.status.sent', 'Đã gửi') },
            PAUSED: { variant: 'warning' as const, label: t('marketing:email.campaigns.status.paused', 'Tạm dừng') },
            CANCELLED: { variant: 'danger' as const, label: t('marketing:email.campaigns.status.cancelled', 'Đã hủy') },
            FAILED: { variant: 'danger' as const, label: t('marketing:email.campaigns.status.failed', 'Thất bại') },
          };
          const config = statusConfig[status] || { variant: 'info' as const, label: status };
          return <Badge variant={config.variant}>{config.label}</Badge>;
        },
      },
      {
        key: 'recipients',
        title: t('marketing:email.campaigns.table.recipients', 'Người nhận'),
        dataIndex: 'totalRecipients',
        sortable: true,
        render: (value: unknown) => (
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4 text-muted-foreground" />
            <Typography variant="body2">{String(value || 0)}</Typography>
          </div>
        ),
      },
      {
        key: 'performance',
        title: t('marketing:email.campaigns.table.performance', 'Hiệu suất'),
        render: (_: unknown, record: EmailCampaignDto) => {
          const openRate = record.totalRecipients > 0 ? (record.openedCount / record.totalRecipients * 100) : 0;
          const clickRate = record.totalRecipients > 0 ? (record.clickedCount / record.totalRecipients * 100) : 0;

          return (
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span>Open: {openRate.toFixed(1)}%</span>
                <span>Click: {clickRate.toFixed(1)}%</span>
              </div>
              <div className="flex gap-1">
                <div className="flex-1 bg-gray-200 rounded-full h-1">
                  <div
                    className="bg-green-500 h-1 rounded-full"
                    style={{ width: `${Math.min(openRate, 100)}%` }}
                  />
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-1">
                  <div
                    className="bg-blue-500 h-1 rounded-full"
                    style={{ width: `${Math.min(clickRate, 100)}%` }}
                  />
                </div>
              </div>
            </div>
          );
        },
      },
      {
        key: 'scheduledAt',
        title: t('marketing:email.campaigns.table.scheduled', 'Lên lịch'),
        dataIndex: 'scheduledAt',
        sortable: true,
        render: (value: unknown) => {
          if (!value) return <Typography variant="caption" color="muted">Gửi ngay</Typography>;
          return (
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <Typography variant="caption">
                {new Date(Number(value)).toLocaleDateString('vi-VN')}
              </Typography>
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('marketing:email.campaigns.table.actions', 'Thao tác'),
        width: '150px',
        render: (_: unknown, record: EmailCampaignDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:email.campaigns.analytics.title', 'Xem Analytics')}>
              <IconCard
                icon="bar-chart"
                variant="default"
                size="sm"
                onClick={() => handleViewAnalytics(record)}
              />
            </Tooltip>

            {record.status === 'DRAFT' && (
              <Tooltip content={t('marketing:email.campaigns.send', 'Gửi chiến dịch')}>
                <IconCard
                  icon="send"
                  variant="primary"
                  size="sm"
                  onClick={() => handleSendCampaign(record)}
                />
              </Tooltip>
            )}

            {(record.status === 'SENDING' || record.status === 'PAUSED') && (
              <Tooltip content={record.status === 'SENDING' ? t('marketing:email.campaigns.pause', 'Tạm dừng') : t('marketing:email.campaigns.resume', 'Tiếp tục')}>
                <IconCard
                  icon={record.status === 'SENDING' ? 'pause' : 'play'}
                  variant="danger"
                  size="sm"
                  onClick={() => handlePauseResume(record)}
                />
              </Tooltip>
            )}

            <Tooltip content={t('common:edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Edit campaign:', record.id);
                }}
              />
            </Tooltip>

            <Tooltip content={t('common:delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => {
                  console.log('Delete campaign:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleViewAnalytics, handlePauseResume, handleSendCampaign]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'draft',
        label: t('marketing:email.campaigns.status.draft', 'Bản nháp'),
        icon: 'edit',
        value: 'DRAFT',
      },
      {
        id: 'scheduled',
        label: t('marketing:email.campaigns.status.scheduled', 'Đã lên lịch'),
        icon: 'calendar',
        value: 'SCHEDULED',
      },
      {
        id: 'sending',
        label: t('marketing:email.campaigns.status.sending', 'Đang gửi'),
        icon: 'send',
        value: 'SENDING',
      },
      {
        id: 'sent',
        label: t('marketing:email.campaigns.status.sent', 'Đã gửi'),
        icon: 'check',
        value: 'SENT',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): EmailCampaignQueryDto => {
      const queryParams: EmailCampaignQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as EmailCampaignStatus;
      }

      if (params.dateRange[0] && params.dateRange[1]) {
        queryParams.startDate = params.dateRange[0];
        queryParams.endDate = params.dateRange[1];
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<EmailCampaignDto, EmailCampaignQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // API call với query từ dataTable
  const { data: campaignsData, isLoading } = useEmailCampaigns(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng với API data
  useEffect(() => {
    updateTableDataRef.current(campaignsData, isLoading);
  }, [campaignsData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        DRAFT: t('marketing:email.campaigns.status.draft', 'Bản nháp'),
        SCHEDULED: t('marketing:email.campaigns.status.scheduled', 'Đã lên lịch'),
        SENDING: t('marketing:email.campaigns.status.sending', 'Đang gửi'),
        SENT: t('marketing:email.campaigns.status.sent', 'Đã gửi'),
        PAUSED: t('marketing:email.campaigns.status.paused', 'Tạm dừng'),
        CANCELLED: t('marketing:email.campaigns.status.cancelled', 'Đã hủy'),
        FAILED: t('marketing:email.campaigns.status.failed', 'Thất bại'),
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    setSearchParams({});
  };

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:email.campaigns.title', 'Email Campaigns')}
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" color="muted">
              {t('marketing:email.campaigns.stats.totalCampaigns', 'Tổng chiến dịch')}
            </Typography>
            <Mail className="h-4 w-4 text-blue-600" />
          </div>
          <Typography variant="h3" color="primary" className="text-blue-600">
            {campaignsData?.meta.totalItems || 0}
          </Typography>
          <Typography variant="caption" color="muted" className="mt-1">
            {t('marketing:email.campaigns.stats.newCampaigns', '+2 chiến dịch mới')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" color="muted">
              {t('marketing:email.campaigns.stats.sending', 'Đang gửi')}
            </Typography>
            <Send className="h-4 w-4 text-orange-600" />
          </div>
          <Typography variant="h3" className="text-orange-600">
            {campaignsData?.items.filter((campaign: EmailCampaignDto) => campaign.status === 'SENDING').length || 0}
          </Typography>
          <Typography variant="caption" color="muted" className="mt-1">
            {t('marketing:email.campaigns.stats.active', 'Đang hoạt động')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" color="muted">
              {t('marketing:email.campaigns.stats.sent', 'Đã gửi')}
            </Typography>
            <BarChart3 className="h-4 w-4 text-green-600" />
          </div>
          <Typography variant="h3" className="text-green-600">
            {campaignsData?.items.filter((campaign: EmailCampaignDto) => campaign.status === 'SENT').length || 0}
          </Typography>
          <Typography variant="caption" color="muted" className="mt-1">
            {t('marketing:email.campaigns.stats.completed', 'Hoàn thành')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" color="muted">
              {t('marketing:email.campaigns.stats.scheduled', 'Đã lên lịch')}
            </Typography>
            <Calendar className="h-4 w-4 text-purple-600" />
          </div>
          <Typography variant="h3" className="text-purple-600">
            {campaignsData?.items.filter((campaign: EmailCampaignDto) => campaign.status === 'SCHEDULED').length || 0}
          </Typography>
          <Typography variant="caption" color="muted" className="mt-1">
            {t('marketing:email.campaigns.stats.upcoming', 'Sắp tới')}
          </Typography>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        onDateRangeChange={dataTable.dateRange.setDateRange}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={() => dataTable.dateRange.setDateRange([null, null])}
        onClearAll={handleClearAll}
      />

      {/* Campaigns Table */}
      <Card className="overflow-hidden">
        <Table<EmailCampaignDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={campaignsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: campaignsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: campaignsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho tạo campaign */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h4">
              {t('marketing:email.campaigns.create.title', 'Tạo Email Campaign')}
            </Typography>
            <Typography variant="body2" color="muted" className="mt-1">
              {t('marketing:email.campaigns.create.description', 'Tạo chiến dịch email marketing mới')}
            </Typography>
          </div>
          <CreateEmailCampaignForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho analytics */}
      <SlideInForm isVisible={isAnalyticsVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h4">
              {t('marketing:email.campaigns.analytics.title', 'Campaign Analytics')}
            </Typography>
            {selectedCampaign && (
              <Typography variant="body2" color="muted" className="mt-1">
                {t('marketing:email.campaigns.analytics.description', 'Chiến dịch: {{name}}', { name: selectedCampaign.name })}
              </Typography>
            )}
          </div>

          {selectedCampaign && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="p-4 text-center">
                  <Typography variant="h4" className="text-blue-600">
                    {selectedCampaign.totalRecipients}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    {t('marketing:email.campaigns.analytics.totalRecipients', 'Tổng người nhận')}
                  </Typography>
                </Card>

                <Card className="p-4 text-center">
                  <Typography variant="h4" className="text-green-600">
                    {selectedCampaign.deliveredCount}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    {t('marketing:email.campaigns.analytics.delivered', 'Đã gửi')}
                  </Typography>
                </Card>

                <Card className="p-4 text-center">
                  <Typography variant="h4" className="text-orange-600">
                    {selectedCampaign.openedCount}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    {t('marketing:email.campaigns.analytics.opened', 'Đã mở')}
                  </Typography>
                </Card>

                <Card className="p-4 text-center">
                  <Typography variant="h4" className="text-purple-600">
                    {selectedCampaign.clickedCount}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    {t('marketing:email.campaigns.analytics.clicked', 'Đã click')}
                  </Typography>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <Typography variant="subtitle2" className="mb-2">
                    {t('marketing:email.campaigns.analytics.openRate', 'Tỷ lệ mở email')}
                  </Typography>
                  <div className="flex items-center justify-between">
                    <Typography variant="h3" className="text-green-600">
                      {selectedCampaign.totalRecipients > 0
                        ? ((selectedCampaign.openedCount / selectedCampaign.totalRecipients) * 100).toFixed(1)
                        : 0}%
                    </Typography>
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${selectedCampaign.totalRecipients > 0
                            ? Math.min((selectedCampaign.openedCount / selectedCampaign.totalRecipients) * 100, 100)
                            : 0}%`
                        }}
                      />
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <Typography variant="subtitle2" className="mb-2">
                    {t('marketing:email.campaigns.analytics.clickRate', 'Tỷ lệ click')}
                  </Typography>
                  <div className="flex items-center justify-between">
                    <Typography variant="h3" className="text-blue-600">
                      {selectedCampaign.totalRecipients > 0
                        ? ((selectedCampaign.clickedCount / selectedCampaign.totalRecipients) * 100).toFixed(1)
                        : 0}%
                    </Typography>
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${selectedCampaign.totalRecipients > 0
                            ? Math.min((selectedCampaign.clickedCount / selectedCampaign.totalRecipients) * 100, 100)
                            : 0}%`
                        }}
                      />
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={hideAnalyticsForm}>
              {t('common:close', 'Đóng')}
            </Button>
          </div>
        </div>
      </SlideInForm>
    </div>
  );
}

export default EmailCampaignsPage;
