import { useTranslation } from 'react-i18next';
import { Mail, FileText, Send, Eye, MousePointer } from 'lucide-react';
import { Card, Chip, Button, Skeleton } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { useEmailTemplates } from '../../hooks/email/useEmailTemplates';
import { useNavigate } from 'react-router-dom';
import type { EmailTemplateDto } from '../../types/email.types';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';

/**
 * Trang tổng quan Email Marketing
 */
export function EmailOverviewPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  const { data: templatesData, isLoading } = useEmailTemplates({ limit: 5 });

  const handleCreateTemplate = () => {
    navigate('/marketing/email/templates?action=create');
  };

  // const handleViewCampaigns = () => {
  //   navigate('/marketing/email/campaigns');
  // };

  const handleViewTemplate = (templateId: string) => {
    navigate(`/marketing/email/templates/${templateId}`);
  };

  // Mock data cho demo
  const mockStats = {
    totalTemplates: templatesData?.meta.totalItems || 0,
    totalCampaigns: 24,
    emailsSentThisMonth: 15420,
    averageOpenRate: 28.5,
    averageClickRate: 4.2,
    recentCampaigns: [
      {
        id: '1',
        name: 'Newsletter tháng 1',
        status: 'sent',
        sentAt: new Date('2025-01-20'),
        recipients: 1250,
        openRate: 32.4,
        clickRate: 5.1,
      },
      {
        id: '2',
        name: 'Khuyến mãi cuối năm',
        status: 'sending',
        sentAt: new Date('2025-01-22'),
        recipients: 2100,
        openRate: 0,
        clickRate: 0,
      },
      {
        id: '3',
        name: 'Chào mừng khách hàng mới',
        status: 'scheduled',
        sentAt: new Date('2025-01-25'),
        recipients: 450,
        openRate: 0,
        clickRate: 0,
      },
    ],
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <Chip variant="success">Đã gửi</Chip>;
      case 'sending':
        return <Chip variant="warning">Đang gửi</Chip>;
      case 'scheduled':
        return <Chip variant="info">Đã lên lịch</Chip>;
      case 'draft':
        return <Chip variant="warning">Bản nháp</Chip>;
      default:
        return <Chip variant="primary">{status}</Chip>;
    }
  };

  // Dữ liệu cho OverviewCard
  const overviewStats: OverviewCardProps[] = [
    {
      title: t('marketing:email.overview.stats.totalTemplates', 'Tổng Templates'),
      value: mockStats.totalTemplates,
      description: t('marketing:email.overview.stats.activeTemplates', '+3 template mới'),
      icon: FileText,
      color: 'blue',
      isLoading,
    },
    {
      title: t('marketing:email.overview.stats.emailsSent', 'Email đã gửi'),
      value: mockStats.emailsSentThisMonth.toLocaleString(),
      description: t('marketing:email.overview.stats.thisMonth', 'Tháng này'),
      icon: Send,
      color: 'green',
    },
    {
      title: t('marketing:email.overview.stats.openRate', 'Tỷ lệ mở'),
      value: `${mockStats.averageOpenRate}%`,
      description: t('marketing:email.overview.stats.averageOpenRate', 'Trung bình 30 ngày'),
      icon: Eye,
      color: 'orange',
    },
    {
      title: t('marketing:email.overview.stats.clickRate', 'Tỷ lệ click'),
      value: `${mockStats.averageClickRate}%`,
      description: t('marketing:email.overview.stats.averageClickRate', 'Trung bình 30 ngày'),
      icon: MousePointer,
      color: 'purple',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={isLoading}
        skeletonCount={4}
      />

      {/* Recent Templates */}
      <Card
        title={t('marketing:email.overview.recentTemplates', 'Templates gần đây')}
        subtitle={t(
          'marketing:email.overview.recentTemplatesDescription',
          'Các email template được tạo hoặc cập nhật gần đây'
        )}
        className="p-4"
      >
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
              </div>
            ))}
          </div>
        ) : templatesData?.items.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
              <FileText className="h-full w-full" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {t('marketing:email.overview.noTemplates', 'Chưa có template nào')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t(
                'marketing:email.overview.noTemplatesDescription',
                'Tạo template đầu tiên để bắt đầu gửi email'
              )}
            </p>
            <Button onClick={handleCreateTemplate}>
              {t('marketing:email.overview.createFirstTemplate', 'Tạo template đầu tiên')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {templatesData?.items.map((template: EmailTemplateDto) => (
              <Card
                key={template.id}
                className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleViewTemplate(template.id)}
                hoverable
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 rounded bg-gradient-to-r from-secondary/20 to-secondary/10 flex items-center justify-center text-secondary">
                      <Mail className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">{template.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <span>{template.subject}</span>
                        <Chip variant="info" size="sm">
                          {template.type}
                        </Chip>
                        <Chip
                          variant={template.status === 'ACTIVE' ? 'success' : 'warning'}
                          size="sm"
                        >
                          {template.status === 'ACTIVE' ? 'Hoạt động' : 'Bản nháp'}
                        </Chip>
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    {new Date(template.updatedAt).toLocaleDateString('vi-VN')}
                  </div>
                </div>
              </Card>
            ))}

            {templatesData && templatesData.meta.totalItems > templatesData.items.length && (
              <div className="text-center pt-4">
                <Button variant="outline" onClick={() => navigate('/marketing/email/templates')}>
                  {t('marketing:email.overview.viewAllTemplates', 'Xem tất cả templates')}
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Recent Campaigns */}
      <Card
        title={t('marketing:email.overview.recentCampaigns', 'Chiến dịch gần đây')}
        subtitle={t(
          'marketing:email.overview.recentCampaignsDescription',
          'Tình trạng các chiến dịch email gần đây'
        )}
        className="p-4"
      >
        <div className="space-y-4">
          {mockStats.recentCampaigns.map(campaign => (
            <Card
              key={campaign.id}
              className="p-4 hover:shadow-md transition-shadow cursor-pointer"
              hoverable
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-10 w-10 rounded bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center text-primary">
                    <Send className="h-5 w-5" />
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground">{campaign.name}</h4>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <span>{campaign.recipients.toLocaleString()} người nhận</span>
                      {getStatusBadge(campaign.status)}
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-sm font-medium">
                    {campaign.status === 'sent' && (
                      <>
                        <span className="text-success">{campaign.openRate}% mở</span>
                        <span className="text-info ml-2">{campaign.clickRate}% click</span>
                      </>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {campaign.sentAt.toLocaleDateString('vi-VN')}
                  </div>
                </div>
              </div>
            </Card>
          ))}

          <div className="text-center pt-4">
            <Button variant="outline" onClick={() => navigate('/marketing/email/campaigns')}>
              {t('marketing:email.overview.viewAllCampaigns', 'Xem tất cả chiến dịch')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card
          title={t('marketing:email.overview.quickActions.templates.title', 'Quản lý Templates')}
          subtitle={t(
            'marketing:email.overview.quickActions.templates.subtitle',
            'Tạo và chỉnh sửa email templates'
          )}
          className="cursor-pointer hover:shadow-lg transition-shadow p-4"
          onClick={() => navigate('/marketing/email/templates')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              {t('marketing:email.overview.quickActions.viewDetails', 'Xem chi tiết')}
            </Button>
          </div>
        </Card>

        <Card
          title={t('marketing:email.overview.quickActions.automation.title', 'Email Automation')}
          subtitle={t(
            'marketing:email.overview.quickActions.automation.subtitle',
            'Thiết lập chuỗi email tự động'
          )}
          className="cursor-pointer hover:shadow-lg transition-shadow p-4"
          onClick={() => navigate('/marketing/email/automation')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              {t('marketing:email.overview.quickActions.viewDetails', 'Xem chi tiết')}
            </Button>
          </div>
        </Card>

        <Card
          title={t('marketing:email.overview.quickActions.analytics.title', 'Analytics')}
          subtitle={t(
            'marketing:email.overview.quickActions.analytics.subtitle',
            'Báo cáo hiệu quả email marketing'
          )}
          className="cursor-pointer hover:shadow-lg transition-shadow p-4"
          onClick={() => navigate('/marketing/email/analytics')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              {t('marketing:email.overview.quickActions.viewDetails', 'Xem chi tiết')}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default EmailOverviewPage;
