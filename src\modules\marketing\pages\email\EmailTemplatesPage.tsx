import React, { useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Mail,
  Edit,
  Send,
  FileText
} from 'lucide-react';
import {
  Card,
  Table,
  Chip,
  IconCard,
  Tooltip,
  Button,
  Typography,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useEmailTemplatesAdapter } from '../../hooks/email/useEmailTemplatesAdapter';
import { CreateEmailTemplateForm } from '../../components/email/CreateEmailTemplateForm';
import type { EmailTemplateQueryDto, EmailTemplateDto, EmailTemplateStatus } from '../../types/email.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';
import { formatTimestamp } from '@/shared/utils/date';

/**
 * Trang quản lý Email Templates
 */
export function EmailTemplatesPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = React.useState<EmailTemplateDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible: isCreateVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();
  const { isVisible: isPreviewVisible, showForm: showPreviewForm, hideForm: hidePreviewForm } = useSlideForm();

  // Kiểm tra nếu có action=create trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'create') {
      showCreateForm();
      setSearchParams({});
    }
  }, [searchParams, showCreateForm, setSearchParams]);

  const handlePreviewTemplate = useCallback((template: EmailTemplateDto) => {
    setSelectedTemplate(template);
    showPreviewForm();
  }, [showPreviewForm]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<EmailTemplateDto>[]>(
    () => [
      {
        key: 'template',
        title: t('marketing:email.templates.table.template', 'Template'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: EmailTemplateDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white">
              <Mail className="h-6 w-6" />
            </div>
            <div>
              <Typography variant="body2" className="font-medium">{String(value || '')}</Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {record.subject}
              </Typography>
            </div>
          </div>
        ),
      },
      {
        key: 'type',
        title: t('marketing:email.templates.table.type', 'Loại'),
        dataIndex: 'type',
        sortable: true,
        render: (value: unknown) => {
          const type = value as string;
          const typeVariants: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'> = {
            NEWSLETTER: 'info',
            PROMOTIONAL: 'warning',
            TRANSACTIONAL: 'success',
            WELCOME: 'primary',
            ABANDONED_CART: 'danger',
            FOLLOW_UP: 'default',
          };
          return (
            <Chip variant={typeVariants[type] || 'default'} size="sm">
              {type}
            </Chip>
          );
        },
      },
      {
        key: 'status',
        title: t('marketing:email.templates.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          switch (status) {
            case 'ACTIVE':
              return <Chip variant="success" size="sm">{t('marketing:email.templates.status.active', 'Hoạt động')}</Chip>;
            case 'DRAFT':
              return <Chip variant="warning" size="sm">{t('marketing:email.templates.status.draft', 'Bản nháp')}</Chip>;
            case 'ARCHIVED':
              return <Chip variant="info" size="sm">{t('marketing:email.templates.status.archived', 'Đã lưu trữ')}</Chip>;
            default:
              return <Chip variant="info" size="sm">{status}</Chip>;
          }
        },
      },
      {
        key: 'tags',
        title: t('marketing:email.templates.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags?.slice(0, 2).map((tag: string) => (
                <Chip key={tag} variant="info" size="sm">
                  {tag}
                </Chip>
              ))}
              {tags?.length > 2 && (
                <Chip variant="info" size="sm">
                  +{tags.length - 2}
                </Chip>
              )}
            </div>
          );
        },
      },
      {
        key: 'variables',
        title: t('marketing:email.templates.table.variables', 'Biến'),
        dataIndex: 'variables',
        render: (value: unknown) => {
          const variables = value as Array<{ name: string; type: string; required: boolean }>;
          return (
            <Typography variant="caption">
              {variables?.length || 0} biến
            </Typography>
          );
        },
      },
      {
        key: 'updatedAt',
        title: t('marketing:email.templates.table.updated', 'Cập nhật'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <Typography variant="caption" className="text-muted-foreground">
            {formatTimestamp(value, 'vi-VN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })}
          </Typography>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:email.templates.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: EmailTemplateDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:email.templates.preview.title', 'Xem trước Template')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => handlePreviewTemplate(record)}
              />
            </Tooltip>
            <Tooltip content={t('common.edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Edit template:', record.id);
                }}
              />
            </Tooltip>
            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => {
                  console.log('Delete template:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handlePreviewTemplate]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('marketing:email.templates.status.active', 'Hoạt động'),
        icon: 'check',
        value: 'ACTIVE',
      },
      {
        id: 'draft',
        label: t('marketing:email.templates.status.draft', 'Bản nháp'),
        icon: 'edit',
        value: 'DRAFT',
      },
      {
        id: 'archived',
        label: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
        icon: 'archive',
        value: 'ARCHIVED',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): EmailTemplateQueryDto => {
      const queryParams: EmailTemplateQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as EmailTemplateStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<EmailTemplateDto, EmailTemplateQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Stable query params để tránh gọi API nhiều lần
  const stableQueryParams = useMemo(() => {
    return {
      page: dataTable.queryParams.page || 1,
      limit: dataTable.queryParams.limit || 10,
      search: dataTable.queryParams.search || undefined,
      sortBy: dataTable.queryParams.sortBy || undefined,
      sortDirection: dataTable.queryParams.sortDirection || undefined,
      status: dataTable.queryParams.status || undefined,
    };
  }, [
    dataTable.queryParams.page,
    dataTable.queryParams.limit,
    dataTable.queryParams.search,
    dataTable.queryParams.sortBy,
    dataTable.queryParams.sortDirection,
    dataTable.queryParams.status,
  ]);

  // Debug logging
  console.log('📊 [EmailTemplatesPage] stableQueryParams:', stableQueryParams);

  // API call với stable query params
  const { data: templatesData, isLoading } = useEmailTemplatesAdapter(stableQueryParams);



  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ACTIVE: t('marketing:email.templates.status.active', 'Hoạt động'),
        DRAFT: t('marketing:email.templates.status.draft', 'Bản nháp'),
        ARCHIVED: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    setSearchParams({});
  };

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.totalTemplates', 'Tổng Templates')}
            </Typography>
            <FileText className="h-4 w-4 text-blue-600" />
          </div>
          <Typography variant="h2" className="text-blue-600">
            {templatesData?.meta.totalItems || 0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.newTemplates', '+3 template mới')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.active', 'Hoạt động')}
            </Typography>
            <Mail className="h-4 w-4 text-green-600" />
          </div>
          <Typography variant="h2" className="text-green-600">
            {templatesData?.items.filter((template: EmailTemplateDto) => template.status === 'ACTIVE').length || 0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.readyToUse', 'Sẵn sàng sử dụng')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.draft', 'Bản nháp')}
            </Typography>
            <Edit className="h-4 w-4 text-yellow-600" />
          </div>
          <Typography variant="h2" className="text-yellow-600">
            {templatesData?.items.filter((template: EmailTemplateDto) => template.status === 'DRAFT').length || 0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.incomplete', 'Chưa hoàn thành')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.testSent', 'Đã gửi test')}
            </Typography>
            <Send className="h-4 w-4 text-orange-600" />
          </div>
          <Typography variant="h2" className="text-orange-600">24</Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.thisWeek', 'Tuần này')}
          </Typography>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Templates Table */}
      <Card className="overflow-hidden">
        <Table<EmailTemplateDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={templatesData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: templatesData?.meta.currentPage || 1,
            pageSize: templatesData?.meta.itemsPerPage || 10,
            total: templatesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho tạo template */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h3">
              {t('marketing:email.templates.create.title', 'Tạo Email Template')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('marketing:email.templates.create.description', 'Tạo template email marketing mới')}
            </Typography>
          </div>
          <CreateEmailTemplateForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho preview template */}
      <SlideInForm isVisible={isPreviewVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h3">
              {t('marketing:email.templates.preview.title', 'Xem trước Template')}
            </Typography>
            {selectedTemplate && (
              <Typography variant="caption" className="text-muted-foreground mt-1">
                Template: {selectedTemplate.name}
              </Typography>
            )}
          </div>

          {selectedTemplate && (
            <div className="space-y-4">
              <div>
                <Typography variant="h4">{selectedTemplate.name}</Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  Subject: {selectedTemplate.subject}
                </Typography>
              </div>

              <div className="border rounded-lg p-4 bg-white">
                <div
                  dangerouslySetInnerHTML={{
                    __html: selectedTemplate.htmlContent || '<p>Không có nội dung preview</p>'
                  }}
                />
              </div>

              <div className="space-y-2">
                <Typography variant="body2" className="font-medium">
                  {t('marketing:email.templates.preview.variables', 'Biến template:')}
                </Typography>
                <div className="space-y-1">
                  {selectedTemplate.variables.map((variable, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <Typography variant="caption" className="font-mono">{variable.name}</Typography>
                      <div className="flex items-center gap-2">
                        <Chip variant="info" size="sm">
                          {variable.type}
                        </Chip>
                        {variable.required && (
                          <Chip variant="danger" size="sm">
                            {t('marketing:email.templates.preview.required', 'Bắt buộc')}
                          </Chip>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={hidePreviewForm}>
              {t('common.close', 'Đóng')}
            </Button>
          </div>
        </div>
      </SlideInForm>
    </div>
  );
}

export default EmailTemplatesPage;
