import React, { useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Mail,
  Edit,
  Send,
  FileText
} from 'lucide-react';
import {
  Card,
  Table,
  Badge,
  IconCard,
  Tooltip,
  Button,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useEmailTemplates } from '../../hooks/email/useEmailTemplates';
import { CreateEmailTemplateForm } from '../../components/email/CreateEmailTemplateForm';
import type { EmailTemplateQueryDto, EmailTemplateDto, EmailTemplateStatus } from '../../types/email.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý Email Templates
 */
export function EmailTemplatesPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = React.useState<EmailTemplateDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible: isCreateVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();
  const { isVisible: isPreviewVisible, showForm: showPreviewForm, hideForm: hidePreviewForm } = useSlideForm();

  // Kiểm tra nếu có action=create trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'create') {
      showCreateForm();
      setSearchParams({});
    }
  }, [searchParams, showCreateForm, setSearchParams]);

  const handlePreviewTemplate = useCallback((template: EmailTemplateDto) => {
    setSelectedTemplate(template);
    showPreviewForm();
  }, [showPreviewForm]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<EmailTemplateDto>[]>(
    () => [
      {
        key: 'template',
        title: t('marketing:email.templates.table.template', 'Template'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: EmailTemplateDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white">
              <Mail className="h-6 w-6" />
            </div>
            <div>
              <div className="font-medium">{String(value || '')}</div>
              <div className="text-sm text-muted-foreground">
                {record.subject}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: 'type',
        title: t('marketing:email.templates.table.type', 'Loại'),
        dataIndex: 'type',
        sortable: true,
        render: (value: unknown) => {
          const type = value as string;
          const typeColors: Record<string, string> = {
            NEWSLETTER: 'bg-blue-100 text-blue-800',
            PROMOTIONAL: 'bg-orange-100 text-orange-800',
            TRANSACTIONAL: 'bg-green-100 text-green-800',
            WELCOME: 'bg-purple-100 text-purple-800',
            ABANDONED_CART: 'bg-red-100 text-red-800',
            FOLLOW_UP: 'bg-gray-100 text-gray-800',
          };
          return (
            <Badge variant="info" className={typeColors[type] || 'bg-gray-100 text-gray-800'}>
              {type}
            </Badge>
          );
        },
      },
      {
        key: 'status',
        title: t('marketing:email.templates.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          switch (status) {
            case 'ACTIVE':
              return <Badge variant="success">{t('marketing:email.templates.status.active', 'Hoạt động')}</Badge>;
            case 'DRAFT':
              return <Badge variant="warning">{t('marketing:email.templates.status.draft', 'Bản nháp')}</Badge>;
            case 'ARCHIVED':
              return <Badge variant="info">{t('marketing:email.templates.status.archived', 'Đã lưu trữ')}</Badge>;
            default:
              return <Badge variant="info">{status}</Badge>;
          }
        },
      },
      {
        key: 'tags',
        title: t('marketing:email.templates.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags?.slice(0, 2).map((tag: string) => (
                <Badge key={tag} variant="info" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {tags?.length > 2 && (
                <Badge variant="info" className="text-xs">
                  +{tags.length - 2}
                </Badge>
              )}
            </div>
          );
        },
      },
      {
        key: 'variables',
        title: t('marketing:email.templates.table.variables', 'Biến'),
        dataIndex: 'variables',
        render: (value: unknown) => {
          const variables = value as Array<{ name: string; type: string; required: boolean }>;
          return (
            <div className="text-sm">
              {variables?.length || 0} biến
            </div>
          );
        },
      },
      {
        key: 'updatedAt',
        title: t('marketing:email.templates.table.updated', 'Cập nhật'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : ''}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:email.templates.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: EmailTemplateDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:email.templates.preview.title', 'Xem trước Template')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => handlePreviewTemplate(record)}
              />
            </Tooltip>
            <Tooltip content={t('common.edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Edit template:', record.id);
                }}
              />
            </Tooltip>
            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => {
                  console.log('Delete template:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handlePreviewTemplate]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('marketing:email.templates.status.active', 'Hoạt động'),
        icon: 'check',
        value: 'ACTIVE',
      },
      {
        id: 'draft',
        label: t('marketing:email.templates.status.draft', 'Bản nháp'),
        icon: 'edit',
        value: 'DRAFT',
      },
      {
        id: 'archived',
        label: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
        icon: 'archive',
        value: 'ARCHIVED',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): EmailTemplateQueryDto => {
      const queryParams: EmailTemplateQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as EmailTemplateStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<EmailTemplateDto, EmailTemplateQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // API call với query từ dataTable
  const { data: templatesData, isLoading } = useEmailTemplates(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng với API data
  useEffect(() => {
    updateTableDataRef.current(templatesData, isLoading);
  }, [templatesData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ACTIVE: t('marketing:email.templates.status.active', 'Hoạt động'),
        DRAFT: t('marketing:email.templates.status.draft', 'Bản nháp'),
        ARCHIVED: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    setSearchParams({});
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:email.templates.title', 'Email Templates')}
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.totalTemplates', 'Tổng Templates')}
            </span>
            <FileText className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {templatesData?.meta.totalItems || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.newTemplates', '+3 template mới')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.active', 'Hoạt động')}
            </span>
            <Mail className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">
            {templatesData?.items.filter((template: EmailTemplateDto) => template.status === 'ACTIVE').length || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.readyToUse', 'Sẵn sàng sử dụng')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.draft', 'Bản nháp')}
            </span>
            <Edit className="h-4 w-4 text-yellow-600" />
          </div>
          <div className="text-2xl font-bold text-yellow-600">
            {templatesData?.items.filter((template: EmailTemplateDto) => template.status === 'DRAFT').length || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.incomplete', 'Chưa hoàn thành')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.testSent', 'Đã gửi test')}
            </span>
            <Send className="h-4 w-4 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">24</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.thisWeek', 'Tuần này')}
          </p>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Templates Table */}
      <Card className="overflow-hidden">
        <Table<EmailTemplateDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={templatesData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: templatesData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: templatesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho tạo template */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:email.templates.create.title', 'Tạo Email Template')}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:email.templates.create.description', 'Tạo template email marketing mới')}
            </p>
          </div>
          <CreateEmailTemplateForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho preview template */}
      <SlideInForm isVisible={isPreviewVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:email.templates.preview.title', 'Xem trước Template')}
            </h3>
            {selectedTemplate && (
              <p className="text-sm text-muted-foreground mt-1">
                Template: {selectedTemplate.name}
              </p>
            )}
          </div>

          {selectedTemplate && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">{selectedTemplate.name}</h4>
                <p className="text-sm text-muted-foreground">
                  Subject: {selectedTemplate.subject}
                </p>
              </div>

              <div className="border rounded-lg p-4 bg-white">
                <div
                  dangerouslySetInnerHTML={{
                    __html: selectedTemplate.htmlContent || '<p>Không có nội dung preview</p>'
                  }}
                />
              </div>

              <div className="space-y-2">
                <h5 className="text-sm font-medium">
                  {t('marketing:email.templates.preview.variables', 'Biến template:')}
                </h5>
                <div className="space-y-1">
                  {selectedTemplate.variables.map((variable, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="font-mono">{variable.name}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="info" className="text-xs">
                          {variable.type}
                        </Badge>
                        {variable.required && (
                          <Badge variant="danger" className="text-xs">
                            {t('marketing:email.templates.preview.required', 'Bắt buộc')}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={hidePreviewForm}>
              {t('common.close', 'Đóng')}
            </Button>
          </div>
        </div>
      </SlideInForm>
    </div>
  );
}

export default EmailTemplatesPage;
