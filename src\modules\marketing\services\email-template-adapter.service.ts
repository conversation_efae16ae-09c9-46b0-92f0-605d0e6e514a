/**
 * Email Template Adapter Service
 * Bridges the gap between backend template-email API and frontend email template expectations
 */

import { TemplateEmailBusinessService } from './template-email-business.service';
import { TemplateEmail, TemplateEmailQueryParams } from '../types/template-email.types';
import { EmailTemplateDto, EmailTemplateQueryDto, EmailTemplateStatus, EmailTemplateType } from '../types/email.types';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Adapter service to convert between backend template-email and frontend email-template formats
 */
export class EmailTemplateAdapterService {
  /**
   * Convert backend TemplateEmail to frontend EmailTemplateDto
   */
  static convertToEmailTemplateDto(backendTemplate: TemplateEmail): EmailTemplateDto {
    return {
      id: backendTemplate.id.toString(),
      name: backendTemplate.name,
      subject: backendTemplate.subject,
      htmlContent: backendTemplate.content,
      textContent: undefined,
      type: EmailTemplateType.NEWSLETTER, // Default type since backend doesn't have this
      status: EmailTemplateStatus.ACTIVE, // Default status since backend doesn't have this
      previewText: undefined,
      thumbnailUrl: undefined,
      tags: backendTemplate.tags || [],
      variables: (backendTemplate.placeholders || []).map(placeholder => ({
        name: placeholder,
        type: 'TEXT' as const,
        defaultValue: '',
        required: false,
        description: `Variable: ${placeholder}`,
      })),
      createdAt: new Date(backendTemplate.createdAt),
      updatedAt: new Date(backendTemplate.updatedAt),
    };
  }

  /**
   * Convert frontend EmailTemplateQueryDto to backend TemplateEmailQueryParams
   */
  static convertToBackendQueryParams(frontendQuery: EmailTemplateQueryDto): TemplateEmailQueryParams {
    return {
      name: frontendQuery.search, // Backend uses 'name' for search
      page: frontendQuery.page,
      limit: frontendQuery.limit,
      sortBy: frontendQuery.sortBy,
      sortDirection: frontendQuery.sortDirection,
      // Note: Backend doesn't support type/status filtering, so we ignore these
    };
  }

  /**
   * Get email templates using backend API and convert to frontend format
   */
  static async getEmailTemplates(query?: EmailTemplateQueryDto): Promise<PaginatedResult<EmailTemplateDto>> {
    const backendQuery = query ? this.convertToBackendQueryParams(query) : {};
    const backendResult = await TemplateEmailBusinessService.getTemplateEmails(backendQuery);

    return {
      items: backendResult.items.map(item => this.convertToEmailTemplateDto(item)),
      meta: backendResult.meta,
    };
  }

  /**
   * Get email template by ID using backend API and convert to frontend format
   */
  static async getEmailTemplate(id: string): Promise<EmailTemplateDto> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    const backendTemplate = await TemplateEmailBusinessService.getTemplateEmailById(numericId);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Create email template using backend API
   */
  static async createEmailTemplate(data: {
    name: string;
    subject: string;
    htmlContent: string;
    tags?: string[];
    variables?: Array<{ name: string; type: string; required: boolean }>;
  }): Promise<EmailTemplateDto> {
    const backendData = {
      name: data.name,
      subject: data.subject,
      content: data.htmlContent,
      tags: data.tags || [],
      placeholders: data.variables?.map(v => v.name) || [],
    };

    const backendTemplate = await TemplateEmailBusinessService.createTemplateEmail(backendData);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Update email template using backend API
   */
  static async updateEmailTemplate(
    id: string,
    data: {
      name?: string;
      subject?: string;
      htmlContent?: string;
      tags?: string[];
      variables?: Array<{ name: string; type: string; required: boolean }>;
    }
  ): Promise<EmailTemplateDto> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    const backendData = {
      name: data.name,
      subject: data.subject,
      content: data.htmlContent,
      tags: data.tags,
      placeholders: data.variables?.map(v => v.name),
    };

    const backendTemplate = await TemplateEmailBusinessService.updateTemplateEmail(numericId, backendData);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Delete email template using backend API
   */
  static async deleteEmailTemplate(id: string): Promise<boolean> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    return TemplateEmailBusinessService.deleteTemplateEmail(numericId);
  }

  /**
   * Get email template statistics
   */
  static async getEmailTemplateStatistics(): Promise<{
    totalTemplates: number;
    activeTemplates: number;
    draftTemplates: number;
    recentTemplates: EmailTemplateDto[];
  }> {
    const backendStats = await TemplateEmailBusinessService.getTemplateStatistics();

    return {
      totalTemplates: backendStats.totalTemplates,
      activeTemplates: backendStats.activeTemplates,
      draftTemplates: backendStats.draftTemplates,
      recentTemplates: backendStats.recentTemplates.map(template =>
        this.convertToEmailTemplateDto(template)
      ),
    };
  }

  /**
   * Preview template with variables
   */
  static previewTemplate(content: string, variables: Record<string, string>): string {
    return TemplateEmailBusinessService.previewTemplate(content, variables);
  }

  /**
   * Validate template placeholders
   */
  static validateTemplatePlaceholders(content: string, placeholders: string[]): {
    isValid: boolean;
    missingPlaceholders: string[];
    unusedPlaceholders: string[];
  } {
    return TemplateEmailBusinessService.validateTemplatePlaceholders(content, placeholders);
  }
}
