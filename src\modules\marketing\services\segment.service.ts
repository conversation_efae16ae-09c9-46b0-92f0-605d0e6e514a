/**
 * Service for segment API
 */

import { apiClient } from '@/shared/api';
import {
  CreateSegmentRequest,
  SegmentDetailResponse,
  SegmentListResponse,
  SegmentQueryParams,
  SegmentStatsResponse,
  UpdateSegmentRequest,
} from '../types/segment.types';

/**
 * Base URL for segment API
 */
const BASE_URL = '/marketing/segments';

/**
 * Segment service
 */
export const SegmentService = {
  /**
   * Get segments with pagination and filtering
   */
  getSegments: async (params: SegmentQueryParams): Promise<SegmentListResponse> => {
    const response = await apiClient.get<SegmentListResponse['result']>(BASE_URL, { params });

    // Backend trả về array, chúng ta cần wrap thành paginated result
    if (Array.isArray(response.result)) {
      return {
        ...response,
        result: {
          items: response.result,
          meta: {
            currentPage: params.page || 1,
            totalItems: response.result.length,
            totalPages: 1,
            itemsPerPage: params.limit || 10,
            itemCount: response.result.length,
          }
        }
      };
    }

    return response;
  },

  /**
   * Get segment by ID
   */
  getSegmentById: async (id: number): Promise<SegmentDetailResponse> => {
    return apiClient.get<SegmentDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create segment
   */
  createSegment: async (data: CreateSegmentRequest): Promise<SegmentDetailResponse> => {
    return apiClient.post<SegmentDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update segment
   */
  updateSegment: async (id: number, data: UpdateSegmentRequest): Promise<SegmentDetailResponse> => {
    return apiClient.put<SegmentDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete segment
   */
  deleteSegment: async (id: number): Promise<{ success: boolean }> => {
    await apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
    return { success: true };
  },

  /**
   * Get segment stats
   */
  getSegmentStats: async (id: number): Promise<SegmentStatsResponse> => {
    return apiClient.get<SegmentStatsResponse['result']>(`${BASE_URL}/${id}/stats`);
  },
};
