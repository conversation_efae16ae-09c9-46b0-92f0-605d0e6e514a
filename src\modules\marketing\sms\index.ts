/**
 * SMS Marketing Module
 * 
 * Comprehensive SMS marketing solution with multi-provider support,
 * campaign management, template system, and analytics.
 */

// Types
export * from './types';

// Constants
export * from './constants/sms.constants';

// Components (will be added in future phases)
// export * from './components';

// Hooks (will be added in future phases)
// export * from './hooks';

// Services (will be added in future phases)
// export * from './services';

// Pages (will be added in future phases)
// export * from './pages';

// Locales
export { default as smsLocalesVi } from './locales/vi.json';
export { default as smsLocalesEn } from './locales/en.json';

/**
 * SMS Module Configuration
 */
export const SMS_MODULE_CONFIG = {
  name: 'SMS Marketing',
  version: '1.0.0',
  description: 'Comprehensive SMS marketing solution',
  features: [
    'Multi-provider SMS support',
    'Campaign management',
    'Template system',
    'Contact management',
    'Analytics and reporting',
    'Automation triggers',
    'A/B testing',
    'Compliance management',
  ],
  supportedProviders: [
    'Twilio',
    'AWS SNS',
    'Viettel SMS',
    'VNPT SMS',
    'FPT SMS',
    'Custom API',
  ],
  routes: {
    overview: '/marketing/sms',
    campaigns: '/marketing/sms/campaigns',
    templates: '/marketing/sms/templates',
    contacts: '/marketing/sms/contacts',
    analytics: '/marketing/sms/analytics',
    settings: '/marketing/sms/settings',
    integration: '/integrations/sms',
  },
} as const;

/**
 * SMS Module Metadata
 */
export const SMS_MODULE_METADATA = {
  id: 'sms-marketing',
  title: 'SMS Marketing',
  description: 'Manage SMS campaigns and messages',
  icon: 'message-square',
  category: 'marketing',
  status: 'development',
  priority: 'high',
  estimatedCompletion: '2024-02-15',
  dependencies: [
    'integration-module',
    'contact-management',
    'analytics-engine',
  ],
  team: {
    lead: 'SMS Team Lead',
    developers: ['Developer 1', 'Developer 2'],
    qa: ['QA Engineer'],
  },
} as const;

/**
 * SMS Module Navigation
 */
export const SMS_MODULE_NAVIGATION = [
  {
    id: 'sms-overview',
    title: 'Tổng quan',
    path: '/marketing/sms',
    icon: 'home',
    description: 'SMS Marketing overview and statistics',
  },
  {
    id: 'sms-campaigns',
    title: 'Chiến dịch',
    path: '/marketing/sms/campaigns',
    icon: 'send',
    description: 'Manage SMS campaigns',
  },
  {
    id: 'sms-templates',
    title: 'Mẫu tin',
    path: '/marketing/sms/templates',
    icon: 'file-text',
    description: 'Manage SMS templates',
  },
  {
    id: 'sms-contacts',
    title: 'Liên hệ',
    path: '/marketing/sms/contacts',
    icon: 'users',
    description: 'Manage contact lists',
  },
  {
    id: 'sms-analytics',
    title: 'Báo cáo',
    path: '/marketing/sms/analytics',
    icon: 'bar-chart-3',
    description: 'SMS analytics and reports',
  },
  {
    id: 'sms-settings',
    title: 'Cài đặt',
    path: '/marketing/sms/settings',
    icon: 'settings',
    description: 'SMS configuration and settings',
  },
] as const;

/**
 * SMS Module Permissions
 */
export const SMS_MODULE_PERMISSIONS = {
  // Overview
  VIEW_SMS_OVERVIEW: 'sms:overview:view',
  
  // Campaigns
  VIEW_SMS_CAMPAIGNS: 'sms:campaigns:view',
  CREATE_SMS_CAMPAIGNS: 'sms:campaigns:create',
  EDIT_SMS_CAMPAIGNS: 'sms:campaigns:edit',
  DELETE_SMS_CAMPAIGNS: 'sms:campaigns:delete',
  START_SMS_CAMPAIGNS: 'sms:campaigns:start',
  STOP_SMS_CAMPAIGNS: 'sms:campaigns:stop',
  
  // Templates
  VIEW_SMS_TEMPLATES: 'sms:templates:view',
  CREATE_SMS_TEMPLATES: 'sms:templates:create',
  EDIT_SMS_TEMPLATES: 'sms:templates:edit',
  DELETE_SMS_TEMPLATES: 'sms:templates:delete',
  APPROVE_SMS_TEMPLATES: 'sms:templates:approve',
  
  // Contacts
  VIEW_SMS_CONTACTS: 'sms:contacts:view',
  CREATE_SMS_CONTACTS: 'sms:contacts:create',
  EDIT_SMS_CONTACTS: 'sms:contacts:edit',
  DELETE_SMS_CONTACTS: 'sms:contacts:delete',
  IMPORT_SMS_CONTACTS: 'sms:contacts:import',
  EXPORT_SMS_CONTACTS: 'sms:contacts:export',
  
  // Providers
  VIEW_SMS_PROVIDERS: 'sms:providers:view',
  CREATE_SMS_PROVIDERS: 'sms:providers:create',
  EDIT_SMS_PROVIDERS: 'sms:providers:edit',
  DELETE_SMS_PROVIDERS: 'sms:providers:delete',
  TEST_SMS_PROVIDERS: 'sms:providers:test',
  
  // Analytics
  VIEW_SMS_ANALYTICS: 'sms:analytics:view',
  EXPORT_SMS_ANALYTICS: 'sms:analytics:export',
  
  // Settings
  VIEW_SMS_SETTINGS: 'sms:settings:view',
  EDIT_SMS_SETTINGS: 'sms:settings:edit',
  
  // Admin
  ADMIN_SMS_MODULE: 'sms:admin:all',
} as const;

/**
 * SMS Module Feature Flags
 */
export const SMS_MODULE_FEATURES = {
  // Core features
  CAMPAIGNS_ENABLED: true,
  TEMPLATES_ENABLED: true,
  CONTACTS_ENABLED: true,
  ANALYTICS_ENABLED: true,
  
  // Advanced features
  AB_TESTING_ENABLED: false, // Phase 4
  AUTOMATION_ENABLED: false, // Phase 4
  ADVANCED_SEGMENTATION_ENABLED: false, // Phase 4
  
  // Provider features
  TWILIO_ENABLED: true,
  AWS_SNS_ENABLED: true,
  VIETTEL_ENABLED: true,
  VNPT_ENABLED: true,
  FPT_ENABLED: true,
  CUSTOM_API_ENABLED: true,
  
  // Compliance features
  OPT_OUT_MANAGEMENT_ENABLED: true,
  GDPR_COMPLIANCE_ENABLED: true,
  AUDIT_LOGS_ENABLED: true,
  
  // Integration features
  WEBHOOK_ENABLED: false, // Phase 3
  API_ACCESS_ENABLED: false, // Phase 4
  THIRD_PARTY_INTEGRATION_ENABLED: false, // Phase 4
} as const;

/**
 * SMS Module Default Settings
 */
export const SMS_MODULE_DEFAULT_SETTINGS = {
  // General
  defaultLanguage: 'vi',
  defaultTimezone: 'Asia/Ho_Chi_Minh',
  defaultCurrency: 'USD',
  
  // Limits
  maxCampaignsPerUser: 100,
  maxTemplatesPerUser: 50,
  maxContactListsPerUser: 20,
  maxContactsPerList: 10000,
  maxSmsPerDay: 1000,
  maxSmsPerMonth: 30000,
  
  // Rate limiting
  defaultRateLimitPerSecond: 5,
  defaultRateLimitPerMinute: 100,
  defaultRateLimitPerHour: 1000,
  
  // Cost management
  defaultDailyBudget: 100, // USD
  defaultMonthlyBudget: 3000, // USD
  costAlertThreshold: 80, // Percentage
  
  // Compliance
  requireOptIn: true,
  optOutKeywords: ['STOP', 'UNSUBSCRIBE', 'HUY'],
  optInMessage: 'Reply YES to confirm subscription',
  optOutMessage: 'You have been unsubscribed. Reply START to resubscribe.',
  
  // Template defaults
  defaultTemplateCategory: 'marketing',
  maxTemplateLength: 160,
  maxVariablesPerTemplate: 10,
  
  // Campaign defaults
  defaultCampaignPriority: 'normal',
  maxRecipientsPerCampaign: 50000,
  defaultRetryAttempts: 3,
  retryDelayMinutes: 5,
} as const;
