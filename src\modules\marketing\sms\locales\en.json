{"sms": {"title": "SMS Marketing", "description": "Manage SMS campaigns and messages", "overview": {"title": "SMS Overview", "description": "SMS Marketing statistics and performance", "stats": {"totalSent": "Total Messages Sent", "totalDelivered": "Successfully Delivered", "totalFailed": "Failed to Send", "totalCost": "Total Cost", "deliveryRate": "Delivery Rate", "averageCost": "Average Cost", "activeProviders": "Active Providers", "activeCampaigns": "Active Campaigns"}, "quickActions": {"sendSms": "Send SMS", "createCampaign": "Create Campaign", "createTemplate": "Create Template", "manageContacts": "Manage Contacts"}}, "campaigns": {"title": "SMS Campaigns", "description": "Manage SMS campaigns", "create": "Create Campaign", "edit": "Edit Campaign", "delete": "Delete Campaign", "duplicate": "Duplicate Campaign", "start": "Start", "pause": "Pause", "stop": "Stop", "resume": "Resume", "viewDetails": "View Details", "viewAnalytics": "View Analytics", "empty": "No campaigns yet", "form": {"name": "Campaign Name", "namePlaceholder": "Enter campaign name", "description": "Description", "descriptionPlaceholder": "Brief description of the campaign", "type": "Campaign Type", "priority": "Priority", "template": "Message Template", "selectTemplate": "Select message template", "contactLists": "Contact Lists", "selectContactLists": "Select contact lists", "schedule": "Schedule", "sendNow": "Send Now", "sendLater": "Send Later", "scheduledAt": "Send Time", "provider": "SMS Provider", "selectProvider": "Select provider", "sender": "Sender", "senderPlaceholder": "Sender name or phone number"}, "status": {"draft": "Draft", "scheduled": "Scheduled", "sending": "Sending", "sent": "<PERSON><PERSON>", "paused": "Paused", "cancelled": "Cancelled", "failed": "Failed"}, "type": {"immediate": "Send Now", "scheduled": "Scheduled", "triggered": "Event Triggered", "recurring": "Recurring"}, "priority": {"low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>"}}, "templates": {"title": "Message Templates", "description": "Manage SMS message templates", "create": "Create Template", "edit": "Edit Template", "delete": "Delete Template", "duplicate": "Duplicate Template", "preview": "Preview", "validate": "Validate", "empty": "No templates yet", "form": {"name": "Template Name", "namePlaceholder": "Enter template name", "description": "Description", "descriptionPlaceholder": "Brief description of the template", "category": "Category", "selectCategory": "Select category", "content": "Message Content", "contentPlaceholder": "Enter message content...", "variables": "Variables", "addVariable": "Add Variable", "variableName": "Variable Name", "variableDescription": "Variable Description", "variableType": "Variable Type", "variableRequired": "Required", "variableDefault": "Default Value", "tags": "Tags", "tagsPlaceholder": "Enter tags and press Enter", "language": "Language", "isDefault": "<PERSON><PERSON><PERSON>"}, "category": {"marketing": "Marketing", "transactional": "Transactional", "reminder": "Reminder", "alert": "<PERSON><PERSON>", "otp": "OTP", "notification": "Notification", "welcome": "Welcome", "promotional": "Promotional"}, "status": {"draft": "Draft", "active": "Active", "inactive": "Inactive", "archived": "Archived", "pending_approval": "Pending Approval", "rejected": "Rejected"}, "validation": {"characterCount": "Character Count", "smsCount": "SMS Count", "containsUnicode": "Contains Unicode", "missingVariables": "Missing Variables", "unusedVariables": "Unused Variables", "tooLong": "Message Too Long", "invalidVariables": "Invalid Variables"}}, "providers": {"title": "SMS Providers", "description": "Manage SMS service providers", "add": "Add Provider", "edit": "Edit", "delete": "Delete", "test": {"title": "Test Provider", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number to test", "message": "Test Message", "messagePlaceholder": "Enter test message", "send": "Send Test", "success": "Test Successful", "failed": "Test Failed", "pending": "Testing..."}, "configure": "Configure", "activate": "Activate", "deactivate": "Deactivate", "empty": "No providers yet", "form": {"name": "Provider Name", "namePlaceholder": "Enter provider name", "type": "Provider Type", "selectType": "Select provider type", "description": "Description", "descriptionPlaceholder": "Description about the provider", "credentials": "Credentials", "apiKey": "API Key", "apiSecret": "API Secret", "accountSid": "Account SID", "authToken": "<PERSON><PERSON>", "region": "Region", "endpoint": "Endpoint", "username": "Username", "password": "Password", "settings": "Settings", "defaultSender": "<PERSON><PERSON><PERSON>", "maxLength": "Max Length", "supportUnicode": "Support Unicode", "supportDeliveryReports": "Support Delivery Reports", "costPerSms": "Cost per SMS", "currency": "<PERSON><PERSON><PERSON><PERSON>", "priority": "Priority", "rateLimits": "Rate Limits", "perSecond": "Per Second", "perMinute": "Per <PERSON>", "perHour": "Per Hour", "perDay": "Per Day"}, "types": {"twilio": "<PERSON><PERSON><PERSON>", "aws-sns": "AWS SNS", "viettel": "Viettel SMS", "vnpt": "VNPT SMS", "fpt": "FPT SMS", "custom": "Custom API"}, "status": {"active": "Active", "inactive": "Inactive", "error": "Error", "testing": "Testing"}}, "contacts": {"title": "Contact Lists", "description": "Manage contact lists and groups", "create": "Create List", "edit": "Edit", "delete": "Delete", "import": {"title": "Import Contact List", "selectFile": "Select File", "supportedFormats": "Supported: CSV, Excel", "mapping": "Column Mapping", "preview": "Preview", "import": "Import Data", "success": "Import Successful", "failed": "Import Failed"}, "export": "Export File", "addContact": "Add Contact", "removeContact": "Remove Contact", "empty": "No contact lists yet", "form": {"listName": "List Name", "listNamePlaceholder": "Enter list name", "description": "Description", "descriptionPlaceholder": "Description about the list", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "name": "Full Name", "namePlaceholder": "Enter full name", "email": "Email", "emailPlaceholder": "Enter email", "firstName": "First Name", "lastName": "Last Name", "country": "Country", "timezone": "Timezone", "language": "Language", "tags": "Tags", "customFields": "Custom Fields"}}, "analytics": {"title": "Reports & Analytics", "description": "SMS Marketing performance statistics", "overview": "Overview", "campaigns": "By Campaign", "providers": "By Provider", "geographic": "By Geography", "timeRange": "Time Range", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "customRange": "Custom Range", "metrics": {"deliveryRate": "Delivery Rate", "clickRate": "Click Rate", "unsubscribeRate": "Unsubscribe Rate", "costPerSms": "Cost per SMS", "roi": "ROI", "conversionRate": "Conversion Rate"}}, "settings": {"title": "SMS Settings", "description": "General configuration for SMS Marketing", "general": "General", "compliance": "Compliance", "rateLimiting": "Rate Limiting", "costManagement": "Cost Management", "form": {"defaultProvider": "<PERSON><PERSON><PERSON> Provider", "defaultSender": "<PERSON><PERSON><PERSON>", "enableDeliveryReports": "Enable Delivery Reports", "enableClickTracking": "Enable Click Tracking", "enableOptOutManagement": "Enable Opt-out Management", "maxDailyLimit": "Daily Limit", "maxMonthlyLimit": "Monthly Limit", "timezone": "Timezone", "language": "Language", "requireOptIn": "Require Opt-in", "optOutKeywords": "Opt-out Keywords", "optInMessage": "Opt-in Message", "optOutMessage": "Opt-out Message", "dailyBudgetLimit": "Daily Budget Limit", "monthlyBudgetLimit": "Monthly Budget Limit", "costAlertThreshold": "Cost <PERSON><PERSON>"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "loading": "Loading...", "noData": "No data", "error": "An error occurred", "success": "Success", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "selectAll": "Select All", "deselectAll": "Deselect All", "actions": "Actions", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "total": "Total", "showing": "Showing", "of": "of", "items": "items"}, "errors": {"invalidPhoneNumber": "Invalid phone number", "invalidMessageContent": "Invalid message content", "providerNotAvailable": "Provider not available", "rateLimitExceeded": "Rate limit exceeded", "insufficientBalance": "Insufficient balance", "templateNotFound": "Temp<PERSON> not found", "campaignNotFound": "Campaign not found", "contactOptedOut": "Contact has opted out", "messageTooLong": "Message too long", "invalidSenderId": "Invalid sender ID"}}}