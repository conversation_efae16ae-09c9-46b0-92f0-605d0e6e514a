/**
 * Export tất cả types của module marketing
 */

// Export PagingResponseDto từ một file duy nhất để tránh trùng lặp
export type { PagingResponseDto } from './google-ads.types';

// Google Ads types
export * from './google-ads.types';

// Facebook Ads types
export type {
  FacebookAdsAccountDto,
  FacebookAdsCampaignDto,
  FacebookAdsAdSetDto,
  FacebookAdsAdDto,
  FacebookAdsInsightDto,
  FacebookAdsAccountQueryDto,
  FacebookAdsCampaignQueryDto,
  FacebookAdsInsightQueryDto,
  CreateFacebookAdsAccountDto,
  UpdateFacebookAdsAccountDto,
  CreateFacebookAdsCampaignDto,
  UpdateFacebookAdsCampaignDto,
} from './facebook-ads.types';
export {
  FacebookAdsAccountStatus,
  FacebookAdsCampaignObjective,
  FacebookAdsCampaignStatus,
  FacebookAdsFormat,
} from './facebook-ads.types';

// SMS Marketing types
export type {
  SmsBrandnameDto,
  SmsTemplateDto,
  SmsMessageDto,
  SmsCampaignDto,
  SmsBrandnameQueryDto,
  SmsTemplateQueryDto,
  SmsMessageQueryDto,
  SmsCampaignQueryDto,
  CreateSmsBrandnameDto,
  UpdateSmsBrandnameDto,
  CreateSmsTemplateDto,
  UpdateSmsTemplateDto,
  SendSmsMessageDto,
  SendBulkSmsMessageDto,
  CreateSmsCampaignDto,
  UpdateSmsCampaignDto,
} from './sms.types';
export {
  SmsBrandnameStatus,
  SmsBrandnameType,
  SmsTemplateStatus,
  SmsMessageStatus,
  SmsCampaignStatus,
} from './sms.types';

// Zalo OA/ZNS types
export type {
  ZaloOAAccountDto,
  ZaloFollowerDto,
  ZaloMessageDto,
  ZaloAttachmentDto,
  ZNSTemplateDto,
  ZNSMessageDto,
  ZNSCampaignDto,
  ZaloOAAccountQueryDto,
  ZaloFollowerQueryDto,
  ZaloMessageQueryDto,
  ZNSTemplateQueryDto,
  CreateZaloOAAccountDto,
  UpdateZaloOAAccountDto,
  CreateZNSTemplateDto,
  SendZNSMessageDto,
  CreateZNSCampaignDto,
} from './zalo.types';
export {
  ZaloOAStatus,
  ZNSTemplateStatus,
  ZNSMessageStatus,
  ZaloMessageType,
} from './zalo.types';

// TikTok Ads types
export type {
  TikTokAdsAccountDto,
  TikTokAdsCampaignDto,
  TikTokAdsAdGroupDto,
  TikTokAdsCreativeDto,
  TikTokAdsAudienceDto,
  TikTokAdsTargeting,
  TikTokAdsAccountQueryDto,
  TikTokAdsCampaignQueryDto,
  TikTokAdsAdGroupQueryDto,
  TikTokAdsCreativeQueryDto,
  TikTokAdsAudienceQueryDto,
  CreateTikTokAdsAccountDto,
  UpdateTikTokAdsAccountDto,
  CreateTikTokAdsCampaignDto,
  UpdateTikTokAdsCampaignDto,
  CreateTikTokAdsCreativeDto,
  UpdateTikTokAdsCreativeDto,
  CreateTikTokAdsAudienceDto,
  UpdateTikTokAdsAudienceDto,
  TikTokAdsAccountResponse,
  TikTokAdsCampaignResponse,
  TikTokAdsCreativeResponse,
  TikTokAdsAudienceResponse,
} from './tiktok-ads.types';
export {
  TikTokAdsAccountStatus,
  TikTokAdsCampaignStatus,
  TikTokAdsCampaignObjective,
  TikTokAdsAdGroupStatus,
  TikTokAdsCreativeStatus,
  TikTokAdsCreativeType,
  TikTokAdsAudienceStatus,
} from './tiktok-ads.types';

// Các types khác sẽ được thêm vào sau
export * from './audience.types';
export * from './segment.types';
export * from './campaign.types';
export * from './tag.types';
export * from './custom-field.types';
