/**
 * Types for template email API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Template email status enum
 */
export enum TemplateEmailStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Template email type enum
 */
export enum TemplateEmailType {
  WELCOME = 'welcome',
  NEWSLETTER = 'newsletter',
  PROMOTION = 'promotion',
  NOTIFICATION = 'notification',
  TRANSACTIONAL = 'transactional',
  CUSTOM = 'custom',
}

/**
 * Template email entity - matches backend TemplateEmailResponseDto
 */
export interface TemplateEmail {
  id: number;
  userId: number;
  name: string;
  subject: string;
  content: string;
  tags: string[];
  placeholders: string[];
  createdAt: number; // Unix timestamp
  updatedAt: number; // Unix timestamp
}

/**
 * Create template email request - matches backend CreateTemplateEmailDto
 */
export interface CreateTemplateEmailRequest {
  name: string;
  description?: string;
  subject: string;
  content: string;
  tags?: string[];
  placeholders?: string[];
}

/**
 * Update template email request - matches backend UpdateTemplateEmailDto
 */
export interface UpdateTemplateEmailRequest {
  name?: string;
  subject?: string;
  content?: string;
  tags?: string[];
  placeholders?: string[];
}

/**
 * Template email response
 */
export type TemplateEmailResponse = TemplateEmail;

/**
 * Template email list response
 */
export type TemplateEmailListResponse = ApiResponseDto<PaginatedResult<TemplateEmailResponse>>;

/**
 * Template email detail response
 */
export type TemplateEmailDetailResponse = ApiResponseDto<TemplateEmailResponse>;

/**
 * Template email query params - matches backend TemplateEmailQueryDto
 */
export interface TemplateEmailQueryParams {
  name?: string; // Backend uses 'name' instead of 'search'
  search?: string; // For frontend compatibility
  tag?: string;
  status?: TemplateEmailStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}
