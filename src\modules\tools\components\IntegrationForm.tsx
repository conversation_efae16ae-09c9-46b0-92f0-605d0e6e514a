import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Input,
  Textarea,
  Select,
  FormItem,
  Card,
} from '@/shared/components/common';
import {
  AuthType,
  ApiKeyLocation,
  TokenSource,
  IntegrationToolDetail,
  AuthConfig,
  ApiKeyAuthConfig,
  OAuthAuthConfig,
  NoAuthConfig,
} from '../integration/types/integration.types';
import {
  IntegrateFromOpenApiParams,
  UpdateBaseUrlParams,
  UpdateToolAuthParams,
} from '../integration/schemas/integration.schema';

interface IntegrationFormProps {
  initialValues?: IntegrationToolDetail;
  onSubmit: (values: IntegrateFromOpenApiParams | UpdateBaseUrlParams | UpdateToolAuthParams) => void;
  onCancel: () => void;
  readOnly?: boolean;
  isLoading?: boolean;
  isEdit?: boolean;
}

/**
 * Component form để tạo/chỉnh sửa integration
 */
const IntegrationForm: React.FC<IntegrationFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  readOnly = false,
  isLoading = false,
  isEdit = false,
}) => {
  const { t } = useTranslation(['tools']);

  // State cho form
  const [name, setName] = useState(initialValues?.name || '');
  const [description, setDescription] = useState(initialValues?.description || '');
  const [baseUrl, setBaseUrl] = useState(initialValues?.baseUrl || '');
  const [openapiSpec, setOpenapiSpec] = useState<Record<string, unknown>>(
    initialValues?.openapiSpec || {}
  );
  const [authConfig, setAuthConfig] = useState<AuthConfig>(
    initialValues?.authConfig || { authType: AuthType.NONE }
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Cập nhật state khi có initialValues
  useEffect(() => {
    if (initialValues) {
      setName(initialValues.name || '');
      setDescription(initialValues.description || '');
      setBaseUrl(initialValues.baseUrl || '');
      setOpenapiSpec(initialValues.openapiSpec || {});
      setAuthConfig(initialValues.authConfig || { authType: AuthType.NONE });
    }
  }, [initialValues]);

  // Xử lý thay đổi OpenAPI spec
  const handleOpenapiSpecChange = (value: string) => {
    try {
      const parsed = JSON.parse(value);
      setOpenapiSpec(parsed);
      if (errors.openapiSpec) {
        setErrors(prev => ({ ...prev, openapiSpec: '' }));
      }
    } catch {
      setErrors(prev => ({
        ...prev,
        openapiSpec: t('tools:integration.validation.invalidJson', 'Invalid JSON format'),
      }));
    }
  };

  // Xử lý thay đổi auth config
  const handleAuthTypeChange = (authType: AuthType) => {
    switch (authType) {
      case AuthType.API_KEY:
        setAuthConfig({
          authType: AuthType.API_KEY,
          schemeName: '',
          apiKey: '',
          apiKeyLocation: ApiKeyLocation.HEADER,
          paramName: '',
        } as ApiKeyAuthConfig);
        break;
      case AuthType.OAUTH:
        setAuthConfig({
          authType: AuthType.OAUTH,
          schemeName: '',
          token: '',
          tokenSource: TokenSource.HEADER,
        } as OAuthAuthConfig);
        break;
      default:
        setAuthConfig({ authType: AuthType.NONE } as NoAuthConfig);
        break;
    }
  };

  // Xử lý thay đổi API Key config
  const handleApiKeyConfigChange = (field: keyof ApiKeyAuthConfig, value: unknown) => {
    if (authConfig.authType === AuthType.API_KEY) {
      setAuthConfig(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Xử lý thay đổi OAuth config
  const handleOAuthConfigChange = (field: keyof OAuthAuthConfig, value: unknown) => {
    if (authConfig.authType === AuthType.OAUTH) {
      setAuthConfig(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!isEdit) {
      if (Object.keys(openapiSpec).length === 0) {
        newErrors.openapiSpec = t(
          'tools:integration.validation.openapiRequired',
          'OpenAPI specification is required'
        );
      }
    }

    if (baseUrl && !/^https?:\/\/.+/.test(baseUrl)) {
      newErrors.baseUrl = t('tools:integration.validation.invalidUrl', 'Invalid URL format');
    }

    // Validate auth config
    if (authConfig.authType === AuthType.API_KEY) {
      const apiKeyConfig = authConfig as ApiKeyAuthConfig;
      if (!apiKeyConfig.apiKey) {
        newErrors.apiKey = t('tools:integration.validation.apiKeyRequired', 'API Key is required');
      }
      if (!apiKeyConfig.paramName) {
        newErrors.paramName = t(
          'tools:integration.validation.paramNameRequired',
          'Parameter name is required'
        );
      }
    } else if (authConfig.authType === AuthType.OAUTH) {
      const oauthConfig = authConfig as OAuthAuthConfig;
      if (!oauthConfig.token) {
        newErrors.token = t('tools:integration.validation.tokenRequired', 'Token is required');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Xử lý submit form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isEdit && initialValues) {
      // Cập nhật base URL
      if (baseUrl !== initialValues.baseUrl) {
        const updateData: UpdateBaseUrlParams = {
          toolId: initialValues.id,
          baseUrl,
        };
        onSubmit(updateData);
      } else {
        // Cập nhật auth config
        const updateData: UpdateToolAuthParams = {
          toolId: initialValues.id,
          authConfig,
        };
        onSubmit(updateData);
      }
    } else {
      // Tạo mới integration
      const createData: IntegrateFromOpenApiParams = {
        openapiSpec,
        baseUrl: baseUrl || undefined,
        authConfig: authConfig.authType !== AuthType.NONE ? authConfig : undefined,
      };
      onSubmit(createData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <Typography variant="h5">
          {readOnly
            ? t('tools:integration.viewIntegration', 'View Integration')
            : isEdit
              ? t('tools:integration.editIntegration', 'Edit Integration')
              : t('tools:integration.createIntegration', 'Create Integration')}
        </Typography>
      </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle2" className="text-blue-600 dark:text-blue-400">
            {t('tools:integration.basicInfo', 'Basic Information')}
          </Typography>

          <FormItem label={t('tools:integration.name', 'Name')}>
            <Input
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder={t('tools:integration.namePlaceholder', 'Enter integration name')}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          <FormItem label={t('tools:integration.description', 'Description')}>
            <Textarea
              value={description || ''}
              onChange={e => setDescription(e.target.value)}
              placeholder={t(
                'tools:integration.descriptionPlaceholder',
                'Enter integration description'
              )}
              disabled={readOnly || isLoading}
              rows={3}
            />
          </FormItem>

          <FormItem
            label={t('tools:integration.baseUrl', 'Base URL')}
            helpText={errors.baseUrl}
          >
            <Input
              value={baseUrl}
              onChange={e => {
                setBaseUrl(e.target.value);
                if (errors.baseUrl) setErrors(prev => ({ ...prev, baseUrl: '' }));
              }}
              placeholder={t('tools:integration.baseUrlPlaceholder', 'https://api.example.com')}
              disabled={readOnly || isLoading}
            />
          </FormItem>
        </div>

        {/* OpenAPI Specification - Only for create */}
        {!isEdit && (
          <div className="space-y-4">
            <Typography variant="subtitle2" className="text-green-600 dark:text-green-400">
              {t('tools:integration.openapiSpec', 'OpenAPI Specification')}
            </Typography>

            <FormItem
              label={t('tools:integration.openapiSpec', 'OpenAPI Specification')}
              helpText={errors.openapiSpec}
              required
            >
              <Textarea
                value={JSON.stringify(openapiSpec, null, 2)}
                onChange={e => handleOpenapiSpecChange(e.target.value)}
                placeholder={t(
                  'tools:integration.openapiPlaceholder',
                  'Paste your OpenAPI specification in JSON format'
                )}
                disabled={readOnly || isLoading}
                rows={15}
                className="font-mono text-sm"
              />
            </FormItem>
          </div>
        )}

        {/* Authentication */}
        <div className="space-y-4">
          <Typography variant="subtitle2" className="text-purple-600 dark:text-purple-400">
            {t('tools:integration.authentication', 'Authentication')}
          </Typography>

          <FormItem label={t('tools:integration.authType', 'Authentication Type')}>
            <Select
              value={authConfig.authType}
              onChange={value => handleAuthTypeChange(value as AuthType)}
              options={[
                { value: AuthType.NONE, label: t('tools:integration.auth.none', 'No Authentication') },
                { value: AuthType.API_KEY, label: t('tools:integration.auth.apiKey', 'API Key') },
                { value: AuthType.OAUTH, label: t('tools:integration.auth.oauth', 'OAuth') },
              ]}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          {authConfig.authType === AuthType.API_KEY && (
            <Card className="p-4 space-y-4">
              <Typography variant="subtitle2">
                {t('tools:integration.apiKeyConfig', 'API Key Configuration')}
              </Typography>

              <FormItem
                label={t('tools:integration.apiKey', 'API Key')}
                helpText={errors.apiKey}
                required
              >
                <Input
                  type="password"
                  value={(authConfig as ApiKeyAuthConfig).apiKey}
                  onChange={e => handleApiKeyConfigChange('apiKey', e.target.value)}
                  placeholder={t('tools:integration.apiKeyPlaceholder', 'Enter your API key')}
                  disabled={readOnly || isLoading}
                />
              </FormItem>

              <FormItem label={t('tools:integration.apiKeyLocation', 'Location')}>
                <Select
                  value={(authConfig as ApiKeyAuthConfig).apiKeyLocation}
                  onChange={value => handleApiKeyConfigChange('apiKeyLocation', value)}
                  options={[
                    { value: ApiKeyLocation.HEADER, label: 'Header' },
                    { value: ApiKeyLocation.QUERY, label: 'Query Parameter' },
                    { value: ApiKeyLocation.COOKIE, label: 'Cookie' },
                  ]}
                  disabled={readOnly || isLoading}
                />
              </FormItem>

              <FormItem
                label={t('tools:integration.paramName', 'Parameter Name')}
                helpText={errors.paramName}
                required
              >
                <Input
                  value={(authConfig as ApiKeyAuthConfig).paramName}
                  onChange={e => handleApiKeyConfigChange('paramName', e.target.value)}
                  placeholder={t('tools:integration.paramNamePlaceholder', 'e.g., X-API-Key')}
                  disabled={readOnly || isLoading}
                />
              </FormItem>
            </Card>
          )}

          {authConfig.authType === AuthType.OAUTH && (
            <Card className="p-4 space-y-4">
              <Typography variant="subtitle2">
                {t('tools:integration.oauthConfig', 'OAuth Configuration')}
              </Typography>

              <FormItem
                label={t('tools:integration.token', 'Token')}
                helpText={errors.token}
                required
              >
                <Input
                  type="password"
                  value={(authConfig as OAuthAuthConfig).token}
                  onChange={e => handleOAuthConfigChange('token', e.target.value)}
                  placeholder={t('tools:integration.tokenPlaceholder', 'Enter your OAuth token')}
                  disabled={readOnly || isLoading}
                />
              </FormItem>

              <FormItem label={t('tools:integration.tokenSource', 'Token Source')}>
                <Select
                  value={(authConfig as OAuthAuthConfig).tokenSource}
                  onChange={value => handleOAuthConfigChange('tokenSource', value)}
                  options={[
                    { value: TokenSource.HEADER, label: 'Header' },
                    { value: TokenSource.QUERY, label: 'Query Parameter' },
                    { value: TokenSource.BODY, label: 'Request Body' },
                  ]}
                  disabled={readOnly || isLoading}
                />
              </FormItem>
            </Card>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          {t('common.cancel', 'Cancel')}
        </Button>
        {!readOnly && (
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading
              ? t('common.processing', 'Processing...')
              : isEdit
                ? t('common.update', 'Update')
                : t('common.create', 'Create')}
          </Button>
        )}
      </div>
    </form>
  );
};

export default IntegrationForm;
