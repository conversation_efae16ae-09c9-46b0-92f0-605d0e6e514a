import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { integrationService } from '../services/integration.service';
import {
  BaseQueryParams,
} from '../types/integration.types';
import {
  integrationQuerySchema,
  IntegrateFromOpenApiParams,
  UpdateBaseUrlParams,
  UpdateToolAuthParams,
} from '../schemas/integration.schema';

// Query keys
export const INTEGRATION_QUERY_KEYS = {
  all: ['integrations'] as const,
  lists: () => [...INTEGRATION_QUERY_KEYS.all, 'list'] as const,
  list: (params: BaseQueryParams) => [...INTEGRATION_QUERY_KEYS.lists(), params] as const,
  details: () => [...INTEGRATION_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...INTEGRATION_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách integration tools
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useIntegrationTools = (params?: BaseQueryParams) => {
  // Validate và transform params
  const validParams = params ? integrationQuerySchema.parse(params) : undefined;

  return useQuery({
    queryKey: INTEGRATION_QUERY_KEYS.list(validParams || {}),
    queryFn: () => integrationService.getCustomTools(validParams || {}),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết integration tool
 * @param toolId ID của tool
 * @returns Query object
 */
export const useIntegrationToolDetail = (toolId?: string) => {
  return useQuery({
    queryKey: INTEGRATION_QUERY_KEYS.detail(toolId || ''),
    queryFn: () => integrationService.getCustomToolById(toolId || ''),
    enabled: !!toolId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tích hợp từ OpenAPI
 * @returns Mutation object
 */
export const useIntegrateFromOpenApi = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IntegrateFromOpenApiParams) =>
      integrationService.integrateFromOpenApi(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa integration tool
 * @returns Mutation object
 */
export const useDeleteIntegrationTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (toolId: string) => integrationService.deleteCustomTool(toolId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật base URL
 * @returns Mutation object
 */
export const useUpdateBaseUrl = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateBaseUrlParams) => integrationService.updateBaseUrl(params),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: INTEGRATION_QUERY_KEYS.detail(variables.toolId)
      });
    },
  });
};

/**
 * Hook để cập nhật auth config
 * @returns Mutation object
 */
export const useUpdateToolAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateToolAuthParams) => integrationService.updateToolAuth(params),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: INTEGRATION_QUERY_KEYS.detail(variables.toolId)
      });
    },
  });
};

/**
 * Hook để toggle status của tool
 * @returns Mutation object
 */
export const useToggleIntegrationToolStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (toolId: string) => integrationService.toggleToolStatus(toolId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: INTEGRATION_QUERY_KEYS.detail(data.id)
      });
    },
  });
};

/**
 * Hook để test connection
 * @returns Mutation object
 */
export const useTestConnection = () => {
  return useMutation({
    mutationFn: (toolId: string) => integrationService.testConnection(toolId),
  });
};

/**
 * Hook để refresh OpenAPI spec
 * @returns Mutation object
 */
export const useRefreshOpenApiSpec = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (toolId: string) => integrationService.refreshOpenApiSpec(toolId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: INTEGRATION_QUERY_KEYS.detail(data.id)
      });
    },
  });
};

/**
 * Hook để export configuration
 * @returns Mutation object
 */
export const useExportConfiguration = () => {
  return useMutation({
    mutationFn: (toolId: string) => integrationService.exportConfiguration(toolId),
  });
};

/**
 * Hook để import configuration
 * @returns Mutation object
 */
export const useImportConfiguration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (config: Record<string, unknown>) => integrationService.importConfiguration(config),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
    },
  });
};
