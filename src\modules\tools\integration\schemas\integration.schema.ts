import * as z from 'zod';
import { AuthType, ApiKeyLocation, TokenSource, IntegrationStatus } from '../types/integration.types';

/**
 * Schema cho cấu hình xác thực API Key
 */
export const apiKeyAuthConfigSchema = z.object({
  authType: z.literal(AuthType.API_KEY),
  schemeName: z.string().min(1, 'Scheme name is required'),
  apiKey: z.string().min(1, 'API Key is required'),
  apiKeyLocation: z.nativeEnum(ApiKeyLocation),
  paramName: z.string().min(1, 'Parameter name is required'),
});

/**
 * Schema cho cấu hình xác thực OAuth
 */
export const oauthAuthConfigSchema = z.object({
  authType: z.literal(AuthType.OAUTH),
  schemeName: z.string().min(1, 'Scheme name is required'),
  token: z.string().min(1, 'Token is required'),
  tokenSource: z.nativeEnum(TokenSource),
});

/**
 * Schema cho cấu hình không xác thực
 */
export const noAuthConfigSchema = z.object({
  authType: z.literal(AuthType.NONE),
});

/**
 * Schema cho cấu hình xác thực
 */
export const authConfigSchema = z.discriminatedUnion('authType', [
  apiKeyAuthConfigSchema,
  oauthAuthConfigSchema,
  noAuthConfigSchema,
]);

/**
 * Schema cho tham số truy vấn danh sách integration
 */
export const integrationQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC'),
  status: z.nativeEnum(IntegrationStatus).optional(),
  authType: z.nativeEnum(AuthType).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

/**
 * Schema cho tham số tích hợp từ OpenAPI
 */
export const integrateFromOpenApiSchema = z.object({
  openapiSpec: z.record(z.any()).refine(
    (spec) => {
      // Validate basic OpenAPI structure
      return spec && typeof spec === 'object' && (spec.openapi || spec.swagger);
    },
    {
      message: 'Invalid OpenAPI specification format',
    }
  ),
  baseUrl: z.string().url('Invalid URL format').optional(),
  authConfig: authConfigSchema.optional(),
});

/**
 * Schema cho tham số cập nhật base URL
 */
export const updateBaseUrlSchema = z.object({
  toolId: z.string().min(1, 'Tool ID is required'),
  baseUrl: z.string().url('Invalid URL format'),
});

/**
 * Schema cho tham số cập nhật xác thực
 */
export const updateToolAuthSchema = z.object({
  toolId: z.string().min(1, 'Tool ID is required'),
  authConfig: authConfigSchema,
});

/**
 * Schema cho form tạo integration
 */
export const createIntegrationFormSchema = z.object({
  name: z.string().min(1, 'Integration name is required').optional(),
  description: z.string().optional(),
  baseUrl: z.string().url('Invalid URL format').optional(),
  openapiSpec: z.record(z.any()).refine(
    (spec) => {
      return spec && typeof spec === 'object' && (spec.openapi || spec.swagger);
    },
    {
      message: 'Invalid OpenAPI specification format',
    }
  ),
  authConfig: authConfigSchema.optional(),
});

/**
 * Schema cho form cập nhật integration
 */
export const updateIntegrationFormSchema = z.object({
  id: z.string().min(1, 'Integration ID is required'),
  name: z.string().min(1, 'Integration name is required').optional(),
  description: z.string().optional(),
  baseUrl: z.string().url('Invalid URL format').optional(),
  authConfig: authConfigSchema.optional(),
});

/**
 * Schema cho OpenAPI spec upload
 */
export const openapiSpecSchema = z.object({
  openapi: z.string().optional(),
  swagger: z.string().optional(),
  info: z.object({
    title: z.string(),
    version: z.string(),
    description: z.string().optional(),
  }),
  servers: z.array(z.object({
    url: z.string(),
    description: z.string().optional(),
  })).optional(),
  paths: z.record(z.any()),
}).refine(
  (spec) => spec.openapi || spec.swagger,
  {
    message: 'OpenAPI or Swagger version is required',
  }
);

/**
 * Type definitions từ schemas
 */
export type IntegrationQueryParams = z.infer<typeof integrationQuerySchema>;
export type IntegrateFromOpenApiParams = z.infer<typeof integrateFromOpenApiSchema>;
export type UpdateBaseUrlParams = z.infer<typeof updateBaseUrlSchema>;
export type UpdateToolAuthParams = z.infer<typeof updateToolAuthSchema>;
export type CreateIntegrationFormData = z.infer<typeof createIntegrationFormSchema>;
export type UpdateIntegrationFormData = z.infer<typeof updateIntegrationFormSchema>;
export type AuthConfig = z.infer<typeof authConfigSchema>;
export type ApiKeyAuthConfig = z.infer<typeof apiKeyAuthConfigSchema>;
export type OAuthAuthConfig = z.infer<typeof oauthAuthConfigSchema>;
export type NoAuthConfig = z.infer<typeof noAuthConfigSchema>;
