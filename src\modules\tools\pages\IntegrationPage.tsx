import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Table, Modal, Chip, IconCard, Tooltip } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import IntegrationForm from '@/modules/tools/components/IntegrationForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';

// Import hooks từ integration module
import {
  useIntegrationTools,
  useIntegrateFromOpenApi,
  useUpdateBaseUrl,
  useUpdateToolAuth,
  useDeleteIntegrationTool,
  useToggleIntegrationToolStatus,
  useTestConnection,
  useRefreshOpenApiSpec,
} from '../integration/hooks/useIntegration';

// Import types từ integration module
import {
  IntegrationToolListItem,
  IntegrationToolDetail,
  BaseQueryParams,
  IntegrationStatus,
  AuthType,
} from '../integration/types/integration.types';

// Import schemas từ integration module
import {
  IntegrateFromOpenApiParams,
  UpdateBaseUrlParams,
  UpdateToolAuthParams,
} from '../integration/schemas/integration.schema';

// Type cho API response item
interface ApiIntegrationItem {
  id: string;
  toolName?: string;
  name?: string;
  toolDescription?: string;
  description?: string;
  baseUrl?: string;
  endpoint?: string;
  active?: boolean;
  status?: string;
  authType: string;
  createdAt: string | number;
  updatedAt: string | number;
  toolCount?: number;
  method?: string;
}

/**
 * Trang quản lý tool integration
 */
const IntegrationPage: React.FC = () => {
  const { t } = useTranslation(['tools', 'common']);
  const [integrations, setIntegrations] = useState<IntegrationToolListItem[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [integrationToDelete, setIntegrationToDelete] = useState<IntegrationToolListItem | null>(null);
  const [integrationToView, setIntegrationToView] = useState<IntegrationToolDetail | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showTestModal, setShowTestModal] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // State cho tìm kiếm và filter
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter,] = useState<IntegrationStatus | undefined>(undefined);
  const [authTypeFilter,] = useState<AuthType | undefined>(undefined);

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditSlideForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<BaseQueryParams>(() => {
    const params: BaseQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
      status: statusFilter,
      authType: authTypeFilter,
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection, statusFilter, authTypeFilter]);

  // Hooks để gọi API
  const { data: integrationsData, isLoading: isLoadingIntegrations, error: integrationsError } =
    useIntegrationTools(queryParams);

  const { mutateAsync: integrateFromOpenApi } = useIntegrateFromOpenApi();
  const { mutateAsync: updateBaseUrl } = useUpdateBaseUrl();
  const { mutateAsync: updateToolAuth } = useUpdateToolAuth();
  const { mutateAsync: deleteIntegration } = useDeleteIntegrationTool();
  const { mutateAsync: toggleStatus } = useToggleIntegrationToolStatus();
  const { mutateAsync: testConnection } = useTestConnection();
  const { mutateAsync: refreshSpec } = useRefreshOpenApiSpec();

  // Xử lý submit form tạo integration
  const handleSubmitCreateIntegration = useCallback(
    async (values: IntegrateFromOpenApiParams | UpdateBaseUrlParams | UpdateToolAuthParams) => {
      try {
        // For create form, values should be IntegrateFromOpenApiParams
        await integrateFromOpenApi(values as IntegrateFromOpenApiParams);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating integration:', error);
      }
    },
    [integrateFromOpenApi, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa integration
  const handleSubmitEditIntegration = useCallback(
    async (values: IntegrateFromOpenApiParams | UpdateBaseUrlParams | UpdateToolAuthParams) => {
      if (!integrationToView) return;

      try {
        if ('baseUrl' in values) {
          await updateBaseUrl(values as UpdateBaseUrlParams);
        } else if ('authConfig' in values) {
          await updateToolAuth(values as UpdateToolAuthParams);
        }
        hideEditForm();
        setIntegrationToView(null);
      } catch (error) {
        console.error('Error updating integration:', error);
      }
    },
    [updateBaseUrl, updateToolAuth, hideEditForm, integrationToView]
  );

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (integrationsData) {
      // Map dữ liệu từ API response sang format component mong đợi
      const mappedItems: IntegrationToolListItem[] = integrationsData.items.map((item: ApiIntegrationItem) => ({
        id: item.id,
        name: item.toolName || item.name || 'Unknown Tool',
        description: item.toolDescription || item.description,
        baseUrl: item.baseUrl || item.endpoint,
        status: item.active ? IntegrationStatus.ACTIVE : IntegrationStatus.INACTIVE,
        authType: item.authType as AuthType,
        method: item.method,
        createdAt: typeof item.createdAt === 'string' ? parseInt(item.createdAt) : item.createdAt,
        updatedAt: typeof item.updatedAt === 'string' ? parseInt(item.updatedAt) : item.updatedAt,
        toolCount: item.toolCount || 0,
      }));

      setIntegrations(mappedItems);
      setTotalItems(integrationsData.meta.totalItems);
    }
  }, [integrationsData, integrationsError]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1);
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((integration: IntegrationToolListItem) => {
    setIntegrationToDelete(integration);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setIntegrationToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!integrationToDelete) return;

    try {
      await deleteIntegration(integrationToDelete.id);
      setShowDeleteConfirm(false);
      setIntegrationToDelete(null);
    } catch (error) {
      console.error('Error deleting integration:', error);
    }
  }, [integrationToDelete, deleteIntegration]);

  // Xử lý đảo ngược trạng thái
  const handleToggleStatus = useCallback(
    async (integration: IntegrationToolListItem) => {
      try {
        await toggleStatus(integration.id);
      } catch (error) {
        console.error('Error toggling integration status:', error);
      }
    },
    [toggleStatus]
  );

  // Xử lý test connection
  const handleTestConnection = useCallback(
    async (integration: IntegrationToolListItem) => {
      try {
        const result = await testConnection(integration.id);
        setTestResult(result);
        setShowTestModal(true);
      } catch (error) {
        console.error('Error testing connection:', error);
        setTestResult({ success: false, message: 'Connection test failed' });
        setShowTestModal(true);
      }
    },
    [testConnection]
  );

  // Xử lý refresh OpenAPI spec
  const handleRefreshSpec = useCallback(
    async (integration: IntegrationToolListItem) => {
      try {
        await refreshSpec(integration.id);
      } catch (error) {
        console.error('Error refreshing OpenAPI spec:', error);
      }
    },
    [refreshSpec]
  );

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết
  const handleShowViewForm = useCallback(
    (integration: IntegrationToolListItem) => {
      setIntegrationToView(integration as IntegrationToolDetail);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Xử lý hiển thị form chỉnh sửa
  const handleShowEditForm = useCallback(
    (integration: IntegrationToolListItem) => {
      setIntegrationToView(integration as IntegrationToolDetail);
      showEditSlideForm();
    },
    [showEditSlideForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('tools:integration.table.name', 'Name'),
        dataIndex: 'name',
        width: '18%',
        sortable: true,
      },
     
      {
        key: 'baseUrl',
        title: t('tools:integration.table.endpoint', 'Endpoint'),
        dataIndex: 'baseUrl',
        width: '18%',
        render: (value: unknown) => {
          const baseUrl = value as string;
          return (
            <div className="flex items-center space-x-2">
              <span className="truncate font-mono text-sm">{baseUrl || 'N/A'}</span>
              {baseUrl && (
                <Tooltip content={t('common.copy', 'Copy')}>
                  <IconCard
                    icon="copy"
                    variant="default"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(baseUrl);
                    }}
                  />
                </Tooltip>
              )}
            </div>
          );
        },
      },
      {
        key: 'method',
        title: t('tools:integration.table.method', 'Method'),
        dataIndex: 'method',
        width: '8%',
        render: (value: unknown) => {
          const method = (value as string)?.toUpperCase();
          const variants = {
            'GET': 'success' as const,
            'POST': 'primary' as const,
            'PUT': 'warning' as const,
            'DELETE': 'danger' as const,
            'PATCH': 'info' as const,
          };
          return method ? (
            <Chip size="sm" variant={variants[method as keyof typeof variants] || 'default'}>
              {method}
            </Chip>
          ) : null;
        },
      },
      {
        key: 'authType',
        title: t('tools:integration.table.authType', 'Auth Type'),
        dataIndex: 'authType',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const authType = value as AuthType;
          const variants = {
            [AuthType.NONE]: 'default' as const,
            [AuthType.API_KEY]: 'primary' as const,
            [AuthType.OAUTH]: 'success' as const,
          };
          return (
            <Chip size="sm" variant={variants[authType] || 'default'}>
              {authType === AuthType.NONE ? 'None' :
               authType === AuthType.API_KEY ? 'API Key' : 'OAuth'}
            </Chip>
          );
        },
      },
      {
        key: 'status',
        title: t('tools:integration.table.status', 'Status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as IntegrationStatus;
          const variants = {
            [IntegrationStatus.ACTIVE]: 'success' as const,
            [IntegrationStatus.INACTIVE]: 'default' as const,
            [IntegrationStatus.ERROR]: 'danger' as const,
          };
          return (
            <Chip size="sm" variant={variants[status] || 'default'}>
              {status === IntegrationStatus.ACTIVE ? t('common.active', 'Active') :
               status === IntegrationStatus.INACTIVE ? t('common.inactive', 'Inactive') :
               t('common.error', 'Error')}
            </Chip>
          );
        },
      },
 
      {
        key: 'createdAt',
        title: t('tools:integration.table.createdAt', 'Created'),
        dataIndex: 'createdAt',
        width: '12%',
        sortable: true,
        render: (value: unknown) => {
          try {
            const timestamp = value as number;
            const timestampInMs = timestamp < 10 ** 12 ? timestamp * 1000 : timestamp;
            const date = new Date(timestampInMs);
            return date.toLocaleString('vi-VN');
          } catch {
            return String(value);
          }
        },
      },
      {
        key: 'actions',
        title: t('common.actions', 'Actions'),
        render: (_: unknown, record: IntegrationToolListItem) => {
          const menuItems = [
            {
              label: t('common.view', 'View'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('common.edit', 'Edit'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            {
              label: t('tools:integration.testConnection', 'Test Connection'),
              icon: 'wifi',
              onClick: () => handleTestConnection(record),
            },
            {
              label: t('tools:integration.refreshSpec', 'Refresh Spec'),
              icon: 'refresh',
              onClick: () => handleRefreshSpec(record),
            },
            {
              label: record.status === IntegrationStatus.ACTIVE
                ? t('common.deactivate', 'Deactivate')
                : t('common.activate', 'Activate'),
              icon: record.status === IntegrationStatus.ACTIVE ? 'toggle-off' : 'toggle-on',
              onClick: () => handleToggleStatus(record),
            },
            {
              label: t('common.delete', 'Delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [
    t,
    handleShowDeleteConfirm,
    handleShowViewForm,
    handleShowEditForm,
    handleToggleStatus,
    handleTestConnection,
    handleRefreshSpec,
  ]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={true}
            showColumnFilter={true}
            items={[]}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <IntegrationForm
            onSubmit={handleSubmitCreateIntegration}
            onCancel={hideCreateForm}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {integrationToView && (
            <IntegrationForm
              initialValues={integrationToView}
              onSubmit={() => {}} // Không cần xử lý submit vì chỉ xem
              onCancel={hideViewForm}
              readOnly={true}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {integrationToView && (
            <IntegrationForm
              initialValues={integrationToView}
              onSubmit={handleSubmitEditIntegration}
              onCancel={hideEditForm}
              isEdit={true}
            />
          )}
        </SlideInForm>

        <Card >
          <Table<IntegrationToolListItem>
            columns={columns}
            data={integrations}
            rowKey="id"
            loading={isLoadingIntegrations}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('tools:integration.confirmDelete', 'Confirm Delete')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete}>
              {t('common.delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <p>
            {t(
              'tools:integration.confirmDeleteMessage',
              'Are you sure you want to delete this integration? This action cannot be undone.'
            )}
          </p>
          {integrationToDelete && (
            <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
              <p className="font-medium">{integrationToDelete.name}</p>
            </div>
          )}
        </div>
      </Modal>

      {/* Modal test connection result */}
      <Modal
        isOpen={showTestModal}
        onClose={() => setShowTestModal(false)}
        title={t('tools:integration.connectionTestResult', 'Connection Test Result')}
        size="sm"
        footer={
          <div className="flex justify-end">
            <Button variant="primary" onClick={() => setShowTestModal(false)}>
              {t('common.close', 'Close')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          {testResult && (
            <div className={`p-3 rounded ${testResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              <div className="flex items-center space-x-2">
                <span className="font-medium">
                  {testResult.success ? '✅ Success' : '❌ Failed'}
                </span>
              </div>
              <p className="mt-1">{testResult.message}</p>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default IntegrationPage;

