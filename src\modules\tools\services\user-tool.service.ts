import { apiClient } from '@/shared/api/axios';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

import {
  ToolDetail,
  ToolListItem,
  ToolQueryParams,
  CloneAdminToolParams,
  UpdateFromAdminParams,
  CloneAllPublicToolsParams,
  RollbackToAdminVersionParams,
  CreateToolParams,
} from '../types/tool.types';
import { EditUserToolVersionParams } from '../types/user-tool.types';

/**
 * Service để tương tác với API tools của user
 */
export class ToolService {
  private baseUrl = `/user/tools`;

  /**
   * Lấy danh sách tool của người dùng
   * @param params Tham số truy vấn
   * @returns Danh sách tool với phân trang
   */
  async getUserTools(params?: ToolQueryParams): Promise<PaginatedResult<ToolListItem>> {
    try {
      const response = await apiClient.get<PaginatedResult<ToolListItem>>(this.baseUrl, {
        params,
        tokenType: 'user',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching user tools:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết tool của người dùng
   * @param id ID của tool
   * @returns Thông tin chi tiết tool
   */
  async getUserToolById(id: string): Promise<ToolDetail> {
    try {
      const response = await apiClient.get<ToolDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'user',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching user tool with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Sao chép tool từ admin
   * @param params Dữ liệu sao chép
   * @returns ID của tool đã sao chép
   */
  async cloneAdminTool(params: CloneAdminToolParams): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(`${this.baseUrl}/clone`, params, {
        tokenType: 'user',
      });
      return response.result.id;
    } catch (error) {
      console.error('Error cloning admin tool:', error);
      throw error;
    }
  }

  /**
   * Cập nhật tool từ phiên bản mới của admin
   * @param params Dữ liệu cập nhật
   * @returns ID của tool đã cập nhật
   */
  async updateFromAdmin(params: UpdateFromAdminParams): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(
        `${this.baseUrl}/update-from-admin`,
        params,
        {
          tokenType: 'user',
        }
      );
      return response.result.id;
    } catch (error) {
      console.error('Error updating tool from admin:', error);
      throw error;
    }
  }

  /**
   * Sao chép tất cả tool công khai từ admin
   * @param params Dữ liệu sao chép
   * @returns Số lượng tool đã sao chép
   */
  async cloneAllPublicTools(params: CloneAllPublicToolsParams): Promise<number> {
    try {
      const response = await apiClient.post<{ count: number }>(
        `${this.baseUrl}/clone-all`,
        params,
        { tokenType: 'user' }
      );
      return response.result.count;
    } catch (error) {
      console.error('Error cloning all public tools:', error);
      throw error;
    }
  }

  /**
   * Chỉnh sửa phiên bản tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param params Dữ liệu chỉnh sửa
   * @returns ID của phiên bản mới
   */
  async editToolVersion(
    toolId: string,
    versionId: string,
    params: EditUserToolVersionParams
  ): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(
        `${this.baseUrl}/${toolId}/versions/${versionId}/edit`,
        params,
        { tokenType: 'user' }
      );
      return response.result.id;
    } catch (error) {
      console.error('Error editing tool version:', error);
      throw error;
    }
  }

  /**
   * Khôi phục về phiên bản gốc từ admin
   * @param params Dữ liệu khôi phục
   * @returns ID của tool đã khôi phục
   */
  async rollbackToAdminVersion(params: RollbackToAdminVersionParams): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(
        `${this.baseUrl}/rollback-to-admin`,
        params,
        { tokenType: 'user' }
      );
      return response.result.id;
    } catch (error) {
      console.error('Error rolling back to admin version:', error);
      throw error;
    }
  }

  /**
   * Xóa tool
   * @param id ID của tool
   * @returns Thông báo xóa thành công
   */
  async deleteTool(id: string): Promise<string> {
    try {
      const response = await apiClient.delete<{ message: string }>(`${this.baseUrl}/${id}`, {
        tokenType: 'user',
      });
      return response.result.message;
    } catch (error) {
      console.error(`Error deleting tool with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo tool mới
   * @param params Dữ liệu tạo tool
   * @returns ID của tool đã tạo
   */
  async createTool(params: CreateToolParams): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(`${this.baseUrl}`, params, {
        tokenType: 'user',
      });
      return response.result.id;
    } catch (error) {
      console.error('Error creating tool:', error);
      throw error;
    }
  }

  /**
   * Kích hoạt/vô hiệu hóa tool
   * @param id ID của tool
   * @returns Trạng thái mới của tool
   */
  async toggleToolActive(id: string): Promise<{ isActive: boolean }> {
    try {
      const response = await apiClient.put<{ isActive: boolean }>(
        `${this.baseUrl}/${id}/active`,
        {},
        {
          tokenType: 'user',
        }
      );
      return response.result;
    } catch (error) {
      console.error(`Error toggling tool active status with ID ${id}:`, error);
      throw error;
    }
  }


}
