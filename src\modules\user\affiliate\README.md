# User Affiliate Module

Module quản lý tính năng affiliate cho người dùng cuối.

## Cấu trúc

```
src/modules/user/affiliate/
├── api/                    # API calls
├── components/             # React components
│   ├── WithdrawalModal.tsx
│   ├── ConvertPointsModal.tsx
│   └── index.ts
├── constants/              # Hằng số và query keys
├── demo/                   # Mock data cho demo
├── hooks/                  # Custom hooks
├── locales/                # Translation files
│   ├── vi.json
│   └── en.json
├── pages/                  # Page components
│   ├── AffiliateOverviewPage.tsx
│   └── index.ts
├── routers/                # Route definitions
├── schemas/                # Validation schemas
├── services/               # Business logic
├── types/                  # TypeScript types
└── index.ts               # Main export file
```

## Tính năng

### 1. Trang tổng quan (AffiliateOverviewPage)

- **Top Cards Overview**: Hiển thị thống kê tổng quan
  - Số lượng click
  - Số khách hàng
  - <PERSON><PERSON> đơn hàng
  - <PERSON><PERSON>h thu

- **Bi<PERSON><PERSON> đồ hoa hồng**: LineChart hiển thị hoa hồng theo tháng

- **Ví và hành động**:
  - <PERSON><PERSON><PERSON> thị số dư khả dụng
  - Hiển thị số tiền đang xử lý
  - Button "Yêu cầu rút tiền"
  - Button "Đổi point"

- **Link giới thiệu và chứng chỉ**:
  - Hiển thị link giới thiệu với button copy
  - Hiển thị chứng chỉ affiliate (nếu có)

- **Danh sách có thể đóng/mở (CollapsibleCard)**:
  - Danh sách đơn hàng
  - Lịch sử rút tiền
  - Danh sách khách hàng

### 2. Modal Components

- **WithdrawalModal**: Modal yêu cầu rút tiền với validation
- **ConvertPointsModal**: Modal đổi point thành tiền với validation

## Sử dụng

### Import module

```typescript
import { AffiliateOverviewPage } from '@/modules/user/affiliate';
```

### Sử dụng hooks

```typescript
import { 
  useAffiliateOverview,
  useRequestWithdrawal,
  useConvertPoints 
} from '@/modules/user/affiliate';

const MyComponent = () => {
  const { data, isLoading } = useAffiliateOverview();
  const withdrawalMutation = useRequestWithdrawal();
  const convertPointsMutation = useConvertPoints();
  
  // ...
};
```

### Routes

Module đã định nghĩa sẵn routes:

- `/user/affiliate` - Trang tổng quan affiliate
- `/user/affiliate/overview` - Trang tổng quan affiliate (alias)

## API Integration

Module sử dụng pattern 3-layer:

1. **API Layer** (`api/index.ts`): Gọi API thô
2. **Services Layer** (`services/index.ts`): Xử lý logic nghiệp vụ
3. **Hooks Layer** (`hooks/index.ts`): TanStack Query hooks

### API Endpoints cần implement

```typescript
// Backend cần implement các endpoints sau:
GET /user/affiliate/overview          // Lấy dữ liệu tổng quan
POST /user/affiliate/withdrawal       // Yêu cầu rút tiền
POST /user/affiliate/convert-points   // Đổi point
GET /user/affiliate/orders           // Danh sách đơn hàng
GET /user/affiliate/withdrawals      // Lịch sử rút tiền
GET /user/affiliate/customers        // Danh sách khách hàng
```

## Validation

Module sử dụng Zod cho validation:

- `withdrawalRequestSchema`: Validation cho yêu cầu rút tiền
- `convertPointsSchema`: Validation cho đổi point

## Internationalization

Module hỗ trợ đa ngôn ngữ với namespace `affiliate`:

```typescript
const { t } = useTranslation(['affiliate', 'common']);
```

## Demo Data

File `demo/mock-data.ts` chứa dữ liệu mẫu để test giao diện.

## Styling

Module tuân thủ design system của dự án:

- Sử dụng các component từ `@/shared/components/common`
- Responsive design với `ResponsiveGrid`
- Theme support (light/dark mode)
- Consistent spacing và typography
