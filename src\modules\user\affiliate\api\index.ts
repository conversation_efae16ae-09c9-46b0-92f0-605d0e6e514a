import { apiClient } from '@/shared/api';
import {
  AffiliateOverviewData,
  AffiliatePaginatedResponse,
  AffiliateOrder,
  AffiliateWithdrawal,
  AffiliateCustomer
} from '../types';

/**
 * API calls cho affiliate overview
 */

// L<PERSON>y dữ liệu tổng quan affiliate
export const getAffiliateOverview = async (): Promise<AffiliateOverviewData> => {
  const response = await apiClient.get('/user/affiliate/overview');
  return response.result as AffiliateOverviewData;
};

// Yêu cầu rút tiền
export const requestWithdrawal = async (amount: number, bankAccount: string) => {
  const response = await apiClient.post('/user/affiliate/withdrawal', {
    amount,
    bankAccount,
  });
  return response.result;
};

// Đổi point
export const convertPoints = async (points: number) => {
  const response = await apiClient.post('/user/affiliate/convert-points', {
    points,
  });
  return response.result;
};

// <PERSON><PERSON><PERSON> danh sách đơn hàng affiliate
export const getAffiliateOrders = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateOrder>> => {
  const response = await apiClient.get('/user/affiliate/orders', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateOrder>;
};

// Lấy lịch sử rút tiền
export const getWithdrawalHistory = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateWithdrawal>> => {
  const response = await apiClient.get('/user/affiliate/withdrawals', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateWithdrawal>;
};

// Lấy danh sách khách hàng
export const getAffiliateCustomers = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateCustomer>> => {
  const response = await apiClient.get('/user/affiliate/customers', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateCustomer>;
};
