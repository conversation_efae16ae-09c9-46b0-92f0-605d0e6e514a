import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Input,
  Button,
  Typography,
} from '@/shared/components/common';
import { useRequestWithdrawal } from '../hooks';
import { withdrawalRequestSchema, WithdrawalRequestData } from '../schemas';
import { AFFILIATE_CONSTANTS } from '../constants';
import { formatCurrency } from '@/shared/utils/number-format.utils';

interface WithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance: number;
}

/**
 * Modal yêu cầu rút tiền
 */
const WithdrawalModal: React.FC<WithdrawalModalProps> = ({
  isOpen,
  onClose,
  currentBalance,
}) => {
  const { t } = useTranslation(['affiliate', 'common']);
  const [formData, setFormData] = useState<WithdrawalRequestData>({
    amount: 0,
    bankAccount: '',
  });
  const [errors, setErrors] = useState<Partial<Record<keyof WithdrawalRequestData, string>>>({});

  const requestWithdrawalMutation = useRequestWithdrawal();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form data
      const validatedData = withdrawalRequestSchema.parse(formData);

      // Check if amount exceeds balance
      if (validatedData.amount > currentBalance) {
        setErrors({ amount: 'Số tiền rút không được vượt quá số dư hiện tại' });
        return;
      }

      // Submit withdrawal request
      await requestWithdrawalMutation.mutateAsync(validatedData);

      // Reset form and close modal
      setFormData({ amount: 0, bankAccount: '' });
      setErrors({});
      onClose();

      // TODO: Show success toast
    } catch (error) {
      console.error('Error submitting withdrawal request:', error);
      // Handle generic error
      setErrors({ amount: 'Có lỗi xảy ra, vui lòng thử lại' });
    }
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setFormData({ ...formData, amount: value });
  };

  const handleBankAccountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, bankAccount: e.target.value });
  };

  const handleMaxAmount = () => {
    const maxAmount = Math.min(currentBalance, AFFILIATE_CONSTANTS.MAX_WITHDRAWAL_AMOUNT);
    setFormData({ ...formData, amount: maxAmount });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('affiliate:withdrawal.title')}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-4">
          <Typography variant="body2" color="muted">
            {t('affiliate:withdrawal.currentBalance')}: {formatCurrency(currentBalance)}
          </Typography>
          <Typography variant="caption" color="muted">
            {t('affiliate:withdrawal.minAmount')}: {formatCurrency(AFFILIATE_CONSTANTS.MIN_WITHDRAWAL_AMOUNT)}
          </Typography>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium">
            {t('affiliate:withdrawal.amount')} <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <Input
              type="number"
              value={formData.amount || ''}
              onChange={handleAmountChange}
              placeholder={t('affiliate:withdrawal.amountPlaceholder')}
              min={AFFILIATE_CONSTANTS.MIN_WITHDRAWAL_AMOUNT}
              max={Math.min(currentBalance, AFFILIATE_CONSTANTS.MAX_WITHDRAWAL_AMOUNT)}
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleMaxAmount}
              className="whitespace-nowrap"
            >
              {t('affiliate:withdrawal.maxAmount')}
            </Button>
          </div>
          {errors.amount && (
            <Typography variant="caption" color="danger">
              {errors.amount}
            </Typography>
          )}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium">
            {t('affiliate:withdrawal.bankAccount')} <span className="text-red-500">*</span>
          </label>
          <Input
            type="text"
            value={formData.bankAccount}
            onChange={handleBankAccountChange}
            placeholder={t('affiliate:withdrawal.bankAccountPlaceholder')}
            maxLength={500}
          />
          {errors.bankAccount && (
            <Typography variant="caption" color="danger">
              {errors.bankAccount}
            </Typography>
          )}
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={requestWithdrawalMutation.isPending}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={requestWithdrawalMutation.isPending}
          >
            {t('affiliate:withdrawal.submit')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default WithdrawalModal;
