import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  CollapsibleCard,
  Table,
  ResponsiveGrid,
  Input,
} from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { LineChart } from '@/shared/components/charts';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { MousePointer, Users, ShoppingCart, DollarSign } from 'lucide-react';
import {
  useAffiliateOverview,
  useAffiliateOrders,
  useWithdrawalHistory,
  useAffiliateCustomers,
} from '../hooks';
import { ORDER_STATUS_MAP, WITHDRAWAL_STATUS_MAP } from '../constants';
import { formatCurrency } from '@/shared/utils/number-format.utils';
import { formatDate } from '@/shared/utils/date';
import { WithdrawalModal, ConvertPointsModal } from '../components';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';

/**
 * Trang tổng quan Affiliate
 */
const AffiliateOverviewPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);
  const [showPointsModal, setShowPointsModal] = useState(false);

  // Search and filter states
  const [ordersSearch, setOrdersSearch] = useState('');
  const [withdrawalsSearch, setWithdrawalsSearch] = useState('');
  const [customersSearch, setCustomersSearch] = useState('');

  // Hooks
  const { data: overviewData } = useAffiliateOverview();
  const { data: ordersData, isLoading: ordersLoading } = useAffiliateOrders({ limit: 5 });
  const { data: withdrawalsData, isLoading: withdrawalsLoading } = useWithdrawalHistory({ limit: 5 });
  const { data: customersData, isLoading: customersLoading } = useAffiliateCustomers({ limit: 5 });

  // Overview cards data
  const overviewStats: OverviewCardProps[] = useMemo(() => {
    if (!overviewData?.stats) return [];

    const { stats } = overviewData;
    return [
      {
        title: t('affiliate:overview.stats.clicks'),
        value: stats.totalClicks.toLocaleString(),
        description: '+12%',
        icon: MousePointer,
        color: 'blue',
      },
      {
        title: t('affiliate:overview.stats.customers'),
        value: stats.totalCustomers.toLocaleString(),
        description: '+8%',
        icon: Users,
        color: 'green',
      },
      {
        title: t('affiliate:overview.stats.orders'),
        value: stats.totalOrders.toLocaleString(),
        description: '+15%',
        icon: ShoppingCart,
        color: 'orange',
      },
      {
        title: t('affiliate:overview.stats.revenue'),
        value: formatCurrency(stats.totalRevenue),
        description: '+22%',
        icon: DollarSign,
        color: 'purple',
      },
    ];
  }, [overviewData, t]);

  // Chart data
  const chartData = useMemo(() => {
    return overviewData?.commissionChart || [];
  }, [overviewData]);

  // Table columns for orders
  const orderColumns = useMemo(() => [
    {
      title: t('affiliate:orders.orderCode'),
      dataIndex: 'orderCode',
      key: 'orderCode',
    },
    {
      title: t('affiliate:orders.customer'),
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: t('affiliate:orders.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('affiliate:orders.commission'),
      dataIndex: 'commission',
      key: 'commission',
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('affiliate:orders.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: unknown) => (
        <span className={`badge badge-${ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.color}`}>
          {ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.label}
        </span>
      ),
    },
  ], [t]);

  // Table columns for withdrawals
  const withdrawalColumns = useMemo(() => [
    {
      title: t('affiliate:withdrawals.amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('affiliate:withdrawals.bankAccount'),
      dataIndex: 'bankAccount',
      key: 'bankAccount',
    },
    {
      title: t('affiliate:withdrawals.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: unknown) => (
        <span className={`badge badge-${WITHDRAWAL_STATUS_MAP[status as keyof typeof WITHDRAWAL_STATUS_MAP]?.color}`}>
          {WITHDRAWAL_STATUS_MAP[status as keyof typeof WITHDRAWAL_STATUS_MAP]?.label}
        </span>
      ),
    },
    {
      title: t('affiliate:withdrawals.requestedAt'),
      dataIndex: 'requestedAt',
      key: 'requestedAt',
      render: (date: unknown) => formatDate(date as string),
    },
  ], [t]);

  // Table columns for customers
  const customerColumns = useMemo(() => [
    {
      title: t('affiliate:customers.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('affiliate:customers.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('affiliate:customers.totalOrders'),
      dataIndex: 'totalOrders',
      key: 'totalOrders',
    },
    {
      title: t('affiliate:customers.totalSpent'),
      dataIndex: 'totalSpent',
      key: 'totalSpent',
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('affiliate:customers.registeredAt'),
      dataIndex: 'registeredAt',
      key: 'registeredAt',
      render: (date: unknown) => formatDate(date as string),
    },
  ], [t]);

  const handleWithdrawal = () => {
    setShowWithdrawalModal(true);
  };

  const handleConvertPoints = () => {
    setShowPointsModal(true);
  };

  const handleCloseWithdrawalModal = () => {
    setShowWithdrawalModal(false);
  };

  const handleClosePointsModal = () => {
    setShowPointsModal(false);
  };

  const handleCopyReferralLink = () => {
    if (overviewData?.referralLink?.url) {
      navigator.clipboard.writeText(overviewData.referralLink.url);
      // TODO: Show toast notification
    }
  };

  // Search handlers
  const handleOrdersSearch = (term: string) => {
    setOrdersSearch(term);
    // TODO: Implement search logic with ordersSearch
    console.log('Orders search:', term, ordersSearch);
  };

  const handleWithdrawalsSearch = (term: string) => {
    setWithdrawalsSearch(term);
    // TODO: Implement search logic with withdrawalsSearch
    console.log('Withdrawals search:', term, withdrawalsSearch);
  };

  const handleCustomersSearch = (term: string) => {
    setCustomersSearch(term);
    // TODO: Implement search logic with customersSearch
    console.log('Customers search:', term, customersSearch);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Overview Stats */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={!overviewData}
        skeletonCount={4}
      />

      {/* Commission Chart */}
      <Card className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('affiliate:overview.commissionChart')}
        </Typography>
        <div style={{ height: 300, width: '100%' }}>
          <LineChart
            data={chartData as unknown as Record<string, unknown>[]}
            xAxisKey="month"
            lines={[
              {
                dataKey: 'commission',
                name: t('affiliate:overview.commission'),
                color: '#3B82F6',
                strokeWidth: 2,
              },
            ]}
            height={300}
            showGrid
            showTooltip
            showLegend
          />
        </div>
      </Card>

      {/* Wallet and Actions */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }} gap={6}>
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('affiliate:wallet.title')}
          </Typography>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Typography variant="body1" color="muted">
                {t('affiliate:wallet.balance')}
              </Typography>
              <Typography variant="h4" color="primary">
                {formatCurrency(overviewData?.wallet?.balance || 0)}
              </Typography>
            </div>
            <div className="flex justify-between items-center">
              <Typography variant="body1" color="muted">
                {t('affiliate:wallet.pending')}
              </Typography>
              <Typography variant="h5" color="warning">
                {formatCurrency(overviewData?.wallet?.pendingAmount || 0)}
              </Typography>
            </div>
            <div className="flex gap-3 mt-6">
              <Button
                variant="primary"
                onClick={handleWithdrawal}
                className="flex-1"
              >
                {t('affiliate:wallet.withdraw')}
              </Button>
              <Button
                variant="outline"
                onClick={handleConvertPoints}
                className="flex-1"
              >
                {t('affiliate:wallet.convertPoints')}
              </Button>
            </div>
          </div>
        </Card>

        {/* Referral Link and Certificate */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('affiliate:referral.title')}
          </Typography>
          <div className="space-y-4">
            <div>
              <Typography variant="body2" color="muted" className="mb-2">
                {t('affiliate:referral.link')}
              </Typography>
              <div className="flex gap-2">
                <Input
                  type="text"
                  value={overviewData?.referralLink?.url || ''}
                  readOnly
                  className="flex-1"
                />
                <Button variant="outline" onClick={handleCopyReferralLink}>
                  {t('affiliate:common.copy')}
                </Button>
              </div>
            </div>

            {overviewData?.certificate && (
              <div>
                <Typography variant="body2" color="muted" className="mb-2">
                  {t('affiliate:certificate.title')}
                </Typography>
                <img
                  src={overviewData.certificate.imageUrl}
                  alt={overviewData.certificate.title}
                  className="w-full h-32 object-cover rounded-md border"
                />
              </div>
            )}
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Orders List */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('affiliate:orders.title')} ({ordersData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleOrdersSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={orderColumns}
          data={ordersData?.items || []}
          loading={ordersLoading}
          pagination={false}
        />
        {ordersData?.items?.length === 0 && !ordersLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('affiliate:orders.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Withdrawal History */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('affiliate:withdrawals.title')} ({withdrawalsData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleWithdrawalsSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={withdrawalColumns}
          data={withdrawalsData?.items || []}
          loading={withdrawalsLoading}
          pagination={false}
        />
        {withdrawalsData?.items?.length === 0 && !withdrawalsLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('affiliate:withdrawals.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Customers List */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('affiliate:customers.title')} ({customersData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleCustomersSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={customerColumns}
          data={customersData?.items || []}
          loading={customersLoading}
          pagination={false}
        />
        {customersData?.items?.length === 0 && !customersLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('affiliate:customers.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Modals */}
      <WithdrawalModal
        isOpen={showWithdrawalModal}
        onClose={handleCloseWithdrawalModal}
        currentBalance={overviewData?.wallet?.balance || 0}
      />

      <ConvertPointsModal
        isOpen={showPointsModal}
        onClose={handleClosePointsModal}
        currentPoints={1000} // TODO: Get from API
      />
    </div>
  );
};

export default AffiliateOverviewPage;
