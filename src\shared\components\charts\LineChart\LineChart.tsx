import React, { useMemo } from 'react';
import {
  LineChart as RechartsLine<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
} from 'recharts';
import { LineChartProps } from './LineChart.types';
import { useTheme } from '@/shared/contexts/theme';

/**
 * LineChart component hiển thị dữ liệu dạng đường
 *
 * @example
 * ```tsx
 * <LineChart
 *   data={[
 *     { month: 'Jan', sales: 100, profit: 50 },
 *     { month: 'Feb', sales: 200, profit: 100 },
 *     { month: 'Mar', sales: 150, profit: 75 },
 *   ]}
 *   xAxisKey="month"
 *   lines={[
 *     { dataKey: 'sales', name: 'Sales', color: '#FF3333' },
 *     { dataKey: 'profit', name: 'Profit', color: '#FFCC99' },
 *   ]}
 *   height={300}
 *   showGrid
 *   showTooltip
 *   showLegend
 * />
 * ```
 */
const LineChart: React.FC<LineChartProps> = ({
  data,
  xAxisKey,
  lines,
  height = 300,
  width = '100%',
  showGrid = true,
  showTooltip = true,
  showLegend = true,
  legendPosition = 'bottom',
  // customLegend không được sử dụng do vấn đề tương thích kiểu
  margin = { top: 10, right: 30, left: 0, bottom: 0 },
  xAxisFormatter,
  yAxisFormatter,
  xAxisLabel,
  yAxisLabel,
  className = '',
  animated = true,
}) => {
  const { currentTheme } = useTheme();

  // Lấy màu từ theme
  const gridColor = useMemo(
    () => currentTheme?.semanticColors?.border || '#E5E7EB',
    [currentTheme]
  );
  const textColor = useMemo(
    () => currentTheme?.semanticColors?.foreground || '#111827',
    [currentTheme]
  );

  // Tạo các đường
  const renderLines = useMemo(() => {
    return lines.map((line, index) => (
      <Line
        key={`line-${index}`}
        type={line.type || 'monotone'}
        dataKey={line.dataKey}
        name={line.name || line.dataKey}
        stroke={line.color || '#FF3333'}
        strokeWidth={line.strokeWidth || 2}
        dot={line.showDot !== false ? { r: line.dotSize || 4 } : false}
        activeDot={{ r: (line.dotSize || 4) + 2 }}
        connectNulls={line.connectNulls !== false}
        isAnimationActive={animated}
      />
    ));
  }, [lines, animated]);

  // Tạo component chính
  const chart = (
    <RechartsLineChart data={data} margin={margin} className={className}>
      {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />}

      <XAxis dataKey={xAxisKey} tick={{ fill: textColor }} tickFormatter={xAxisFormatter}>
        {xAxisLabel && (
          <Label value={xAxisLabel} offset={-5} position="insideBottom" style={{ textAnchor: 'middle', fill: textColor }} />
        )}
      </XAxis>

      <YAxis tick={{ fill: textColor }} tickFormatter={yAxisFormatter}>
        {yAxisLabel && (
          <Label value={yAxisLabel} angle={-90} position="insideLeft" style={{ textAnchor: 'middle', fill: textColor }} />
        )}
      </YAxis>

      {showTooltip && <Tooltip />}

      {showLegend && (
        <Legend
          verticalAlign={
            legendPosition === 'top' || legendPosition === 'bottom' ? legendPosition : 'middle'
          }
          align={
            legendPosition === 'left' || legendPosition === 'right' ? legendPosition : 'center'
          }
        />
      )}

      {renderLines}
    </RechartsLineChart>
  );

  // Đảm bảo dữ liệu không rỗng
  if (!data || data.length === 0) {
    return <div>Không có dữ liệu</div>;
  }

  // Sử dụng div với kích thước cố định và ResponsiveContainer
  return (
    <div style={{ width, height, position: 'relative', minHeight: '300px', overflow: 'visible' }}>
      {/* Sử dụng aspect ratio để đảm bảo biểu đồ luôn hiển thị */}
      <ResponsiveContainer width="100%" height="100%" aspect={16 / 9}>
        {chart}
      </ResponsiveContainer>
    </div>
  );
};

export default LineChart;
