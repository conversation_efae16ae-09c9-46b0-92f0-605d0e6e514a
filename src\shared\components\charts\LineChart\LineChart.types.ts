import { CurveType } from 'recharts/types/shape/Curve';

/**
 * C<PERSON>u hình cho một đường trong biểu đồ
 */
export interface LineConfig {
  /**
   * Khóa dữ liệu cho đường
   */
  dataKey: string;

  /**
   * Tên hiển thị cho đường
   */
  name?: string;

  /**
   * <PERSON><PERSON>u sắc của đường
   */
  color?: string;

  /**
   * Độ dày của đường
   */
  strokeWidth?: number;

  /**
   * Loại đường (linear, monotone, step, etc.)
   */
  type?: CurveType;

  /**
   * C<PERSON> hiển thị điểm dữ liệu không
   */
  showDot?: boolean;

  /**
   * <PERSON><PERSON>ch thước của điểm dữ liệu
   */
  dotSize?: number;

  /**
   * C<PERSON> kết nối các điểm dữ liệu không
   */
  connectNulls?: boolean;
}

/**
 * Props cho LineChart component
 */
export interface LineChartProps {
  /**
   * Dữ liệu cho biểu đồ
   */
  data: Record<string, unknown>[];

  /**
   * Khóa dữ liệu cho trục X
   */
  xAxisKey: string;

  /**
   * Cấu hình cho các đường
   */
  lines: LineConfig[];

  /**
   * Chiều cao của biểu đồ
   */
  height?: number;

  /**
   * Chiều rộng của biểu đồ (mặc định là 100%)
   */
  width?: number | string;

  /**
   * Có hiển thị lưới không
   */
  showGrid?: boolean;

  /**
   * Có hiển thị tooltip không
   */
  showTooltip?: boolean;

  /**
   * Có hiển thị legend không
   */
  showLegend?: boolean;

  /**
   * Vị trí của legend
   */
  legendPosition?: 'top' | 'right' | 'bottom' | 'left';

  /**
   * Component legend tùy chỉnh - Hiện tại không được sử dụng do vấn đề tương thích kiểu
   */
  // customLegend?: (props: LegendProps) => ReactNode;

  /**
   * Margin của biểu đồ
   */
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  /**
   * Format cho giá trị trục X
   */
  xAxisFormatter?: (value: unknown) => string;

  /**
   * Format cho giá trị trục Y
   */
  yAxisFormatter?: (value: unknown) => string;

  /**
   * Nhãn cho trục X
   */
  xAxisLabel?: string;

  /**
   * Nhãn cho trục Y
   */
  yAxisLabel?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Có animation không
   */
  animated?: boolean;
}
