import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { CalendarProps } from './types';
import CalendarHeader from './CalendarHeader';
import CalendarGrid from './CalendarGrid';
import { 
  useCalendar, 
  useCalendarKeyboard, 
  useCalendarTouch, 
  useCalendarResponsive,
  useCalendarTheme 
} from './hooks';

/**
 * Props cho AnimatedCalendar component
 */
export interface AnimatedCalendarProps extends CalendarProps {
  /**
   * Bật/tắt animations
   */
  enableAnimations?: boolean;
  
  /**
   * Animation preset
   */
  animationPreset?: 'subtle' | 'smooth' | 'bouncy' | 'fast';
  
  /**
   * Custom animation variants
   */
  customVariants?: {
    calendar?: Variants;
    month?: Variants;
    cell?: Variants;
  };
  
  /**
   * Animation direction cho month transition
   */
  animationDirection?: 'horizontal' | 'vertical' | 'fade';
}

/**
 * Animation variants cho các preset khác nhau
 */
const animationPresets = {
  subtle: {
    calendar: {
      initial: { opacity: 0, scale: 0.95 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.95 },
      transition: { duration: 0.2, ease: 'easeOut' },
    },
    month: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 },
      transition: { duration: 0.3, ease: 'easeInOut' },
    },
    cell: {
      initial: { scale: 0.8, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      whileHover: { scale: 1.05 },
      whileTap: { scale: 0.95 },
      transition: { duration: 0.15, ease: 'easeOut' },
    },
  },
  smooth: {
    calendar: {
      initial: { opacity: 0, y: 10 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -10 },
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    month: {
      initial: { opacity: 0, x: 30 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -30 },
      transition: { duration: 0.4, ease: [0.4, 0, 0.2, 1] },
    },
    cell: {
      initial: { scale: 0.9, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      whileHover: { scale: 1.1, transition: { duration: 0.2 } },
      whileTap: { scale: 0.9 },
      transition: { duration: 0.2, ease: 'easeOut' },
    },
  },
  bouncy: {
    calendar: {
      initial: { opacity: 0, scale: 0.8 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.8 },
      transition: { duration: 0.4, ease: [0.68, -0.55, 0.265, 1.55] },
    },
    month: {
      initial: { opacity: 0, x: 50, scale: 0.9 },
      animate: { opacity: 1, x: 0, scale: 1 },
      exit: { opacity: 0, x: -50, scale: 0.9 },
      transition: { duration: 0.5, ease: [0.68, -0.55, 0.265, 1.55] },
    },
    cell: {
      initial: { scale: 0.7, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      whileHover: { 
        scale: 1.15, 
        transition: { duration: 0.2, ease: [0.68, -0.55, 0.265, 1.55] } 
      },
      whileTap: { scale: 0.85 },
      transition: { duration: 0.3, ease: [0.68, -0.55, 0.265, 1.55] },
    },
  },
  fast: {
    calendar: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.1 },
    },
    month: {
      initial: { opacity: 0, x: 10 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -10 },
      transition: { duration: 0.15, ease: 'easeOut' },
    },
    cell: {
      initial: { scale: 0.95, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      whileHover: { scale: 1.05 },
      whileTap: { scale: 0.95 },
      transition: { duration: 0.1 },
    },
  },
};

/**
 * AnimatedCalendar component với Framer Motion animations
 */
const AnimatedCalendar: React.FC<AnimatedCalendarProps> = ({
  selectedDate,
  onSelectDate,
  month: propMonth,
  onMonthChange,
  disabledDates,
  minDate,
  maxDate,
  showToday = true,
  showWeekNumbers = false,
  firstDayOfWeek = 1,
  weekDayNames,
  monthNames,
  className = '',
  rangeMode = false,
  startDate,
  endDate,
  onRangeSelect,
  showTodayButton = false,
  todayButtonText,
  enableAnimations = true,
  animationPreset = 'smooth',
  customVariants,
  animationDirection = 'horizontal',
}) => {
  const { t } = useTranslation();
  
  // Hooks
  const responsive = useCalendarResponsive();
  const { theme, config } = useCalendarTheme();
  
  // Calendar logic hook
  const calendar = useCalendar({
    initialDate: propMonth,
    selectedDate,
    rangeMode,
    startDate,
    endDate,
    firstDayOfWeek,
    onSelectDate,
    onRangeSelect,
    onMonthChange,
  });

  // Keyboard navigation hook
  const keyboard = useCalendarKeyboard({
    calendar,
    onSelectDate: calendar.handleSelectDate,
    disabled: false,
    autoFocus: false,
  });

  // Touch gestures hook
  const touch = useCalendarTouch({
    calendar,
    disabled: false,
    enableSwipe: responsive.enableSwipe,
  });

  // Animation state
  const [monthKey, setMonthKey] = useState(() => 
    `${calendar.month.getFullYear()}-${calendar.month.getMonth()}`
  );
  const [animationDirection_, setAnimationDirection_] = useState<'next' | 'prev' | 'none'>('none');

  // Handle month change with animation direction
  const handleMonthChange = useCallback((newMonth: Date, direction?: 'next' | 'prev') => {
    setAnimationDirection_(direction || 'none');
    setMonthKey(`${newMonth.getFullYear()}-${newMonth.getMonth()}`);
    calendar.handleMonthChange(newMonth);
  }, [calendar]);

  // Determine if animations should be enabled
  const shouldAnimate = enableAnimations && config.enableAnimations && !config.reducedMotion;

  // Get animation variants
  const variants = customVariants || animationPresets[animationPreset];

  // Month transition variants based on direction
  const getMonthVariants = useCallback(() => {
    if (!shouldAnimate) return {};

    const baseVariants = variants.month;
    
    if (animationDirection === 'vertical') {
      return {
        ...baseVariants,
        initial: { opacity: 0, y: animationDirection_ === 'next' ? 20 : -20 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: animationDirection_ === 'next' ? -20 : 20 },
      };
    } else if (animationDirection === 'fade') {
      return {
        ...baseVariants,
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
      };
    } else {
      // horizontal (default)
      return {
        ...baseVariants,
        initial: { opacity: 0, x: animationDirection_ === 'next' ? 30 : -30 },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: animationDirection_ === 'next' ? -30 : 30 },
      };
    }
  }, [shouldAnimate, variants.month, animationDirection, animationDirection_]);

  // Determine final showWeekNumbers based on responsive design
  const finalShowWeekNumbers = showWeekNumbers && responsive.showWeekNumbers;

  // Base classes with theme and responsive adjustments
  const baseClasses = `
    bg-white dark:bg-dark-light rounded-lg shadow-lg p-2
    ${responsive.compactMode ? 'w-72' : 'w-80'}
    ${responsive.touchOptimized ? 'touch-manipulation' : ''}
    ${theme.name === 'dark' ? 'dark' : ''}
  `.trim();

  // Combine all classes
  const calendarClasses = [baseClasses, className].join(' ');

  // CSS variables for theme
  const cssVariables = theme ? {
    '--calendar-primary': theme.colors.primary,
    '--calendar-background': theme.colors.background,
    '--calendar-text': theme.colors.text.primary,
    '--calendar-border': theme.colors.border.default,
    '--calendar-hover': theme.colors.hover,
    '--calendar-selected': theme.colors.selected,
  } : {};

  return (
    <motion.div
      className={calendarClasses}
      ref={keyboard.calendarRef}
      tabIndex={0}
      onKeyDown={keyboard.handleKeyDown}
      onTouchStart={touch.handleTouchStart}
      onTouchMove={touch.handleTouchMove}
      onTouchEnd={touch.handleTouchEnd}
      role="grid"
      aria-label={t('datepicker.calendar', 'Calendar')}
      style={{
        minHeight: responsive.minTouchTarget * 7,
        ...cssVariables,
      }}
      variants={shouldAnimate ? variants.calendar : undefined}
      initial={shouldAnimate ? "initial" : false}
      animate={shouldAnimate ? "animate" : false}
      exit={shouldAnimate ? "exit" : undefined}
    >
      <CalendarHeader 
        month={calendar.month} 
        onMonthChange={(newMonth) => {
          const direction = newMonth > calendar.month ? 'next' : 'prev';
          handleMonthChange(newMonth, direction);
        }}
        monthNames={monthNames} 
      />

      <div className="relative overflow-hidden">
        <AnimatePresence mode="wait" custom={animationDirection_}>
          <motion.div
            key={monthKey}
            custom={animationDirection_}
            variants={shouldAnimate ? getMonthVariants() : undefined}
            initial={shouldAnimate ? "initial" : false}
            animate={shouldAnimate ? "animate" : false}
            exit={shouldAnimate ? "exit" : undefined}
          >
            <CalendarGrid
              month={calendar.month}
              selectedDate={rangeMode ? null : selectedDate}
              onSelectDate={calendar.handleSelectDate}
              disabledDates={disabledDates}
              minDate={minDate}
              maxDate={maxDate}
              showToday={showToday}
              showWeekNumbers={finalShowWeekNumbers}
              firstDayOfWeek={firstDayOfWeek}
              weekDayNames={weekDayNames}
              rangeMode={rangeMode}
              startDate={startDate}
              endDate={endDate}
              focusedDate={calendar.focusedDate}
              // Animation props for cells - commented out as these props don't exist in CalendarGrid
              // enableCellAnimations={shouldAnimate}
              // cellVariants={variants.cell}
            />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Action buttons */}
      {showTodayButton && (
        <motion.div 
          className="mt-2 flex justify-center"
          initial={shouldAnimate ? { opacity: 0, y: 10 } : false}
          animate={shouldAnimate ? { opacity: 1, y: 0 } : false}
          transition={shouldAnimate ? { delay: 0.2, duration: 0.3 } : undefined}
        >
          <motion.button
            type="button"
            className={`
              px-3 py-1 text-sm rounded transition-colors
              ${responsive.touchOptimized ? 'min-h-[44px] px-4' : ''}
              bg-gray-100 hover:bg-gray-200 
              dark:bg-gray-800 dark:hover:bg-gray-700
            `.trim()}
            onClick={calendar.handleTodayClick}
            style={{
              minHeight: responsive.touchOptimized ? responsive.minTouchTarget : undefined,
            }}
            whileHover={shouldAnimate ? { scale: 1.05 } : undefined}
            whileTap={shouldAnimate ? { scale: 0.95 } : undefined}
          >
            {todayButtonText || t('datepicker.today', 'Today')}
          </motion.button>
        </motion.div>
      )}

      {/* Swipe indicator for mobile */}
      <AnimatePresence>
        {touch.isSwipeInProgress && responsive.isMobile && (
          <motion.div 
            className="absolute inset-0 pointer-events-none flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div 
              className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
            >
              {touch.swipeDirection === 'left' ? '→' : '←'}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default AnimatedCalendar;
