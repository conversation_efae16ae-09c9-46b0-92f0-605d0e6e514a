import { useCallback, useEffect, useRef } from 'react';
import { UseCalendarReturn } from './useCalendar';

/**
 * Hook xử lý keyboard navigation cho Calendar
 * Tối ưu hóa accessibility và user experience
 */
export interface UseCalendarKeyboardOptions {
  calendar: UseCalendarReturn;
  onSelectDate: (date: Date) => void;
  disabled?: boolean;
  autoFocus?: boolean;
}

export interface UseCalendarKeyboardReturn {
  handleKeyDown: (e: React.KeyboardEvent) => void;
  calendarRef: React.RefObject<HTMLDivElement>;
  focusCalendar: () => void;
}

export const useCalendarKeyboard = (
  options: UseCalendarKeyboardOptions
): UseCalendarKeyboardReturn => {
  const { calendar, onSelectDate, disabled = false, autoFocus = false } = options;
  const { focusedDate, setFocusedDate, handleKeyboardNavigation } = calendar;

  const calendarRef = useRef<HTMLDivElement>(null);

  // Focus vào calendar
  const focusCalendar = useCallback(() => {
    if (calendarRef.current && !disabled) {
      calendarRef.current.focus();
    }
  }, [disabled]);

  // Xử lý keyboard events
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (disabled) return;

      // Nếu chưa có ngày nào được focus, focus vào ngày hiện tại
      if (!focusedDate) {
        const today = new Date();
        setFocusedDate(today);
        return;
      }

      // Xử lý các phím đặc biệt
      switch (e.key) {
        case 'Enter':
        case ' ':
          // Chọn ngày đang focus
          e.preventDefault();
          onSelectDate(focusedDate);
          return;

        case 'Escape':
          // Blur khỏi calendar
          e.preventDefault();
          if (calendarRef.current) {
            calendarRef.current.blur();
          }
          return;

        case 'Tab':
          // Cho phép tab navigation bình thường
          return;

        default:
          break;
      }

      // Xử lý navigation keys
      const newFocusDate = handleKeyboardNavigation(e.key, focusedDate);
      
      if (newFocusDate) {
        e.preventDefault();
        setFocusedDate(newFocusDate);
      }
    },
    [disabled, focusedDate, setFocusedDate, handleKeyboardNavigation, onSelectDate]
  );

  // Auto focus khi component mount
  useEffect(() => {
    if (autoFocus && !disabled) {
      // Delay để đảm bảo component đã render xong
      const timer = setTimeout(() => {
        focusCalendar();
      }, 100);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [autoFocus, disabled, focusCalendar]);

  // Focus vào ngày được chọn khi selectedDate thay đổi
  useEffect(() => {
    if (focusedDate && calendarRef.current) {
      // Tìm cell tương ứng với focusedDate và scroll vào view nếu cần
      const cellSelector = `[data-date="${focusedDate.toISOString().split('T')[0]}"]`;
      const cellElement = calendarRef.current.querySelector(cellSelector) as HTMLElement;
      
      if (cellElement) {
        // Scroll vào view nếu cần
        cellElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest'
        });
      }
    }
  }, [focusedDate]);

  return {
    handleKeyDown,
    calendarRef,
    focusCalendar,
  };
};

export default useCalendarKeyboard;
