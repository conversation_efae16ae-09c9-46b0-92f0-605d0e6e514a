import React from 'react';

interface IconProps {
  className?: string; // className là tùy chọn và có kiểu string
}

export type SocialIconName = keyof typeof SocialIcons;

export const SocialIcons: Record<string, React.FC<IconProps>> = {
  facebook: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96C15.9 21.59 18.03 20.44 19.62 18.73C21.2 17.03 22.1 14.86 22 12.62C21.9 10.37 20.83 8.27 19.13 6.69C17.42 5.12 15.16 4.2 12.9 4.08C10.64 3.97 8.32 4.66 6.47 6.05C4.62 7.43 3.34 9.4 2.82 11.61C2.31 13.82 2.6 16.14 3.64 18.17C4.68 20.2 6.41 21.79 8.5 22.64C10.59 23.5 12.93 23.57 15.08 22.84C17.23 22.11 19.04 20.63 20.18 18.67C21.32 16.71 21.73 14.41 21.33 12.18C20.93 9.94 19.74 7.91 18 6.5" />
    </svg>
  ),
  function: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M12.49 10.2722H14.22C14.3659 10.2722 14.5051 10.3333 14.6076 10.4419C14.7101 10.5505 14.7675 10.6977 14.7675 10.8519C14.7675 11.0061 14.7101 11.1533 14.6076 11.2619C14.5051 11.3705 14.3659 11.4316 14.22 11.4316H12.49C12.3441 11.4316 12.2049 11.3705 12.1024 11.2619C11.9999 11.1533 11.9425 11.0061 11.9425 10.8519C11.9425 10.6977 11.9999 10.5505 12.1024 10.4419C12.2049 10.3333 12.3441 10.2722 12.49 10.2722ZM19.01 7.84778C20.6503 9.58921 21.5788 11.9148 21.6 14.3316C21.6 18.7316 17.29 22.2316 12 22.2316C6.71 22.2316 2.4 18.7316 2.4 14.3316C2.4 9.93158 6.71 6.43158 12 6.43158C12.3 6.43158 12.59 6.44158 12.88 6.46158C13.04 6.47158 13.19 6.48158 13.34 6.50158C13.4043 6.50368 13.4679 6.51678 13.5279 6.54025C13.5879 6.56372 13.6433 6.59716 13.6914 6.63919C13.7395 6.68122 13.7795 6.73112 13.8095 6.78644C13.8396 6.84177 13.8592 6.90161 13.8675 6.96358C13.8758 7.02555 13.8726 7.08856 13.8582 7.14923C13.8438 7.2099 13.8184 7.26712 13.7833 7.31769C13.7483 7.36826 13.7042 7.41125 13.6536 7.44446C13.603 7.47767 13.5468 7.50053 13.4883 7.51158C13.3483 7.53158 13.2083 7.55158 13.0683 7.56158C12.7083 7.59158 12.3483 7.60158 12.0083 7.60158C7.41825 7.60158 3.70825 10.6316 3.70825 14.3316C3.70825 18.0316 7.41825 21.0616 12.0083 21.0616C16.5983 21.0616 20.3083 18.0316 20.3083 14.3316C20.2865 12.3223 19.5242 10.3932 18.1683 8.91158C18.1214 8.85788 18.0865 8.79592 18.0656 8.72933C18.0447 8.66274 18.0383 8.59284 18.0468 8.52379C18.0553 8.45474 18.0784 8.38823 18.1148 8.32833C18.1512 8.26844 18.2 8.21643 18.2583 8.17558C18.3166 8.13474 18.3831 8.10587 18.4535 8.09084C18.5239 8.07581 18.5967 8.07494 18.6675 8.08828C18.7383 8.10162 18.8056 8.12891 18.8651 8.16839C18.9246 8.20786 18.9749 8.25868 19.01 8.31778V7.84778ZM15.5 13.4316C15.6459 13.4316 15.7851 13.4927 15.8876 13.6013C15.9901 13.7099 16.0475 13.8571 16.0475 14.0113C16.0475 14.1655 15.9901 14.3127 15.8876 14.4213C15.7851 14.5299 15.6459 14.591 15.5 14.591H12.49C12.3441 14.591 12.2049 14.5299 12.1024 14.4213C11.9999 14.3127 11.9425 14.1655 11.9425 14.0113C11.9425 13.8571 11.9999 13.7099 12.1024 13.6013C12.2049 13.4927 12.3441 13.4316 12.49 13.4316H15.5ZM9.5 10.2722C9.64591 10.2722 9.78508 10.3333 9.88759 10.4419C9.99011 10.5505 10.0475 10.6977 10.0475 10.8519V14.0113C10.0475 14.1655 9.99011 14.3127 9.88759 14.4213C9.78508 14.5299 9.64591 14.591 9.5 14.591C9.35409 14.591 9.21492 14.5299 9.11241 14.4213C9.00989 14.3127 8.9525 14.1655 8.9525 14.0113V10.8519C8.9525 10.6977 9.00989 10.5505 9.11241 10.4419C9.21492 10.3333 9.35409 10.2722 9.5 10.2722ZM7.5 10.2722C7.64591 10.2722 7.78508 10.3333 7.88759 10.4419C7.99011 10.5505 8.0475 10.6977 8.0475 10.8519V14.0113C8.0475 14.1655 7.99011 14.3127 7.88759 14.4213C7.78508 14.5299 7.64591 14.591 7.5 14.591C7.35409 14.591 7.21492 14.5299 7.11241 14.4213C7.00989 14.3127 6.9525 14.1655 6.9525 14.0113V10.8519C6.9525 10.6977 7.00989 10.5505 7.11241 10.4419C7.21492 10.3333 7.35409 10.2722 7.5 10.2722ZM9.5 16.9316C9.64591 16.9316 9.78508 16.9927 9.88759 17.1013C9.99011 17.2099 10.0475 17.3571 10.0475 17.5113C10.0475 17.6655 9.99011 17.8127 9.88759 17.9213C9.78508 18.0299 9.64591 18.091 9.5 18.091H7.5C7.35409 18.091 7.21492 18.0299 7.11241 17.9213C7.00989 17.8127 6.9525 17.6655 6.9525 17.5113C6.9525 17.3571 7.00989 17.2099 7.11241 17.1013C7.21492 16.9927 7.35409 16.9316 7.5 16.9316H9.5Z" />
    </svg>
  ),
  threads: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M12.186 24h-.007c-3.581-.024-5.329-1.576-5.729-5.069l1.307-.118c.314 2.727 1.365 3.854 4.426 3.887 3.098.034 4.294-1.189 4.64-4.75.13-1.345.13-2.78.13-4.35h1.3c0 1.595 0 3.063-.138 4.446-.396 4.113-2.052 5.954-5.929 5.954zm-5.327-14.021c-.194-2.357.568-4.193 2.147-5.169 1.316-.812 3.02-.932 5.116-.363l-.431 1.234c-1.69-.467-3.092-.384-4.073.245-1.085.67-1.584 1.982-1.426 3.765l-1.333.288zm9.847 1.828c-.703-1.061-1.716-1.837-2.87-2.193-1.339-.413-2.686-.404-4.11.03l-.406-1.24c1.734-.533 3.42-.536 5.068-.017 1.513.477 2.832 1.485 3.725 2.85l-1.407.57z" />
    </svg>
  ),
  instagram: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M12 2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153.509.5.902 1.105 1.153 1.772.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 01-1.153 1.772c-.5.508-1.105.902-1.772 1.153-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 01-1.772-1.153 4.904 4.904 0 01-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 011.153-1.772A4.897 4.897 0 015.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2zm0 1.802c-2.67 0-2.986.01-4.04.059-.976.045-1.505.207-1.858.344-.466.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.055-.059 1.37-.059 4.04 0 2.67.01 2.986.059 4.04.045.976.207 1.505.344 1.858.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.047 1.37.059 4.04.059 2.67 0 2.987-.01 4.04-.059.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.047-1.055.059-1.37.059-4.04 0-2.67-.01-2.986-.059-4.04-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.055-.047-1.37-.059-4.04-.059zm0 3.063a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 8.468a3.333 3.333 0 100-6.666 3.333 3.333 0 000 6.666zm6.538-8.469a1.2 1.2 0 11-2.4 0 1.2 1.2 0 012.4 0z" />
    </svg>
  ),
  linkedin: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M19 3a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h14m-.5 15.5v-5.3a3.26 3.26 0 00-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 011.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 001.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 00-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z" />
    </svg>
  ),
  tiktok: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M16.6 5.82s.51.5 0 0A4.278 4.278 0 0020.34 9h-2.67v-.01c-.87 0-1.56-.74-1.56-1.64 0-.99.76-1.79 1.7-1.79.94 0 1.7.8 1.7 1.79 0 .99-.76 1.79-1.7 1.79h.01c-.93 0-1.69-.8-1.69-1.79s.75-1.79 1.68-1.79c.93 0 1.68.8 1.68 1.79v.01c.93 0 1.69-.8 1.69-1.79s-.76-1.79-1.69-1.79zM20.34 9v6.63c0 2.6-2.15 4.71-4.82 4.71-2.66 0-4.82-2.11-4.82-4.71V10.1c0-1.31 1.08-2.37 2.42-2.37 1.33 0 2.42 1.06 2.42 2.37V15.7c0 1.31-1.09 2.37-2.42 2.37-1.34 0-2.42-1.06-2.42-2.37v-1.05h-2.74v1.05c0 2.43 1.99 4.41 4.45 4.41 2.45 0 4.44-1.98 4.44-4.41V10.1c0-2.43-1.99-4.41-4.44-4.41-2.46 0-4.45 1.98-4.45 4.41v6.63c0 3.55 2.9 6.44 6.47 6.44s6.47-2.89 6.47-6.44V9h-2.74z" />
    </svg>
  ),
  telegram: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.64 12c-.88-.25-.89-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15 1.3l-2.72 12.81c-.19.91-.74 1.13-1.5.71L12.6 16.3l-1.99 1.93c-.23.23-.42.42-.83.42z" />
    </svg>
  ),
  website: ({ className }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
    >
      <path d="M16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2m-5.15 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56M14.34 14H9.66c-.1-.66-.16-1.32-.16-2 0-.68.06-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2M12 19.96c-.83-1.2-1.5-2.53-1.91-3.96h3.82c-.41 1.43-1.08 2.76-1.91 3.96M8 8H5.08A7.923 7.923 0 019.4 4.44C8.8 5.55 8.35 6.75 8 8m-2.92 8H8c.35 1.25.8 2.45 1.4 3.56A8.008 8.008 0 015.08 16m-.82-2C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2M12 4.03c.83 1.2 1.5 2.54 1.91 3.97h-3.82c.41-1.43 1.08-2.77 1.91-3.97M18.92 8h-2.95a15.65 15.65 0 00-1.38-3.56c1.84.63 3.37 1.9 4.33 3.56M12 2C6.47 2 2 6.5 2 12a10 10 0 0010 10 10 10 0 0010-10A10 10 0 0012 2z" />
    </svg>
  ),
};
