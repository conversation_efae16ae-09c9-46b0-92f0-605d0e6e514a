import React, { useState, useEffect, useRef } from 'react';

export interface TabItem {
  /**
   * Khóa duy nhất của tab
   */
  key: string;

  /**
   * Tiêu đề của tab
   */
  label: React.ReactNode;

  /**
   * Nội dung của tab
   */
  children: React.ReactNode;

  /**
   * Trạng thái disabled của tab
   */
  disabled?: boolean;

  /**
   * Icon hiển thị bên trái label
   */
  icon?: React.ReactNode;

  /**
   * Badge hiển thị bên phải label
   */
  badge?: string | number;

  /**
   * Màu của badge
   */
  badgeColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

export interface TabsProps {
  /**
   * Danh sách các tab
   */
  items: TabItem[];

  /**
   * Key của tab được chọn mặc định
   */
  defaultActiveKey?: string;

  /**
   * Key của tab đang được chọn (controlled component)
   */
  activeKey?: string;

  /**
   * Callback khi thay đổi tab
   */
  onChange?: (activeKey: string) => void;

  /**
   * <PERSON><PERSON><PERSON> hiển thị của tabs
   */
  type?: 'default' | 'card' | 'underline' | 'pills' | 'segmented' | 'minimal' | 'bordered' | 'vertical-card' | 'icon-only';

  /**
   * Kích thước của tabs
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Vị trí của tabs
   */
  position?: 'top' | 'bottom' | 'left' | 'right';

  /**
   * Căn chỉnh các tab
   */
  alignment?: 'start' | 'center' | 'end' | 'stretch';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Class bổ sung cho tab content
   */
  contentClassName?: string;
}

/**
 * Tabs component hiển thị nội dung theo tab
 *
 * @example
 * // Tabs cơ bản
 * <Tabs
 *   items={[
 *     { key: '1', label: 'Tab 1', children: <div>Content 1</div> },
 *     { key: '2', label: 'Tab 2', children: <div>Content 2</div> },
 *   ]}
 * />
 *
 * @example
 * // Tabs với kiểu card
 * <Tabs
 *   type="card"
 *   items={[
 *     { key: '1', label: 'Tab 1', children: <div>Content 1</div> },
 *     { key: '2', label: 'Tab 2', children: <div>Content 2</div> },
 *   ]}
 * />
 */
const Tabs: React.FC<TabsProps> = ({
  items,
  defaultActiveKey,
  activeKey: propActiveKey,
  onChange,
  type = 'default',
  size = 'md',
  position = 'top',
  alignment = 'start',
  className = '',
  contentClassName = '',
}) => {
  // State để lưu trữ tab đang active
  const [activeKey, setActiveKey] = useState<string>(
    propActiveKey || defaultActiveKey || (items.length > 0 ? items[0].key : '')
  );

  // Ref cho thanh indicator
  const tabsRef = useRef<HTMLDivElement>(null);
  const [indicatorStyle, setIndicatorStyle] = useState<React.CSSProperties>({});

  // Cập nhật activeKey khi prop thay đổi
  useEffect(() => {
    if (propActiveKey !== undefined && propActiveKey !== activeKey) {
      setActiveKey(propActiveKey);
    }
  }, [propActiveKey, activeKey]);

  // Xử lý khi click vào tab
  const handleTabClick = (key: string, disabled?: boolean) => {
    if (disabled) return;

    if (propActiveKey === undefined) {
      setActiveKey(key);
    }

    if (onChange) {
      onChange(key);
    }
  };

  // Cập nhật vị trí của indicator
  useEffect(() => {
    if (tabsRef.current && type === 'underline') {
      const tabElements = tabsRef.current.querySelectorAll('[role="tab"]');
      const activeTabIndex = Array.from(tabElements).findIndex(
        tab => tab.getAttribute('data-key') === activeKey
      );

      if (activeTabIndex !== -1) {
        const activeTab = tabElements[activeTabIndex] as HTMLElement;
        const tabsRect = tabsRef.current.getBoundingClientRect();
        const activeTabRect = activeTab.getBoundingClientRect();

        // Tính toán vị trí và độ rộng của indicator
        const left = activeTabRect.left - tabsRect.left;
        const width = activeTabRect.width;

        setIndicatorStyle({
          left: `${left}px`,
          width: `${width}px`,
          transition: 'all 0.3s ease',
        });
      }
    }
  }, [activeKey, type, items]);

  // Size classes
  const sizeClasses = {
    sm: 'text-xs py-1 px-2',
    md: 'text-sm py-2 px-3',
    lg: 'text-base py-2.5 px-4',
  };

  // Type classes
  const typeClasses = {
    default: 'border-b border-gray-200 dark:border-gray-700',
    card: 'space-x-1',
    underline: 'border-b border-gray-200 dark:border-gray-700',
    pills: 'space-x-1',
    segmented: 'bg-gray-100 dark:bg-gray-800 p-1 rounded-lg',
    minimal: '',
    bordered: 'border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden',
    'vertical-card': 'space-y-1',
    'icon-only': 'space-x-2',
  };

  // Tab item classes based on type
  const getTabItemClasses = (isActive: boolean, disabled?: boolean) => {
    const baseClasses = `${sizeClasses[size]} font-medium transition-all duration-200 cursor-pointer`;
    const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

    const typeSpecificClasses = {
      default: `${
        isActive
          ? 'text-primary border-b-2 border-primary -mb-px'
          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent -mb-px'
      }`,
      card: `${
        isActive
          ? 'bg-white dark:bg-dark-light text-primary shadow-sm rounded-t-lg border-t border-l border-r border-gray-200 dark:border-gray-700'
          : 'bg-gray-100 dark:bg-dark-lighter text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-dark rounded-t-lg'
      }`,
      underline: `${
        isActive
          ? 'text-primary'
          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
      }`,
      pills: `${
        isActive
          ? 'bg-primary text-white rounded-full'
          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-lighter rounded-full'
      }`,
      segmented: `${
        isActive
          ? 'bg-white dark:bg-gray-700 text-primary shadow-sm rounded-md'
          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-md'
      }`,
      minimal: `${
        isActive
          ? 'text-primary font-semibold'
          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
      }`,
      bordered: `${
        isActive
          ? 'bg-primary text-white border-r border-gray-200 dark:border-gray-700 last:border-r-0'
          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 border-r border-gray-200 dark:border-gray-700 last:border-r-0'
      }`,
      'vertical-card': `${
        isActive
          ? 'bg-primary text-white shadow-sm rounded-lg'
          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg'
      }`,
      'icon-only': `${
        isActive
          ? 'bg-primary text-white rounded-lg p-3'
          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg p-3'
      }`,
    };

    return `${baseClasses} ${typeSpecificClasses[type]} ${disabledClasses}`;
  };

  // Alignment classes
  const alignmentClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    stretch: 'justify-between',
  };

  // Position classes
  const isVertical = position === 'left' || position === 'right';
  const tabsContainerClasses = isVertical
    ? `flex ${position === 'left' ? 'flex-row' : 'flex-row-reverse'}`
    : `flex flex-col ${position === 'top' ? '' : 'flex-col-reverse'}`;

  const tabsListClasses = isVertical
    ? `flex flex-col ${typeClasses[type]} ${alignmentClasses['start']}`
    : `flex flex-row ${typeClasses[type]} ${alignmentClasses[alignment]}`;

  // Render active tab content
  const activeTabContent = items.find(item => item.key === activeKey)?.children;

  return (
    <div className={`tabs ${tabsContainerClasses} ${className}`}>
      {/* Tab list */}
      <div ref={tabsRef} className={`tabs-nav relative ${tabsListClasses}`} role="tablist">
        {items.map(item => {
          // Badge color classes
          const badgeColorClasses = {
            primary: 'bg-primary text-white',
            secondary: 'bg-gray-500 text-white',
            success: 'bg-green-500 text-white',
            warning: 'bg-yellow-500 text-white',
            danger: 'bg-red-500 text-white',
          };

          const badgeColor = item.badgeColor || 'primary';

          return (
            <div
              key={item.key}
              className={getTabItemClasses(item.key === activeKey, item.disabled)}
              onClick={() => handleTabClick(item.key, item.disabled)}
              role="tab"
              aria-selected={item.key === activeKey}
              tabIndex={item.disabled ? -1 : 0}
              data-key={item.key}
              title={type === 'icon-only' ? String(item.label) : undefined}
            >
              <span className="flex items-center">
                {item.icon && <span className={type === 'icon-only' ? '' : 'mr-2'}>{item.icon}</span>}
                {type !== 'icon-only' && item.label}
                {item.badge && (
                  <span
                    className={`ml-2 px-1.5 py-0.5 text-xs rounded-full ${badgeColorClasses[badgeColor]}`}
                  >
                    {item.badge}
                  </span>
                )}
              </span>
            </div>
          );
        })}

        {/* Indicator for underline type */}
        {type === 'underline' && (
          <div
            className="absolute bottom-0 h-0.5 bg-primary transition-all duration-300"
            style={indicatorStyle}
          />
        )}
      </div>

      {/* Tab content */}
      <div className={`tabs-content p-4 flex-1 ${contentClassName}`}>{activeTabContent}</div>
    </div>
  );
};

export default Tabs;
