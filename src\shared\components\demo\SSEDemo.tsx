/**
 * Demo component để test các tiện ích SSE
 */
import React, { useState, useCallback } from 'react';
import {
  useSSE,
  useSSESubscription,
  useSSEConnection,
} from '@/shared/hooks/common';
import {
  SSEStatus,
  SSENotification,
  Button,
  Card,
  Badge,
  Alert,
} from '@/shared/components/common';
import { SSEProvider } from '@/shared/contexts/sse';
import type { SSENotificationItem } from '@/shared/components/common/SSENotification';

/**
 * Demo cho useSSE hook
 */
const BasicSSEDemo: React.FC = () => {
  const sse = useSSE('/api/v1/sse/demo', {
    autoConnect: false,
    debug: true,
  });

  const [subscriptions, setSubscriptions] = useState<string[]>([]);

  const handleSubscribe = useCallback((eventType: string) => {
    const id = sse.subscribe(eventType, (event) => {
      console.log(`Event ${eventType}:`, event);
    });
    setSubscriptions(prev => [...prev, id]);
  }, [sse]);

  const handleUnsubscribeAll = useCallback(() => {
    subscriptions.forEach(id => sse.unsubscribe(id));
    setSubscriptions([]);
  }, [sse, subscriptions]);

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">Basic SSE Demo</h3>

      <div className="space-y-4">
        <SSEStatus
          connectionInfo={sse.connectionInfo}
          variant="card"
          showDetails={true}
        />

        <div className="flex gap-2">
          <Button onClick={sse.connect} variant="primary" size="sm">
            Connect
          </Button>
          <Button onClick={sse.disconnect} variant="secondary" size="sm">
            Disconnect
          </Button>
          <Button onClick={sse.clearEvents} variant="outline" size="sm">
            Clear Events
          </Button>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={() => handleSubscribe('test')}
            variant="outline"
            size="sm"
          >
            Subscribe to 'test'
          </Button>
          <Button
            onClick={() => handleSubscribe('notification')}
            variant="outline"
            size="sm"
          >
            Subscribe to 'notification'
          </Button>
          <Button
            onClick={handleUnsubscribeAll}
            variant="danger"
            size="sm"
          >
            Unsubscribe All
          </Button>
        </div>

        <div>
          <h4 className="font-medium mb-2">
            Recent Events ({sse.events.length})
          </h4>
          <div className="max-h-40 overflow-y-auto space-y-1">
            {sse.events.slice(-10).map((event, index) => (
              <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                <Badge variant="info" className="mr-2">
                  {event.type}
                </Badge>
                {JSON.stringify(event.data)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

/**
 * Demo cho useSSESubscription hook
 */
const SubscriptionDemo: React.FC = () => {
  const subscription = useSSESubscription(
    '/api/v1/sse/demo',
    'notification',
    (event) => {
      console.log('Notification received:', event);
    },
    {
      autoSubscribe: false,
      maxEvents: 20,
      filter: {
        type: 'notification',
      },
    }
  );

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">Subscription Demo</h3>

      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <SSEStatus
            connectionInfo={subscription.connectionInfo}
            variant="inline"
          />
          <Badge variant={subscription.isSubscribed ? 'success' : 'info'}>
            {subscription.isSubscribed ? 'Subscribed' : 'Not Subscribed'}
          </Badge>
        </div>

        <div className="flex gap-2">
          <Button onClick={subscription.connect} variant="primary" size="sm">
            Connect
          </Button>
          <Button onClick={subscription.subscribe} variant="outline" size="sm">
            Subscribe
          </Button>
          <Button onClick={subscription.unsubscribe} variant="outline" size="sm">
            Unsubscribe
          </Button>
        </div>

        <div>
          <h4 className="font-medium mb-2">
            Notifications ({subscription.eventCount})
          </h4>
          {subscription.lastEvent && (
            <Alert
              type="info"
              message={
                <span>
                  <strong>Latest:</strong> {JSON.stringify(subscription.lastEvent.data)}
                </span>
              }
              className="mb-2"
            />
          )}
          <div className="max-h-32 overflow-y-auto space-y-1">
            {subscription.events.map((event, index) => (
              <div key={index} className="text-sm p-2 bg-blue-50 rounded">
                {new Date(event.timestamp).toLocaleTimeString()}: {JSON.stringify(event.data)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

/**
 * Demo cho useSSEConnection hook
 */
const ConnectionDemo: React.FC = () => {
  const connection = useSSEConnection('/api/v1/sse/demo', {
    trackMetrics: true,
    autoRetry: true,
    maxRetries: 3,
    onStateChange: (state) => {
      console.log('Connection state changed:', state);
    },
  });

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">Connection Demo</h3>

      <div className="space-y-4">
        <SSEStatus
          connectionInfo={connection.connectionInfo}
          metrics={connection.metrics}
          variant="card"
          showDetails={true}
          showMetrics={true}
        />

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Is Connecting:</span> {connection.isConnecting ? 'Yes' : 'No'}
          </div>
          <div>
            <span className="font-medium">Is Connected:</span> {connection.isConnected ? 'Yes' : 'No'}
          </div>
          <div>
            <span className="font-medium">Is Reconnecting:</span> {connection.isReconnecting ? 'Yes' : 'No'}
          </div>
          <div>
            <span className="font-medium">Has Error:</span> {connection.hasError ? 'Yes' : 'No'}
          </div>
          <div>
            <span className="font-medium">Uptime:</span> {Math.floor(connection.getUptime() / 1000)}s
          </div>
          <div>
            <span className="font-medium">Health:</span> {connection.checkHealth() ? 'Good' : 'Poor'}
          </div>
        </div>

        <div className="flex gap-2">
          <Button onClick={connection.connect} variant="primary" size="sm">
            Connect
          </Button>
          <Button onClick={connection.disconnect} variant="secondary" size="sm">
            Disconnect
          </Button>
          <Button onClick={connection.retry} variant="outline" size="sm">
            Retry
          </Button>
          <Button onClick={connection.reset} variant="danger" size="sm">
            Reset
          </Button>
        </div>

        {connection.lastError && (
          <Alert
            type="error"
            message={
              <span>
                <strong>Error:</strong> {connection.lastError.message}
              </span>
            }
          />
        )}
      </div>
    </Card>
  );
};

/**
 * Demo cho SSE Notifications
 */
const NotificationDemo: React.FC = () => {
  const [notifications, setNotifications] = useState<SSENotificationItem[]>([]);

  const sse = useSSE('/api/v1/sse/demo', {
    autoConnect: false,
  });

  React.useEffect(() => {
    const subscriptionId = sse.subscribe('notification', (event) => {
      // Type assertion for event.data
      const data = event.data as {
        type?: string;
        title?: string;
        message?: string;
        autoHide?: boolean;
        hideAfter?: number;
        actions?: Array<{ label: string; onClick: () => void; variant?: 'primary' | 'secondary' }>;
      };

      const notification: SSENotificationItem = {
        id: event.id || Date.now().toString(),
        type: (data.type as 'info' | 'success' | 'warning' | 'error') || 'info',
        title: data.title || 'Notification',
        message: data.message,
        event,
        timestamp: new Date(),
        autoHide: data.autoHide !== false,
        hideAfter: data.hideAfter || 5000,
        actions: data.actions,
      };

      setNotifications(prev => [...prev, notification]);
    });

    return () => sse.unsubscribe(subscriptionId);
  }, [sse]);

  const handleDismiss = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const handleClearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const addTestNotification = useCallback((type: 'info' | 'success' | 'warning' | 'error') => {
    const notification: SSENotificationItem = {
      id: Date.now().toString(),
      type,
      title: `Test ${type} notification`,
      message: `This is a test ${type} notification message.`,
      event: {
        type: 'notification',
        data: { type, title: `Test ${type}`, message: `Test message` },
        timestamp: Date.now(),
      },
      timestamp: new Date(),
      autoHide: true,
      hideAfter: 5000,
    };

    setNotifications(prev => [...prev, notification]);
  }, []);

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">Notification Demo</h3>

      <div className="space-y-4">
        <SSEStatus
          connectionInfo={sse.connectionInfo}
          variant="badge"
        />

        <div className="flex gap-2">
          <Button onClick={sse.connect} variant="primary" size="sm">
            Connect SSE
          </Button>
          <Button onClick={() => addTestNotification('info')} variant="outline" size="sm">
            Test Info
          </Button>
          <Button onClick={() => addTestNotification('success')} variant="outline" size="sm">
            Test Success
          </Button>
          <Button onClick={() => addTestNotification('warning')} variant="outline" size="sm">
            Test Warning
          </Button>
          <Button onClick={() => addTestNotification('error')} variant="outline" size="sm">
            Test Error
          </Button>
        </div>

        <div>
          <p className="text-sm text-gray-600">
            Active notifications: {notifications.length}
          </p>
        </div>
      </div>

      <SSENotification
        notifications={notifications}
        onDismiss={handleDismiss}
        onClearAll={handleClearAll}
        position="top-right"
        maxVisible={5}
        showTimestamp={true}
        showClearAll={true}
      />
    </Card>
  );
};

/**
 * Main SSE Demo component
 */
const SSEDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('basic');

  const tabs = [
    { id: 'basic', label: 'Basic SSE', component: BasicSSEDemo },
    { id: 'subscription', label: 'Subscription', component: SubscriptionDemo },
    { id: 'connection', label: 'Connection', component: ConnectionDemo },
    { id: 'notification', label: 'Notifications', component: NotificationDemo },
  ];

  return (
    <SSEProvider defaultOptions={{ debug: true }}>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">SSE Utilities Demo</h1>

        <div className="flex space-x-1 mb-6">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {tabs.map(tab => (
          <div
            key={tab.id}
            className={activeTab === tab.id ? 'block' : 'hidden'}
          >
            <tab.component />
          </div>
        ))}
      </div>
    </SSEProvider>
  );
};

export default SSEDemo;
