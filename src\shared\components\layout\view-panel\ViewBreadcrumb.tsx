import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Breadcrumb, Icon } from '@/shared/components/common';
import type { BreadcrumbItem } from '@/shared/components/common/Breadcrumb/Breadcrumb';

interface ViewBreadcrumbProps {
  title: string;
  className?: string;
}

const ViewBreadcrumb: React.FC<ViewBreadcrumbProps> = ({ title, className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation(['common', 'marketing', 'admin', 'marketplace', 'components', 'data', 'business']);

  // Tạo breadcrumb items dựa trên đường dẫn hiện tại
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    // Luôn có item Home (hiển thị icon và text "Trang chủ")
    const items: BreadcrumbItem[] = [
      {
        label: t('common:home', 'Trang chủ'),
        path: '/',
        icon: <Icon name="home" size="sm" />,
        onClick: () => navigate('/'),
      },
    ];

    // Nếu không phải trang chủ, thêm trang hiện tại
    if (location.pathname !== '/') {
      // Xử lý các trường hợp đặc biệt
      if (location.pathname.startsWith('/components')) {
        // Xử lý các trang components
        if (location.pathname === '/components') {
          // Trang components chính
          items.push({
            label: t('components:library.title', 'Thư viện Components'),
          });
        } else {
          // Các trang con của components
          items.push({
            label: t('components:library.title', 'Thư viện Components'),
            path: '/components',
            onClick: () => navigate('/components'),
          });

          // Thêm trang hiện tại
          {
            // Kiểm tra xem title có phải là key translation không
            const isTranslationKey = title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title,
            });
          }
        }
      } else {
        // Xử lý các trang khác
        switch (location.pathname) {
          case '/animation-demo':
            items.push({
              label: t('common:animation', 'Hiệu ứng'),
            });
            break;
          case '/responsive-demo':
            items.push({
              label: t('common:componentsText', 'Components'),
            });
            break;
          case '/ai-agents':
            items.push({
              label: t('chat:aiAgents', 'AI Agents'),
            });
            break;

          // Marketplace module
          case '/marketplace':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            break;
          case '/marketplace/cart':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:cart.title', 'Giỏ hàng'),
            });
            break;
          case location.pathname.startsWith('/marketplace/product/') ? location.pathname : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:product.detail.information', 'Thông tin sản phẩm'),
            });
            break;
          case location.pathname.startsWith('/marketplace/category/') ? location.pathname : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:categories', 'Danh mục'),
            });
            break;
          case location.pathname.startsWith('/business/products/edit/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product.title', 'Sản phẩm'),
              path: '/business/product',
              onClick: () => navigate('/business/product'),
            });
            items.push({
              label: t('business:product.form.editTitle', 'Chỉnh sửa sản phẩm'),
            });
            break;

          // Marketing module
          case '/marketing':
            items.push({
              label: t('marketing:title', 'Marketing'),
            });
            break;
          case '/marketing/dashboard':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:dashboard.title', 'Marketing Dashboard'),
            });
            break;
          case '/marketing/audience':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.audience', 'Đối tượng'),
            });
            break;

          // Business module
          case '/business':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
            });
            break;
          case '/business/product':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product.title', 'Sản phẩm'),
            });
            break;
          case '/business/customer':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customer.title', 'Khách hàng'),
            });
            break;
          case '/business/order':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:order.title', 'Đơn hàng'),
            });
            break;
          case '/business/conversion':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:conversion.title', 'Chuyển đổi'),
            });
            break;
          case '/business/report':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:report.title', 'Báo cáo'),
            });
            break;
          case '/business/inventory':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:inventory.title', 'Quản lý kho'),
            });
            break;
          case '/business/custom-field':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customField.title', 'Trường tùy chỉnh'),
            });
            break;
          case '/business/products/create':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product.title', 'Sản phẩm'),
              path: '/business/product',
              onClick: () => navigate('/business/product'),
            });
            items.push({
              label: t('business:product.form.createTitle', 'Tạo sản phẩm'),
            });
            break;

          // Data module
          case '/data':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
            });
            break;
          case '/data/url':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:url.title', 'Quản lý URL'),
            });
            break;
          case '/data/files':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:files.title', 'Quản lý File'),
            });
            break;
          case '/data/knowledge-files':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:knowledgeFiles.title', 'Knowledge Files'),
            });
            break;
          case '/data/vector-store':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:vectorStore.title', 'Vector Store'),
            });
            break;

          // Admin Data module
          case '/admin/data':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
            });
            break;
          case '/admin/data/media':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.media.title', 'Quản lý Media'),
            });
            break;
          case '/admin/data/url':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.url.title', 'Quản lý URL'),
            });
            break;
          case '/admin/data/knowledge-files':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.knowledgeFiles.title', 'Quản lý Knowledge Files'),
            });
            break;
          case '/admin/data/vector-store':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.vectorStore.title', 'Quản lý Vector Store'),
            });
            break;
          case '/marketing/segment':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.segment', 'Phân đoạn'),
            });
            break;
          case '/marketing/campaign':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.campaign', 'Chiến dịch'),
            });
            break;
          case '/marketing/tags':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.tags', 'Thẻ'),
            });
            break;
          case '/marketing/custom-fields':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.customFields', 'Trường tùy chỉnh'),
            });
            break;
          case '/marketing/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.reports', 'Báo cáo'),
            });
            break;
          case '/marketing/template-emails':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:templateEmail.title', 'Quản lý mẫu email'),
            });
            break;
          case '/marketing/sms':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:smsMarketing.title', 'SMS Marketing'),
            });
            break;
          case '/marketing/google-ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
            });
            break;
          case '/marketing/google-ads/detail':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.detail.title', 'Chi tiết Google Ads'),
            });
            break;
          case '/marketing/google-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.accounts.title', 'Tài khoản Google Ads'),
            });
            break;
          case '/marketing/google-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.campaigns.title', 'Chiến dịch Google Ads'),
            });
            break;
          case '/marketing/facebook-ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
            });
            break;
          case '/marketing/zalo':
          case '/marketing/zalo/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            break;
          case '/marketing/zalo/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
              path: '/marketing/zalo',
              onClick: () => navigate('/marketing/zalo'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
            });
            break;
          case location.pathname.match(/^\/marketing\/zalo\/accounts\/[^/]+\/followers$/) ? location.pathname : '':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
              path: '/marketing/zalo',
              onClick: () => navigate('/marketing/zalo'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
              path: '/marketing/zalo/accounts',
              onClick: () => navigate('/marketing/zalo/accounts'),
            });
            items.push({
              label: t('marketing:zalo.followers.title', 'Quản lý Followers'),
            });
            break;
          case '/marketing/zalo/zns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
              path: '/marketing/zalo',
              onClick: () => navigate('/marketing/zalo'),
            });
            items.push({
              label: t('marketing:zalo.zns.title', 'ZNS Templates'),
            });
            break;
          case '/marketing/email':
          case '/marketing/email/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
            });
            break;
          case '/marketing/email/templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.templates.title', 'Email Templates'),
            });
            break;
          case '/marketing/email/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.campaigns.title', 'Email Campaigns'),
            });
            break;
          case '/marketing/email/analytics':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.analytics.title', 'Analytics'),
            });
            break;
          case '/marketing/zalo-ads':
          case '/marketing/zalo-ads/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
            });
            break;

          default: {
            // Nếu không có xử lý đặc biệt, sử dụng title được truyền vào
            // Kiểm tra xem title có phải là key translation không
            const isTranslationKey = title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title,
            });
          }
        }
      }
    }

    return items;
  };

  return (
    <div className="flex items-center overflow-hidden">
      <Breadcrumb items={generateBreadcrumbItems()} className={`text-sm ${className} truncate`} />
      {/* Hiệu ứng ánh sáng nhẹ */}
      <div className="absolute w-8 h-8 bg-primary opacity-10 rounded-full blur-xl -z-10 animate-pulse-slow"></div>
    </div>
  );
};

export default ViewBreadcrumb;
