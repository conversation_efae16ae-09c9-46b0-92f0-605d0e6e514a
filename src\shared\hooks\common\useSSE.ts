/**
 * Hook chính để sử dụng Server-Sent Events (SSE)
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { SSEClient } from '@/shared/utils/sse-client';
import {
  UseSSEOptions,
  UseSSEReturn,
  SSEEvent,
  SSEConnectionInfo,
  SSEConnectionState
} from '@/shared/types/sse.types';
import { env } from '@/shared/utils';

/**
 * Hook để sử dụng SSE
 */
export function useSSE(url: string, options: UseSSEOptions = {}): UseSSEReturn {
  const {
    autoConnect = true,
    debug = false,
    headers = {},
    autoReconnect = true,
    reconnectDelay = 3000,
    maxReconnectAttempts = 5,
    timeout = 30000,
    withCredentials = false,
    onOpen,
    onError,
    onClose,
    onMessage,
  } = options;

  // State
  const [connectionInfo, setConnectionInfo] = useState<SSEConnectionInfo>({
    state: SSEConnectionState.DISCONNECTED,
    url,
    reconnectAttempts: 0,
    eventsReceived: 0,
  });

  const [events, setEvents] = useState<SSEEvent[]>([]);
  const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null);

  // Refs
  const clientRef = useRef<SSEClient | null>(null);
  const subscriptionsRef = useRef<Map<string, string>>(new Map());

  /**
   * Cập nhật thông tin kết nối
   */
  const updateConnectionInfo = useCallback(() => {
    if (clientRef.current) {
      setConnectionInfo(clientRef.current.getConnectionInfo());
    }
  }, []);

  /**
   * Xử lý message từ SSE
   */
  const handleMessage = useCallback((event: MessageEvent) => {
    let data: unknown;

    try {
      data = JSON.parse(event.data);
    } catch {
      data = event.data;
    }

    const sseEvent: SSEEvent = {
      id: event.lastEventId || undefined,
      type: event.type || 'message',
      data,
      timestamp: Date.now(),
    };

    setEvents(prev => [...prev, sseEvent]);
    setLastEvent(sseEvent);
    updateConnectionInfo();
  }, [updateConnectionInfo]);

  /**
   * Tạo SSE client
   */
  const createClient = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
    }

    // Tạo headers với token nếu cần
    const finalHeaders = { ...headers };

    // Thêm Authorization header nếu có token
    const token = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');
    if (token && !finalHeaders.Authorization) {
      finalHeaders.Authorization = `Bearer ${token}`;
    }

    // Tạo full URL
    const fullUrl = url.startsWith('http') ? url : `${env.apiUrl}${url}`;

    clientRef.current = new SSEClient({
      url: fullUrl,
      headers: finalHeaders,
      autoReconnect,
      reconnectDelay,
      maxReconnectAttempts,
      timeout,
      withCredentials,
      onOpen: (event) => {
        if (debug) console.log('SSE connected:', event);
        updateConnectionInfo();
        onOpen?.(event);
      },
      onError: (error) => {
        if (debug) console.error('SSE error:', error);
        updateConnectionInfo();
        onError?.(error);
      },
      onClose: (event) => {
        if (debug) console.log('SSE closed:', event);
        updateConnectionInfo();
        onClose?.(event);
      },
      onMessage: (event) => {
        if (debug) console.log('SSE message:', event);
        handleMessage(event);
        onMessage?.(event);
      },
    });

    return clientRef.current;
  }, [
    url,
    headers,
    autoReconnect,
    reconnectDelay,
    maxReconnectAttempts,
    timeout,
    withCredentials,
    debug,
    onOpen,
    onError,
    onClose,
    onMessage,
    handleMessage,
    updateConnectionInfo
  ]);



  /**
   * Kết nối SSE
   */
  const connect = useCallback(() => {
    const client = clientRef.current || createClient();
    client.connect();
  }, [createClient]);

  /**
   * Ngắt kết nối SSE
   */
  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
      updateConnectionInfo();
    }
  }, [updateConnectionInfo]);

  /**
   * Subscribe vào một loại event
   */
  const subscribe = useCallback((eventType: string, handler: (event: SSEEvent) => void): string => {
    if (!clientRef.current) {
      createClient();
    }

    const subscriptionId = clientRef.current!.subscribe(eventType, handler);
    subscriptionsRef.current.set(subscriptionId, eventType);

    if (debug) {
      console.log(`Subscribed to event type: ${eventType}, subscription ID: ${subscriptionId}`);
    }

    return subscriptionId;
  }, [createClient, debug]);

  /**
   * Unsubscribe khỏi một subscription
   */
  const unsubscribe = useCallback((subscriptionId: string) => {
    if (clientRef.current) {
      clientRef.current.unsubscribe(subscriptionId);
      const eventType = subscriptionsRef.current.get(subscriptionId);
      subscriptionsRef.current.delete(subscriptionId);

      if (debug && eventType) {
        console.log(`Unsubscribed from event type: ${eventType}, subscription ID: ${subscriptionId}`);
      }
    }
  }, [debug]);

  /**
   * Clear tất cả events
   */
  const clearEvents = useCallback(() => {
    setEvents([]);
    setLastEvent(null);
  }, []);

  /**
   * Gửi message (placeholder - SSE thường chỉ nhận)
   */
  const send = useCallback(() => {
    console.warn('SSE is read-only. Use WebSocket or HTTP POST for sending data.');
    // Có thể implement logic gửi qua HTTP API ở đây
  }, []);

  // Effect để tự động kết nối
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup khi unmount
    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
      }
    };
  }, [autoConnect, connect]);

  // Effect để cập nhật connection info định kỳ
  useEffect(() => {
    const interval = setInterval(updateConnectionInfo, 1000);
    return () => clearInterval(interval);
  }, [updateConnectionInfo]);

  return {
    connectionInfo,
    events,
    lastEvent,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    clearEvents,
    send,
  };
}

export default useSSE;
