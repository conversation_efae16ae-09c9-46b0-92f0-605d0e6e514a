import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Import Components module pages
const AvatarPage = lazy(() => import('@/modules/components/pages/AvatarPage'));
const BannerPage = lazy(() => import('@/modules/components/pages/BannerPage'));
const CustomCardPage = lazy(() => import('@/modules/components/pages/CustomCardPage'));
const ImageGalleryPage = lazy(() => import('@/modules/components/pages/ImageGalleryPage'));
const NotificationPage = lazy(() => import('@/modules/components/pages/NotificationPage'));
const TablePage = lazy(() => import('@/modules/components/pages/TablePage'));
const SearchableSelectPage = lazy(() => import('@/modules/components/pages/SearchableSelectPage'));
const ComponentsPage = lazy(() => import('@/modules/components/pages/ComponentsPage'));
const AnimationPage = lazy(() => import('@/modules/components/pages/AnimationPage'));
const ButtonsPage = lazy(() => import('@/modules/components/pages/ButtonsPage'));
const CardsPage = lazy(() => import('@/modules/components/pages/CardsPage'));
const ChipsPage = lazy(() => import('@/modules/components/pages/ChipsPage'));
const InputsPage = lazy(() => import('@/modules/components/pages/InputsPage'));
const LayoutComponentsPage = lazy(() => import('@/modules/components/pages/LayoutComponentsPage'));
const ThemeComponentsPage = lazy(() => import('@/modules/components/pages/ThemeComponentsPage'));
const ThemeSystemPage = lazy(() => import('@/modules/components/pages/ThemeSystemPage'));
const GridPage = lazy(() => import('@/modules/components/pages/GridPage'));
const TypographyPage = lazy(() => import('@/modules/components/pages/TypographyPage'));
const MenuPage = lazy(() => import('@/modules/components/pages/MenuPage'));
const ModernTooltipPage = lazy(() => import('@/modules/components/pages/ModernTooltipPage'));
const SearchBarPage = lazy(() => import('@/modules/components/pages/SearchBarPage'));
const ModernMenuPage = lazy(() => import('@/modules/components/pages/ModernMenuPage'));
const ResponsiveGridPage = lazy(() => import('@/modules/components/pages/ResponsiveGridPage'));
const LineChartPage = lazy(() => import('@/modules/components/pages/charts/LineChartPage'));
const SimpleChartPage = lazy(() => import('@/modules/components/pages/charts/SimpleChartPage'));
const ChartDemoPage = lazy(() => import('@/modules/components/pages/charts/ChartDemoPage'));
const FormWizardPage = lazy(() => import('@/modules/components/pages/form/wizard/FormWizardPage'));
const TopCardPage = lazy(() => import('@/modules/components/pages/TopCardPage'));
const EmailBuilderPage = lazy(() => import('@/modules/components/pages/EmailBuilderPage'));
const SearchInputWithImagePage = lazy(
  () => import('@/modules/components/pages/SearchInputWithImagePage')
);
const PricingCardsPage = lazy(() => import('@/modules/components/pages/PricingCardsPage'));
const ModuleGalleryPage = lazy(() => import('@/modules/components/pages/ModuleGalleryPage'));
const IconsPage = lazy(() => import('@/modules/components/pages/IconsPage'));
const SSEPage = lazy(() => import('@/modules/components/pages/SSEPage'));
const TabsPage = lazy(() => import('@/modules/components/pages/TabsPage'));
const StepperPage = lazy(() => import('@/modules/components/pages/StepperPage'));

// Import Components demo pages
const LoadingButtonDemo = lazy(() => import('@/modules/components/buttons/LoadingButtonDemo'));
const ThemeFormDemo = lazy(() => import('@/modules/components/pages/form/ThemeFormDemo'));

/**
 * Components module routes
 */
const componentRoutes: RouteObject[] = [
  {
    path: '/components',
    element: (
      <MainLayout title="Components">
        <Suspense fallback={<Loading />}>
          <ComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/animation',
    element: (
      <MainLayout title="Animation">
        <Suspense fallback={<Loading />}>
          <AnimationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/buttons',
    element: (
      <MainLayout title="Buttons">
        <Suspense fallback={<Loading />}>
          <ButtonsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/cards',
    element: (
      <MainLayout title="Cards">
        <Suspense fallback={<Loading />}>
          <CardsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/chips',
    element: (
      <MainLayout title="Chips">
        <Suspense fallback={<Loading />}>
          <ChipsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/inputs',
    element: (
      <MainLayout title="Inputs">
        <Suspense fallback={<Loading />}>
          <InputsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/layout',
    element: (
      <MainLayout title="Layout Components">
        <Suspense fallback={<Loading />}>
          <LayoutComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/theme',
    element: (
      <MainLayout title="Theme Components">
        <Suspense fallback={<Loading />}>
          <ThemeComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/theme-system',
    element: (
      <MainLayout title="Theme System">
        <Suspense fallback={<Loading />}>
          <ThemeSystemPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/grid',
    element: (
      <MainLayout title="Grid System">
        <Suspense fallback={<Loading />}>
          <GridPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/typography',
    element: (
      <MainLayout title="Typography">
        <Suspense fallback={<Loading />}>
          <TypographyPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/menu',
    element: (
      <MainLayout title="Menu">
        <Suspense fallback={<Loading />}>
          <MenuPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/tooltip',
    element: (
      <MainLayout title="Tooltip">
        <Suspense fallback={<Loading />}>
          <ModernTooltipPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/search-bar',
    element: (
      <MainLayout title="Search Bar">
        <Suspense fallback={<Loading />}>
          <SearchBarPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/modern-menu',
    element: (
      <MainLayout title="Modern Menu">
        <Suspense fallback={<Loading />}>
          <ModernMenuPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/responsive-grid',
    element: (
      <MainLayout title="Responsive Grid">
        <Suspense fallback={<Loading />}>
          <ResponsiveGridPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/loading-button-demo',
    element: (
      <MainLayout title="Loading Button Demo">
        <Suspense fallback={<Loading />}>
          <LoadingButtonDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/theme',
    element: (
      <MainLayout title="Form Components with Theme System">
        <Suspense fallback={<Loading />}>
          <ThemeFormDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/custom-card',
    element: (
      <MainLayout title="Form Components with Theme System">
        <Suspense fallback={<Loading />}>
          <CustomCardPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/avatar',
    element: (
      <MainLayout title="Form Components with Avatar System">
        <Suspense fallback={<Loading />}>
          <AvatarPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/banner',
    element: (
      <MainLayout title="Banner Component">
        <Suspense fallback={<Loading />}>
          <BannerPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/table',
    element: (
      <MainLayout title="Table">
        <Suspense fallback={<Loading />}>
          <TablePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/image-gallery',
    element: (
      <MainLayout title="ImageGalleryPage">
        <Suspense fallback={<Loading />}>
          <ImageGalleryPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/charts/line-chart',
    element: (
      <MainLayout title="Line Chart">
        <Suspense fallback={<Loading />}>
          <LineChartPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/wizard',
    element: (
      <MainLayout title="Form Wizard">
        <Suspense fallback={<Loading />}>
          <FormWizardPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/charts/simple-chart',
    element: (
      <MainLayout title="Simple Chart">
        <Suspense fallback={<Loading />}>
          <SimpleChartPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/notification',
    element: (
      <MainLayout title="Notification">
        <Suspense fallback={<Loading />}>
          <NotificationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/searchable-select',
    element: (
      <MainLayout title="Searchable Select">
        <Suspense fallback={<Loading />}>
          <SearchableSelectPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/top-card',
    element: (
      <MainLayout title="TopCard">
        <Suspense fallback={<Loading />}>
          <TopCardPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/search-input-with-image',
    element: (
      <MainLayout title="Search Input With Image">
        <Suspense fallback={<Loading />}>
          <SearchInputWithImagePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/email-builder',
    element: (
      <MainLayout title="Email Builder">
        <Suspense fallback={<Loading />}>
          <EmailBuilderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/charts/demo',
    element: (
      <MainLayout title="Chart Components Demo">
        <Suspense fallback={<Loading />}>
          <ChartDemoPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/pricing-cards',
    element: (
      <MainLayout title="Pricing Cards Showcase">
        <Suspense fallback={<Loading />}>
          <PricingCardsPage />
          </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/module-gallery',
    element: (
      <MainLayout title="Module Gallery">
        <Suspense fallback={<Loading />}>
          <ModuleGalleryPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/icons',
    element: (
      <MainLayout title="Icons">
        <Suspense fallback={<Loading />}>
          <IconsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/sse',
    element: (
      <MainLayout title="SSE (Server-Sent Events)">
        <Suspense fallback={<Loading />}>
          <SSEPage />
          </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/tabs',
    element: (
      <MainLayout title="Tabs Components">
        <Suspense fallback={<Loading />}>
          <TabsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/stepper',
    element: (
      <MainLayout title="Stepper Components">
        <Suspense fallback={<Loading />}>
          <StepperPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default componentRoutes;
